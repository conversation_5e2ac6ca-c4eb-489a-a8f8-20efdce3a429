<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class SiteSetting extends BaseModel
{
    use HasFactory;
    use SoftDeletes;

    /**
     * C<PERSON>c thuộc tính nên được <PERSON>n khi chuyển thành mảng hoặc JSON.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'created_by',
        'updated_by',
    ];
    /**
     * Khóa chính của bảng.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * <PERSON><PERSON><PERSON> thuộc tính có thể gán hàng loạt.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'group_id',
        'name',
        'key',
        'description',
        'value',
        'type',
        'is_private',
        'is_deletable',
        'status',
        'created_by',
        'updated_by',
    ];

    /**
     * Tên bảng tương ứng với model.
     *
     * @var string
     */
    protected $table = 'site_settings';

    /**
     * <PERSON><PERSON><PERSON> các casts dành riêng cho model này.
     *
     * @return array
     */
    protected function getModelCasts(): array
    {
        return [
            'group_id' => 'integer',
            'status' => 'integer',
            'is_private' => 'boolean',
            'is_deletable' => 'boolean',
        ];
    }

    /**
     * Quan hệ với nhóm cài đặt
     *
     * @return BelongsTo
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(SiteSettingGroup::class, 'group_id');
    }

    /**
     * Quan hệ với người tạo
     *
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Quan hệ với người cập nhật
     *
     * @return BelongsTo
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope để lọc theo trạng thái hoạt động
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Scope để lọc theo key
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $key
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByKey($query, string $key)
    {
        return $query->where('key', $key);
    }
}
