<?php

namespace App\Repositories\Eloquent;

use App\Models\Category;
use App\Repositories\Interfaces\CategoryRepositoryInterface;

/**
 * Class CategoryRepository.
 * Triển khai các phương thức truy vấn dữ liệu cho Category model.
 */
class CategoryRepository extends BaseRepository implements CategoryRepositoryInterface
{
    /**
     * <PERSON>h sách các trường có thể tìm kiếm toàn văn.
     *
     * @var array
     */
    protected array $searchableFields = [
        'name',
        'slug',
        'description',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    /**
     * Trả về tên class của model.
     *
     * @return string
     */
    protected function getModelClass(): string
    {
        return Category::class;
    }

    /**
     * L<PERSON>y danh sách ID của các danh mục dựa trên slug.
     *
     * @param array $slugs Mảng chứa các slug của danh mục.
     * @return array Mảng chứa các ID của danh mục.
     */
    public function getIdsBySlugs(array $slugs): array
    {
        return $this->model->whereIn('slug', $slugs)->pluck('id')->toArray();
    }
}
