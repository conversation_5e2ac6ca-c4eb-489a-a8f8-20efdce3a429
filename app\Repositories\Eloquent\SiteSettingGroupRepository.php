<?php

namespace App\Repositories\Eloquent;

use App\Models\SiteSettingGroup;
use App\Repositories\Interfaces\SiteSettingGroupRepositoryInterface;

/**
 * Repository cho nhóm cài đặt hệ thống
 */
class SiteSettingGroupRepository extends BaseRepository implements SiteSettingGroupRepositoryInterface
{
    /**
     * <PERSON>h sách các trường có thể tìm kiếm toàn văn.
     *
     * @var array
     */
    protected array $searchableFields = [
        'name',
        'group_key',
        'description',
    ];

    /**
     * Lấy class model tương ứng
     *
     * @return string
     */
    protected function getModelClass(): string
    {
        return SiteSettingGroup::class;
    }
}
