<?php

namespace App\Repositories\Eloquent;

use App\Models\GalleryGroup;
use App\Repositories\Interfaces\GalleryGroupRepositoryInterface;

class GalleryGroupRepository extends BaseRepository implements GalleryGroupRepositoryInterface
{
    /**
     * <PERSON>h sách các trường có thể tìm kiếm toàn văn.
     *
     * @var array
     */
    protected array $searchableFields = [
        'name',
        'location_key',
        'description',
    ];

    protected function getModelClass(): string
    {
        return GalleryGroup::class;
    }
}
