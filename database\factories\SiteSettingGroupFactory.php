<?php

namespace Database\Factories;

use App\Models\SiteSettingGroup;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * Factory cho model SiteSettingGroup
 *
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SiteSettingGroup>
 */
class SiteSettingGroupFactory extends Factory
{
    /**
     * Tên model tương ứng với factory này
     *
     * @var string
     */
    protected $model = SiteSettingGroup::class;

    /**
     * Định nghĩa trạng thái mặc định của model
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Tạo group_key unique bằng cách thêm timestamp và random string
        $baseKey = $this->faker->randomElement([
            'custom_settings',
            'test_settings',
            'demo_settings',
            'temp_settings',
            'sample_settings',
            'mock_settings',
            'dev_settings',
            'staging_settings',
        ]);

        // Tạo group_key unique
        $uniqueKey = $baseKey . '_' . time() . '_' . $this->faker->randomNumber(4);

        return [
            'name' => $this->faker->words(2, true),
            'group_key' => $uniqueKey,
            'description' => $this->faker->sentence(10),
            'order' => $this->faker->randomNumber(10),
            'created_by' => User::factory(),
            'updated_by' => User::factory(),
            'created_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
        ];
    }

    /**
     * State cho nhóm cài đặt chung
     *
     * @return static
     */
    public function general(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Cài đặt chung',
            'group_key' => 'general_settings',
            'description' => 'Nhóm cài đặt chung của hệ thống',
            'order' => 1,
        ]);
    }

    /**
     * State cho nhóm cài đặt email
     *
     * @return static
     */
    public function email(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Cài đặt email',
            'group_key' => 'email_settings',
            'description' => 'Nhóm cài đặt liên quan đến email và thông báo',
            'order' => 2,
        ]);
    }

    /**
     * State cho nhóm cài đặt mạng xã hội
     *
     * @return static
     */
    public function socialMedia(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Mạng xã hội',
            'group_key' => 'social_media',
            'description' => 'Nhóm cài đặt liên quan đến các mạng xã hội',
            'order' => 3,
        ]);
    }

    /**
     * State cho nhóm cài đặt thanh toán
     *
     * @return static
     */
    public function payment(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Cài đặt thanh toán',
            'group_key' => 'payment_settings',
            'description' => 'Nhóm cài đặt liên quan đến thanh toán và giao dịch',
            'order' => 4,
        ]);
    }

    /**
     * State cho nhóm cài đặt bảo mật
     *
     * @return static
     */
    public function security(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Cài đặt bảo mật',
            'group_key' => 'security_settings',
            'description' => 'Nhóm cài đặt liên quan đến bảo mật hệ thống',
            'order' => 5,
        ]);
    }

    /**
     * State với group_key tùy chỉnh
     *
     * @param string $groupKey
     * @param string|null $name
     * @param string|null $description
     * @return static
     */
    public function withGroupKey(string $groupKey, ?string $name = null, ?string $description = null, ?int $order = null): static
    {
        return $this->state(fn (array $attributes) => [
            'group_key' => $groupKey,
            'name' => $name ?? ucfirst(str_replace('_', ' ', $groupKey)),
            'description' => $description ?? "Nhóm cài đặt {$groupKey}",
            'order' => $order ?? 1,
        ]);
    }

    /**
     * State với user cụ thể
     *
     * @param User $user
     * @return static
     */
    public function createdBy(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'created_by' => $user->id,
            'updated_by' => $user->id,
        ]);
    }
}
