<?php

namespace App\Observers;

use App\Models\CardPrint;

/**
 * Observer xử lý các sự kiện liên quan đến CardPrint
 */
class CardPrintObserver extends BaseObserver
{
    /**
     * Xử lý sự kiện "created" của CardPrint.
     *
     * @param \App\Models\CardPrint $cardPrint
     * @return void
     */
    public function created(CardPrint $cardPrint): void
    {
        $this->log(
            action: 'create',
            description: "Tạo yêu cầu in thẻ mới (ID: {$cardPrint->id})",
            model: $cardPrint,
            dataAfter: $cardPrint->toArray()
        );
    }

    /**
     * Xử lý sự kiện "updated" của CardPrint.
     *
     * @param \App\Models\CardPrint $cardPrint
     * @return void
     */
    public function updated(CardPrint $cardPrint): void
    {
        $this->log(
            action: 'update',
            description: "Cập nhật yêu cầu in thẻ (ID: {$cardPrint->id})",
            model: $cardPrint,
            dataBefore: $cardPrint->getOriginal(),
            dataAfter: $cardPrint->getChanges()
        );
    }

    /**
     * Xử lý sự kiện "deleting" của CardPrint.
     *
     * @param \App\Models\CardPrint $cardPrint
     * @return void
     */
    public function deleting(CardPrint $cardPrint): void
    {
        $this->log(
            action: 'delete',
            description: "Xóa yêu cầu in thẻ (ID: {$cardPrint->id})",
            model: $cardPrint,
            dataBefore: $cardPrint->toArray()
        );
    }
}
