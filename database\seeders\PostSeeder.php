<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Post;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class PostSeeder extends Seeder
{
    /**
     * Tạo dữ liệu mẫu cho bảng posts.
     */
    public function run(): void
    {
        $user = User::first();
        // Lấy các danh mục cần thiết
        $categories = Category::whereIn('slug', ['tin-tuc', 'su-kien', 'huong-dan'])->get()->keyBy('slug');

        $posts = [
            [
                'title' => 'Ra mắt sự kiện "Long Tranh Hổ Đấu"',
                'body' => '<p>Sự kiện "Long Tranh Hổ Đấu" sẽ chính thức bắt đầu từ ngày 01/01/2025. Phần thưởng cực kỳ hấp dẫn đang chờ đón các vị anh hùng. Hãy chuẩn bị sẵn sàng!</p>',
                'category_id' => $categories['su-kien']->id ?? null,
                'is_hot' => true,
                'show_on_homepage' => true,
                'published_at' => now(),
                'cover_image' => '/storage/posts/long-tranh-ho-dau.jpg',
                'meta_title' => 'Ra mắt sự kiện "Long Tranh Hổ Đấu" - Kiếm Hiệp Tình 1',
                'meta_description' => 'Sự kiện "Long Tranh Hổ Đấu" sẽ chính thức bắt đầu từ ngày 01/01/2025 với nhiều phần thưởng hấp dẫn.',
                'meta_keywords' => 'long tranh hổ đấu, sự kiện, kiếm hiệp tình 1',
            ],
            [
                'title' => 'Bản cập nhật tháng 12: Cân bằng sức mạnh môn phái',
                'body' => '<p>Trong bản cập nhật này, chúng tôi đã điều chỉnh sức mạnh của các môn phái để tạo ra sự cân bằng hơn trong các trận chiến. Chi tiết xem tại đây.</p>',
                'category_id' => $categories['tin-tuc']->id ?? null,
                'is_hot' => true,
                'show_on_homepage' => true,
                'published_at' => now()->subDays(2),
                'cover_image' => '/storage/posts/can-bang-mon-phai.jpg',
                'meta_title' => 'Bản cập nhật tháng 12: Cân bằng sức mạnh môn phái - Kiếm Hiệp Tình 1',
                'meta_description' => 'Điều chỉnh sức mạnh của các môn phái để tạo ra sự cân bằng hơn trong các trận chiến.',
                'meta_keywords' => 'cập nhật, cân bằng, môn phái, kiếm hiệp tình 1',
            ],
            [
                'title' => 'Hướng dẫn tham gia hoạt động Công Thành Chiến',
                'body' => '<p>Công Thành Chiến là một trong những hoạt động hấp dẫn nhất. Bài viết này sẽ hướng dẫn chi tiết cách thức tham gia và giành chiến thắng cho các bang hội.</p>',
                'category_id' => $categories['huong-dan']->id ?? null,
                'is_hot' => false,
                'show_on_homepage' => false,
                'published_at' => now()->subDays(5),
                'cover_image' => '/storage/posts/cong-thanh-chien.jpg',
                'meta_title' => 'Hướng dẫn tham gia hoạt động Công Thành Chiến - Kiếm Hiệp Tình 1',
                'meta_description' => 'Hướng dẫn chi tiết cách thức tham gia và giành chiến thắng trong Công Thành Chiến.',
                'meta_keywords' => 'công thành chiến, hướng dẫn, bang hội, kiếm hiệp tình 1',
            ],
        ];

        foreach ($posts as $postData) {
            $slug = Str::slug($postData['title']);
            Post::updateOrCreate(
                ['slug' => $slug], // Điều kiện để tìm hoặc tạo
                array_merge($postData, [
                    'slug' => $slug,
                    'title' => $postData['title'],
                    'excerpt' => Str::limit(strip_tags($postData['body']), 150),
                    'status' => ['draft', 'published', 'pending_review'][array_rand(['draft', 'published', 'pending_review'])],
                    'created_by' => $user->id,
                    'updated_by' => $user->id,
                ])
            );
        }

        $this->command->info('✅ Các bài viết mẫu đã được tạo thành công!');
    }
}
