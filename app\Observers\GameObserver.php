<?php

namespace App\Observers;

use App\Models\Game;
use App\Services\FileUploadService;
// Thêm dòng này
use Illuminate\Support\Facades\Storage; // Thêm dòng này để sử dụng Storage facade

class GameObserver extends BaseObserver
{
    protected FileUploadService $fileUploadService;

    /**
     * Handle the Game "created" event.
     *
     * @param Game $game
     * @return void
     */
    public function created(Game $game): void
    {
        $this->log(
            action: 'create',
            description: "Tạo mới game '{$game->name}'",
            model: $game,
            dataAfter: $game->toArray()
        );
    }

    /**
     * Handle the Game "updated" event.
     *
     * @param Game $game
     * @return void
     */
    public function updated(Game $game): void
    {
        $this->log(
            action: 'update',
            description: "Cập nhật game '{$game->name}'",
            model: $game,
            dataBefore: $game->getOriginal(),
            dataAfter: $game->getChanges()
        );
    }

    /**
     * Handle the Game "deleting" event.
     *
     * @param Game $game
     * @return void
     */
    public function deleting(Game $game): void
    {
        $this->log(
            action: 'delete',
            description: "Xóa game '{$game->name}'",
            model: $game,
            dataBefore: $game->toArray()
        );

        // Thêm timestamp vào slug để tránh xung đột khi tạo mới
        if (! $game->isForceDeleting()) {
            $game->slug = $game->slug . '_deleted_' . time();
            $game->saveQuietly(); // Lưu mà không kích hoạt các events
        }

        // Nếu game có ảnh thumb, xóa ảnh đó khỏi storage khi game bị soft delete
        if ($game->thumb) {
            // Lấy đường dẫn thực tế của file từ URL
            $filePath = $this->fileUploadService->extractPathFromUrl($game->thumb);
            // Xóa file
            $this->fileUploadService->deleteFile($filePath);
        }
    }

    /**
     * Handle the Game "restored" event.
     *
     * @param Game $game
     * @return void
     */
    public function restored(Game $game): void
    {
        $this->log(
            action: 'restore',
            description: "Khôi phục game '{$game->name}'",
            model: $game,
            dataBefore: $game->toArray()
        );
    }

    /**
     * Handle the Game "force deleted" event.
     *
     * @param Game $game
     * @return void
     */
    public function forceDeleted(Game $game): void
    {
        $this->log(
            action: 'force_delete',
            description: "Xóa vĩnh viễn game '{$game->name}'",
            model: $game,
            dataBefore: $game->toArray()
        );
    }
}
