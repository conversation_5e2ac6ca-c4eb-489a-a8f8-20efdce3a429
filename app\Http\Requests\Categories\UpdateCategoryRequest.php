<?php

namespace App\Http\Requests\Categories;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\UploadedFile;
use Illuminate\Validation\Rule;

class UpdateCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Cho phép tất cả người dùng đã xác thực tạo request
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // Lấy ID của category từ route
        $categoryId = $this->route('id');

        return [
            'name' => ['sometimes', 'required', 'string', 'max:255'],
            'slug' => [
                'sometimes',
                'required',
                'string',
                'max:255',
                Rule::unique('categories', 'slug')->ignore($categoryId),
            ],
            'parent_id' => ['nullable', 'integer', 'exists:categories,id'],
            'description' => ['nullable', 'string'],
            'status' => ['nullable', 'integer', Rule::in([0, 1])],
            'is_featured' => ['nullable', 'boolean'],
            'featured_order' => ['nullable', 'integer', 'min:0'],
            'order' => ['nullable', 'integer', 'min:0'],
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string'],
            'meta_image' => ['nullable'],
        ];
    }

    /**
     * Lấy các thông báo lỗi tùy chỉnh cho các quy tắc validation.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Tên danh mục không được để trống.',
            'name.string' => 'Tên danh mục phải là chuỗi ký tự.',
            'name.max' => 'Tên danh mục không được vượt quá 255 ký tự.',
            'slug.required' => 'Slug không được để trống.',
            'slug.unique' => 'Slug này đã tồn tại trong hệ thống.',
            'parent_id.exists' => 'Danh mục cha được chọn không hợp lệ.',
            'status.in' => 'Trạng thái không hợp lệ. Vui lòng chọn published hoặc draft.',
            'is_featured.boolean' => 'Trường "Hiển thị nổi bật" phải là true hoặc false.',
            'featured_order.integer' => 'Thứ tự nổi bật phải là một số nguyên.',
            'featured_order.min' => 'Thứ tự nổi bật phải lớn hơn hoặc bằng 0.',
            'order.integer' => 'Thứ tự hiển thị phải là một số nguyên.',
            'order.min' => 'Thứ tự hiển thị phải lớn hơn hoặc bằng 0.',
            'meta_title.max' => 'Tiêu đề SEO không được vượt quá 255 ký tự.',
            'meta_image.max' => 'Đường dẫn ảnh SEO không được vượt quá 255 ký tự.',
        ];
    }

    /**
     * Defines the parameters for the request body.
     *
     * @return array
     */
    /**
     * Validate sau khi các quy tắc validation đã chạy
     *
     * @param \Illuminate\Validation\Validator $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $metaImage = $this->input('meta_image');

            // Nếu meta_image được cung cấp
            if ($metaImage !== null) {
                // Nếu meta_image không phải là file upload
                if (! ($metaImage instanceof UploadedFile)) {
                    // Kiểm tra nếu là URL hợp lệ
                    if (! is_string($metaImage) || ! filter_var($metaImage, FILTER_VALIDATE_URL)) {
                        $validator->errors()->add('meta_image', 'Ảnh đại diện phải là file upload hoặc URL hợp lệ.');
                    } else {
                        // Kiểm tra đuôi file của URL có phải là ảnh không
                        $fileExtension = pathinfo(parse_url($metaImage, PHP_URL_PATH), PATHINFO_EXTENSION);
                        $fileExtension = strtolower($fileExtension);

                        $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
                        if (! in_array($fileExtension, $validExtensions)) {
                            $validator->errors()->add('meta_image', 'URL ảnh đại diện phải có đuôi hợp lệ (' . implode(', ', $validExtensions) . ').');
                        }
                    }
                } else {
                    // Kiểm tra loại file phù hợp
                    if (! in_array($metaImage->getMimeType(), ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'])) {
                        $validator->errors()->add('meta_image', 'File upload phải là một hình ảnh hợp lệ (jpeg, png, gif, webp, svg).');
                    }

                    // Kiểm tra kích thước file
                    $maxSize = 5120; // 5MB
                    if ($metaImage->getSize() > $maxSize * 1024) {
                        $validator->errors()->add('meta_image', 'Kích thước file không được vượt quá ' . ($maxSize / 1024) . 'MB.');
                    }
                }
            }
        });
    }

    public function bodyParameters(): array
    {
        return [
            'name' => [
                'description' => 'Tên danh mục.',
                'example' => 'Tin tức',
            ],
            'slug' => [
                'description' => 'URL thân thiện SEO (duy nhất).',
                'example' => 'tin-tuc',
            ],
            'parent_id' => [
                'description' => 'ID của danh mục cha (tùy chọn).',
                'example' => null,
            ],
            'description' => [
                'description' => 'Mô tả danh mục (tùy chọn).',
                'example' => 'Mô tả chi tiết về danh mục tin tức.',
            ],
            'status' => [
                'description' => 'Trạng thái danh mục (0: Nháp, 1: Công khai). Mặc định là 1.',
                'example' => 1,
            ],
            'is_featured' => [
                'description' => 'Hiển thị trên trang chủ hay không. Mặc định là false.',
                'example' => false,
            ],
            'featured_order' => [
                'description' => 'Thứ tự trong danh sách nổi bật. Mặc định là 0.',
                'example' => 0,
            ],
            'order' => [
                'description' => 'Thứ tự hiển thị chung. Mặc định là 0.',
                'example' => 0,
            ],
            'meta_title' => [
                'description' => 'Tiêu đề SEO (tùy chọn).',
                'example' => 'Tin Tức Game Mới Nhất',
            ],
            'meta_description' => [
                'description' => 'Mô tả SEO cho danh mục (tùy chọn).',
                'example' => 'Mô tả SEO cho danh mục tin tức.',
            ],
            'meta_image' => [
                'description' => 'Ảnh đại diện khi chia sẻ lên mạng xã hội (tùy chọn). Có thể là file upload hoặc URL hợp lệ của ảnh.',
                'example' => 'https://example.com/images/news-category.jpg',
            ],
        ];
    }
}
