<?php

namespace App\Resources\Public\MenuItems;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource để hiển thị thông tin của một menu item cho người dùng cuối.
 * Hỗ trợ cấu trúc menu lồng nhau (nested).
 */
class MenuItemResource extends JsonResource
{
    /**
     * Dữ liệu đặc biệt cho resource (relationships, computed properties)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'title' => $this->resource->title,
            'target' => $this->resource->target,
            'url' => $this->resolveUrl(),
            // Sử dụng `collection` để xử lý một tập hợp các children
            // `whenLoaded` đảm bảo 'children' chỉ được xử lý khi nó đã được eager load
            'children' => self::collection($this->whenLoaded('children')),
        ];
    }

    /**
     * Tạo URL cuối cùng cho menu item.
     *
     * Ưu tiên custom_url. N<PERSON><PERSON> không có, sẽ tạo URL dựa trên
     * đối tượng được liên kết (Post, Category, StaticPage...).
     *
     * @return string
     */
    private function resolveUrl(): string
    {
        // 1. Ưu tiên URL tùy chỉnh
        if (! empty($this->resource->custom_url)) {
            return $this->resource->custom_url;
        }

        // 2. Xử lý URL từ đối tượng liên kết (polymorphic relationship)
        if ($this->resource->relationLoaded('linkable') && $this->resource->linkable) {
            $linkable = $this->resource->linkable;

            // Giả định các URL prefix cho từng loại nội dung. Bạn có thể thay đổi cho phù hợp với frontend.
            switch (get_class($linkable)) {
                case \App\Models\Post::class: return '/' . $linkable->slug;
                case \App\Models\Category::class: return '/' . $linkable->slug;
                case \App\Models\StaticPage::class: return '/' . $linkable->slug;
                default: return '/'; // Fallback
            }
        }

        // 3. Trả về '/' nếu không có URL nào được xác định
        return '/';
    }
}
