<?php

namespace App\Repositories\Interfaces;

/**
 * <PERSON><PERSON><PERSON> cho CategoryRepository, kế thừa các phương thức cơ bản từ BaseRepositoryInterface.
 * <PERSON><PERSON> thể định nghĩa thêm các phương thức đặc thù cho Category tại đây.
 */
interface CategoryRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Lấy danh sách ID của các danh mục dựa trên slug.
     *
     * @param array $slugs Mảng chứa các slug của danh mục.
     * @return array Mảng chứa các ID của danh mục.
     */
    public function getIdsBySlugs(array $slugs): array;
}
