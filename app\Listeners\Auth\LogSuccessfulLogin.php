<?php

namespace App\Listeners\Auth;

use App\Events\Auth\LoginSuccess;
use App\Listeners\BaseListener;

class LogSuccessfulLogin extends BaseListener
{
    /**
     * Xử lý sự kiện.
     *
     * @param \App\Events\Auth\LoginSuccess $event
     * @return void
     */
    public function handle(LoginSuccess $event): void
    {
        $this->log(
            action: 'login',
            description: "Người dùng '{$event->user->name}' đã đăng nhập thành công.",
            model: $event->user
        );
    }
}
