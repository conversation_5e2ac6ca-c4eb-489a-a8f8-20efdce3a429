<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\MenuItems\StoreMenuItemRequest;
use App\Http\Requests\MenuItems\UpdateMenuItemRequest;
use App\Resources\MenuItems\MenuItemResource;
use App\Resources\MenuItems\MenuItemResourceCollection;
use App\Services\MenuItemService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Menu - Item
 */
class MenuItemController extends BaseController
{
    public function __construct(protected MenuItemService $service)
    {
    }

    /**
     * Lấy danh sách các mục trong một menu cụ thể.
     *
     * @urlParam menu_id integer required ID của menu. Example: 1
     *
     * @param Request $request
     * @param int $menu_id ID của menu
     * @return JsonResponse
     */
    public function index(Request $request, int $menu_id): JsonResponse
    {
        $request->merge(['menu_id' => $menu_id, 'parent_id' => 'IS_NULL']);
        $menuItems = $this->service->list($request, ['createdBy', 'updatedBy', 'children', 'linkable']);

        return $this->success(
            new MenuItemResourceCollection($menuItems),
            'Lấy danh sách menu item thành công'
        );
    }

    /**
     * Lưu một menu item mới thuộc menu cụ thể.
     *
     * @urlParam menu_id integer required ID của menu. Example: 1
     *
     * @param StoreMenuItemRequest $request
     * @param int $menu_id ID của menu
     * @return JsonResponse
     */
    public function store(StoreMenuItemRequest $request, int $menu_id): JsonResponse
    {
        $validatedData = $request->validated();
        $menuItem = $this->service->create($validatedData, ['createdBy', 'updatedBy']);

        return $this->created(
            new MenuItemResource($menuItem),
            'Tạo menu item thành công'
        );
    }

    /**
     * Hiển thị chi tiết một menu item.
     *
     * @urlParam menu_id integer required ID của menu. Example: 1
     * @urlParam id integer required ID của menu item. Example: 1
     *
     * @param int $menu_id ID của menu
     * @param int $id ID của menu item
     * @return JsonResponse
     */
    public function show(int $menu_id, int $id): JsonResponse
    {
        $menuItem = $this->service->read($id, ['createdBy', 'updatedBy', 'linkable']);

        if ($menuItem->menu_id != $menu_id) {
            return $this->notFound('Menu item không thuộc về menu này');
        }

        return $this->success(
            new MenuItemResource($menuItem),
            'Lấy thông tin menu item thành công'
        );
    }

    /**
     * Cập nhật một menu item.
     *
     * @urlParam menu_id integer required ID của menu. Example: 1
     * @urlParam id integer required ID của menu item. Example: 1
     *
     * @param UpdateMenuItemRequest $request
     * @param int $menu_id ID của menu
     * @param int $id ID của menu item
     * @return JsonResponse
     */
    public function update(UpdateMenuItemRequest $request, int $menu_id, int $id): JsonResponse
    {
        $existingMenuItem = $this->service->read($id, []);
        if ($existingMenuItem->menu_id != $menu_id) {
            return $this->notFound('Menu item không thuộc về menu này');
        }

        $menuItem = $this->service->update($id, $request->validated(), ['createdBy', 'updatedBy', 'linkable']);

        return $this->success(
            new MenuItemResource($menuItem),
            'Cập nhật menu item thành công'
        );
    }

    /**
     * Xóa một menu item.
     *
     * @urlParam menu_id integer required ID của menu. Example: 1
     * @urlParam id integer required ID của menu item. Example: 1
     *
     * @param int $menu_id ID của menu
     * @param int $id ID của menu item
     * @return JsonResponse
     */
    public function destroy(int $menu_id, int $id): JsonResponse
    {
        $existingMenuItem = $this->service->read($id, []);
        if ($existingMenuItem->menu_id != $menu_id) {
            return $this->notFound('Menu item không thuộc về menu này');
        }

        $deleted = $this->service->delete($id);

        if (! $deleted) {
            return $this->error('Không thể xóa menu item này', 500);
        }

        return $this->successNoContent('Đã xóa menu item thành công');
    }
}
