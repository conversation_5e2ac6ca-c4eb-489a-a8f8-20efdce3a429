<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('site_setting_groups', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('Tên định danh cho nhóm cài đặt (General, SEO, ...)');
            $table->string('group_key')->unique()->comment('Key gọi trên frontend (e.g. general, seo, ...)');
            $table->text('description')->nullable()->comment('<PERSON>ô tả nhóm cài đặt');
            $table->integer('order')->default(0)->comment('Sắp xếp thứ tự trong danh sách nhóm cài đặt');
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete()->comment('Người tạo nhóm cài đặt');
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete()->comment('Người cập nhật nhóm cài đặt');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('site_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('group_id')->constrained('site_setting_groups')->cascadeOnDelete()->comment('Nhóm cài đặt');
            $table->string('key')->unique()->comment('Khóa cài đặt (duy nhất, dùng để truy xuất nhanh)')->index();
            $table->string('name')->comment('Tên hiển thị hoặc mô tả cài đặt');
            $table->text('description')->nullable()->comment('Mô tả cài đặt');
            $table->text('value')->nullable()->comment('Giá trị của cài đặt, có thể là text, json, ...');
            $table->string('type', 50)->default('text')->comment('Loại dữ liệu: text, number, json, image, ...');
            $table->boolean('is_private')->default(false)->comment('Cài đặt riêng tư');
            $table->boolean('is_deletable')->default(false)->comment('Cài đặt có thể xóa không');
            $table->tinyInteger('status')->default(1)->comment('Trạng thái (1: hoạt động, 0: ẩn)');
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete()->comment('Người tạo');
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete()->comment('Người cập nhật');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('site_settings');
    }
};
