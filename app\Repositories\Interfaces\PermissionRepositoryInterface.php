<?php

namespace App\Repositories\Interfaces;

use Illuminate\Database\Eloquent\Collection;

interface PermissionRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * L<PERSON>y tất cả permissions
     *
     * @return Collection
     */
    public function getAllPermissions(): Collection;

    /**
     * Lấy permissions theo group code
     *
     * @param string $groupCode
     * @return Collection
     */
    public function getPermissionsByGroup(string $groupCode): Collection;

    /**
     * Lấy permissions được nhóm theo group_code
     *
     * @return array
     */
    public function getPermissionsGroupedByCode(): array;
}
