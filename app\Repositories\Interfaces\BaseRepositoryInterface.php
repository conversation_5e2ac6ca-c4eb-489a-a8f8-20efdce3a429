<?php

namespace App\Repositories\Interfaces;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;

/**
 * Interface cơ bản cho Repository Pattern
 *
 * Repository Pattern giúp:
 * - Tách biệt business logic khỏi data access layer
 * - Cung cấp interface thống nhất cho data operations
 * - D<PERSON> mock/stub khi unit testing
 * - Dễ thay đổi data source (MySQL -> MongoDB) mà không ảnh hưởng business logic
 *
 * Mọi Repository interface khác nên extend interface này
 * Mọi Repository implementation phải implement interface này
 *
 * Naming convention:
 * - Interface: UserRepositoryInterface
 * - Implementation: UserRepository, EloquentUserRepository
 */
interface BaseRepositoryInterface
{
    /**
     * L<PERSON>y tất cả bản ghi từ database
     *
     * Use cases:
     * - Export toàn bộ data
     * - Admin dashboard statistics
     * - Dropdown select options
     *
     * Cảnh báo:
     * - Không nên dùng cho table lớn (hàng nghìn records)
     * - Cân nhắc dùng paginate() hoặc chunk() thay thế
     * - Collection có thể chiếm nhiều memory
     *
     * @return Collection Collection chứa tất cả Model instances
     */
    public function all(): Collection;

    /**
     * Phân trang kết quả query
     *
     * Benefits:
     * - Giảm memory usage
     * - Tăng performance
     * - Better UX với pagination controls
     *
     * Return object chứa:
     * - items: Collection của current page
     * - total: Tổng số records
     * - perPage: Số records mỗi trang
     * - currentPage: Trang hiện tại
     * - lastPage: Trang cuối
     * - Các helper methods: links(), nextPageUrl(), etc.
     *
     * @param array $params Tham số phân trang
     * @return LengthAwarePaginator Object chứa data và pagination info
     */
    public function paginate(array $params = [], array $with = []): LengthAwarePaginator;

    /**
     * Phân trang theo search Params
     *
     * Tương tự như paginate() nhưng thêm tham số search
     * Search Params bao gồm các tham số search, filter, sort, etc.
     *
     * @param array $searchParams Tham số search
     * @param array $params Tham số phân trang
     * @return LengthAwarePaginator Object chứa data và pagination info
     */
    public function paginateBySearchParams(array $searchParams, array $params = []): LengthAwarePaginator;

    /**
     * Tìm bản ghi theo primary key (thường là ID)
     *
     * Nullable return type:
     * - Trả về Model instance nếu tìm thấy
     * - Trả về null nếu không tìm thấy
     * - KHÔNG throw exception
     *
     * Use case: Khi cần check existence trước khi xử lý
     *
     * Ví dụ:
     *
     * $user = $userRepo->find(123);
     * if ($user) {
     *     // Process user
     * }
     *
     *
     * @param int $id Primary key value (int cho auto-increment, string cho UUID)
     * @return Model|null Model instance hoặc null
     */
    public function find(int $id): ?Model;

    /**
     * Tìm theo ID hoặc throw exception nếu không tìm thấy
     *
     * Khác với find():
     * - LUÔN trả về Model instance
     * - Throw ModelNotFoundException nếu không tìm thấy
     * - Laravel tự động convert thành 404 response
     *
     * Use case: Khi chắc chắn record phải tồn tại
     *
     * Ví dụ:
     *
     * // Trong Controller
     * $user = $userRepo->findOrFail($id);
     * // Không cần check null, chắc chắn có $user
     * return $user->toArray();
     *
     *
     * @param int $id Primary key value
     * @return Model Model instance (never null)
     * @throws ModelNotFoundException Khi không tìm thấy
     */
    public function findOrFail(int $id, array $with = []): Model;

    /**
     * Tìm record theo field và value
     *
     * @param string $field
     * @param mixed $value
     * @return Model|null
     */
    public function findBy(string $field, mixed $value): ?Model;

    /**
     * Tìm hoặc tạo mới record
     *
     * @param array $attributes
     * @param array $values
     * @return Model
     */
    public function firstOrCreate(array $attributes, array $values = []): Model;

    /**
     * Tìm theo field hoặc throw exception
     *
     * Kết hợp findBy() với OrFail pattern
     * Useful cho unique fields như email, username, code
     *
     * Ví dụ:
     *
     * // Find user by email, 404 if not found
     * $user = $userRepo->findByOrFail('email', $email);
     *
     *
     * @param string $field Tên column
     * @param mixed $value Giá trị search
     * @return Model Model instance (never null)
     * @throws ModelNotFoundException
     */
    public function findByOrFail(string $field, mixed $value): Model;

    /**
     * Tạo mới bản ghi trong database
     *
     * Process:
     * 1. Mass assign data vào Model
     * 2. Validate fillable/guarded
     * 3. Insert vào database
     * 4. Return Model với ID và timestamps
     *
     * Ví dụ:
     *
     * $user = $userRepo->create([
     *     'name' => 'John Doe',
     *     'email' => '<EMAIL>',
     *     'password' => bcrypt('secret')
     * ]);
     * echo $user->id; // Auto-generated ID
     *
     *
     * Lưu ý: Data nên được validate ở FormRequest trước
     *
     * @param array $data Dữ liệu cần tạo
     * @return Model Model instance vừa được tạo
     */
    public function create(array $data): Model;

    /**
     * Lấy tên của Model.
     *
     * @return string
     */
    public function getModelName(): string;

    /**
     * Cập nhật bản ghi existing theo ID
     *
     * Process:
     * 1. Find record by ID (throw nếu không tồn tại)
     * 2. Update attributes
     * 3. Save vào database
     * 4. Return updated Model
     *
     * Ví dụ:
     *
     * $user = $userRepo->update(123, [
     *     'name' => 'Jane Doe',
     *     'status' => 'active'
     * ]);
     *
     *
     * Chỉ update fields được pass, giữ nguyên fields khác
     *
     * @param int $id Primary key
     * @param array $data Fields cần update
     * @return Model Updated Model instance
     * @throws ModelNotFoundException
     */
    public function update(int $id, array $data): Model;

    /**
     * Xóa mềm bản ghi (soft delete)
     *
     * Soft delete behavior:
     * - Update deleted_at = current timestamp
     * - Record vẫn trong database
     * - Tự động exclude khỏi queries
     * - Có thể restore sau này
     *
     * Use cases:
     * - Audit trail requirements
     * - Undo functionality
     * - Data recovery
     *
     * Hard delete dùng forceDelete() method
     *
     * @param int $id Primary key
     * @return bool True nếu xóa thành công
     * @throws ModelNotFoundException
     */
    public function delete(int $id): bool;

    /**
     * Khôi phục bản ghi đã soft delete
     *
     * Process:
     * - Set deleted_at = null
     * - Record active trở lại
     * - Xuất hiện trong queries bình thường
     *
     * Ví dụ:
     *
     * // User nhấn "Undo" sau khi delete
     * $restored = $userRepo->restore($userId);
     *
     *
     * @param int $id Primary key của record đã xóa
     * @return bool True nếu restore thành công
     * @throws ModelNotFoundException Nếu record không tồn tại
     */
    public function restore(int $id): bool;

    /**
     * Xóa vĩnh viễn bản ghi (hard delete)
     *
     * CẢNH BÁO:
     * - Xóa VĨNH VIỄN khỏi database
     * - KHÔNG thể restore
     * - Cẩn thận với foreign key constraints
     *
     * Use cases:
     * - GDPR compliance (right to be forgotten)
     * - Clean up old soft-deleted records
     * - Test data cleanup
     *
     * Best practice: Confirm với user trước khi force delete
     *
     * @param int $id Primary key
     * @return bool True nếu xóa thành công
     * @throws ModelNotFoundException
     */
    public function forceDelete(int $id): bool;

    /**
     * Tìm bản ghi kể cả đã soft delete
     *
     * Override default behavior - include soft deleted records
     *
     * Use cases:
     * - Admin panel xem all records
     * - Restore functionality
     * - Audit reports
     *
     * Ví dụ:
     *
     * $user = $userRepo->findWithTrashed(123);
     * if ($user && $user->trashed()) {
     *     echo "User was deleted at: " . $user->deleted_at;
     * }
     *
     *
     * @param int $id Primary key
     * @return Model|null Model instance (cả active và deleted) hoặc null
     */
    public function findWithTrashed(int $id): ?Model;

    /**
     * Tìm CHỈ các bản ghi đã soft delete
     *
     * Exclude active records, CHỈ return deleted records
     *
     * Use cases:
     * - Recycle bin feature
     * - Deleted items report
     * - Bulk restore operations
     *
     * Ví dụ:
     *
     * // Show only deleted users in trash
     * $deletedUser = $userRepo->findOnlyTrashed($id);
     *
     *
     * @param int $id Primary key
     * @return Model|null Deleted Model instance hoặc null
     */
    public function findOnlyTrashed(int $id): ?Model;
}
