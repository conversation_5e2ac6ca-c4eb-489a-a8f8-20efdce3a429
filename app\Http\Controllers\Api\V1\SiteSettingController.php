<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\SiteSettings\StoreSiteSettingRequest;
use App\Http\Requests\SiteSettings\UpdateSiteSettingRequest;
use App\Resources\SiteSettings\SiteSettingResource;
use App\Resources\SiteSettings\SiteSettingResourceCollection;
use App\Services\SiteSettingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Cài đặt trang web
 */
class SiteSettingController extends BaseController
{
    /**
     * SiteSettingService instance.
     */
    protected SiteSettingService $service;

    /**
     * SiteSettingController constructor.
     */
    public function __construct(SiteSettingService $service)
    {
        $this->service = $service;
    }

    /**
     * Lấy danh sách các cài đặt thuộc một nhóm cụ thể.
     *
     * @urlParam site_setting_group_id integer required ID của nhóm cài đặt. Example: 1
     *
     * @param Request $request
     * @param int $site_setting_group_id ID của nhóm cài đặt
     * @return JsonResponse
     */
    public function index(Request $request, int $site_setting_group_id): JsonResponse
    {
        $request->merge(['group_id' => $site_setting_group_id]);
        $settings = $this->service->list($request, ['createdBy', 'updatedBy', 'group']);

        return $this->success(new SiteSettingResourceCollection($settings), 'Lấy danh sách cài đặt thành công');
    }

    /**
     * Tạo một cài đặt mới thuộc nhóm cụ thể.
     *
     * @urlParam site_setting_group_id integer required ID của nhóm cài đặt. Example: 1
     *
     * @param StoreSiteSettingRequest $request
     * @param int $site_setting_group_id ID của nhóm cài đặt
     * @return JsonResponse
     */
    public function store(StoreSiteSettingRequest $request, int $site_setting_group_id): JsonResponse
    {
        $data = $request->validated();

        $setting = $this->service->create($data, ['createdBy', 'updatedBy']);

        return $this->success(
            new SiteSettingResource($setting),
            'Tạo cài đặt thành công',
            201
        );
    }

    /**
     * Lấy thông tin chi tiết một cài đặt thuộc nhóm cụ thể.
     *
     * @urlParam site_setting_group_id integer required ID của nhóm cài đặt. Example: 1
     * @urlParam id integer required ID của cài đặt. Example: 1
     *
     * @param int $site_setting_group_id ID của nhóm cài đặt
     * @param int $id ID của cài đặt
     * @return JsonResponse
     */
    public function show(int $site_setting_group_id, int $id): JsonResponse
    {
        $setting = $this->service->read($id, ['createdBy', 'updatedBy', 'group']);

        // Kiểm tra cài đặt có thuộc nhóm này không
        if ($setting->group_id != $site_setting_group_id) {
            return $this->error('Cài đặt không thuộc nhóm này', 404);
        }

        return $this->success(new SiteSettingResource($setting), 'Lấy thông tin cài đặt thành công');
    }

    /**
     * Cập nhật một cài đặt thuộc nhóm cụ thể.
     *
     * Lưu ý: Khi cần upload file (meta_image), hãy sử dụng Method Spoofing:
     * - Gửi request HTTP POST thay vì PUT
     * - Thêm trường _method="PUT" vào form-data
     * - Thêm file meta_image vào form-data
     *
     * Nguyên nhân: PHP không xử lý được file upload trong PUT requests trực tiếp.
     *
     * @urlParam site_setting_group_id integer required ID của nhóm cài đặt. Example: 1
     * @urlParam id integer required ID của cài đặt. Example: 1
     *
     * @param UpdateSiteSettingRequest $request
     * @param int $site_setting_group_id ID của nhóm cài đặt
     * @param int $id ID của cài đặt
     * @return JsonResponse
     */
    public function update(UpdateSiteSettingRequest $request, int $site_setting_group_id, int $id): JsonResponse
    {
        // Kiểm tra cài đặt có thuộc nhóm này không
        $settingModel = $this->service->read($id, ['createdBy', 'updatedBy', 'group']);
        if ($settingModel->group_id != $site_setting_group_id) {
            return $this->error('Cài đặt không thuộc nhóm này', 404);
        }

        $data = $request->validated();

        $settingModel = $this->service->update($id, $data, ['createdBy', 'updatedBy']);

        return $this->success(
            new SiteSettingResource($settingModel),
            'Cập nhật cài đặt thành công'
        );
    }

    /**
     * Cập nhật các thuộc tính của cài đặt.
     *
     * @urlParam site_setting_group_id integer required ID của nhóm cài đặt. Example: 1
     * @urlParam id integer required ID của cài đặt. Example: 1
     *
     * @param Request $request
     * @param int $site_setting_group_id ID của nhóm cài đặt
     * @param int $id ID của cài đặt
     * @return JsonResponse
     */
    public function updateAttributes(Request $request, int $site_setting_group_id, int $id): JsonResponse
    {
        // Kiểm tra cài đặt có thuộc nhóm này không
        $settingModel = $this->service->read($id, ['createdBy', 'updatedBy', 'group']);
        if ($settingModel->group_id != $site_setting_group_id) {
            return $this->error('Cài đặt không thuộc nhóm này', 404);
        }

        if (empty($request->all())) {
            return $this->error('Không có dữ liệu để cập nhật', 422);
        }
        $attributes = $request->all();
        $setting = $this->service->updateAttributesWithValidation($id, $attributes, UpdateSiteSettingRequest::class);

        return $this->success(new SiteSettingResource($setting), 'Cập nhật thuộc tính cài đặt thành công.');
    }

    /**
     * Xóa một cài đặt thuộc nhóm cụ thể.
     *
     * @urlParam site_setting_group_id integer required ID của nhóm cài đặt. Example: 1
     * @urlParam id integer required ID của cài đặt. Example: 1
     *
     * @param int $site_setting_group_id ID của nhóm cài đặt
     * @param int $id ID của cài đặt
     * @return JsonResponse
     */
    public function destroy(int $site_setting_group_id, int $id): JsonResponse
    {
        // Kiểm tra cài đặt có thuộc nhóm này không
        $settingModel = $this->service->read($id);
        if (! $settingModel) {
            return $this->error(message: "Không tìm thấy {$this->service->getModelName()} với ID là {$id}");
        }

        if ($settingModel->group_id != $site_setting_group_id) {
            return $this->error('Cài đặt không thuộc nhóm này', 404);
        }

        $deleted = $this->service->delete($id);

        if (! $deleted) {
            return $this->error(message: "Không thể xóa cài đặt với ID là {$id}");
        }

        return $this->successNoContent(message: 'Đã xoá cài đặt thành công');
    }
}
