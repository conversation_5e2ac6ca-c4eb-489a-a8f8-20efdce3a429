<?php

namespace App\Models\Concerns;

use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Trait chứa logic chung cho các base model.
 */
trait HasBaseModelLogic
{
    use SoftDeletes;

    /**
     * <PERSON><PERSON> đè phương thức getCasts của Laravel để hợp nhất các casts c<PERSON> sở và casts của model con.
     *
     * @return array
     */
    public function getCasts()
    {
        // Hợp nhất các casts từ phương thức getBaseCasts() và getModelCasts()
        return array_merge($this->getBaseCasts(), $this->getModelCasts());
    }

    /**
     * C<PERSON> cấp các casts c<PERSON> sở được áp dụng cho mọi model.
     *
     * @return array
     */
    protected function getBaseCasts(): array
    {
        return [
            'created_at' => 'datetime:Y-m-d H:i:s',
            'updated_at' => 'datetime:Y-m-d H:i:s',
            'deleted_at' => 'datetime:Y-m-d H:i:s',
        ];
    }

    /**
     * <PERSON><PERSON><PERSON><PERSON> thức hook để các model con đị<PERSON> ng<PERSON><PERSON><PERSON> các casts riêng.
     * Mặc định trả về một mảng trống.
     *
     * @return array
     */
    protected function getModelCasts(): array
    {
        return [];
    }
}
