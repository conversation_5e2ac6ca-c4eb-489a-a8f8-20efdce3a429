<?php

namespace App\Resources\SiteSettingGroup;

use App\Resources\BaseResource;
use App\Resources\SiteSettings\SiteSettingResource;

/**
 * Resource cho nhóm cài đặt hệ thống
 */
class SiteSettingGroupResource extends BaseResource
{
    /**
     * Dữ liệu đặc biệt cho resource (relationships, computed properties)
     *
     * @param \Illuminate\Http\Request $request
     * @return array<string, mixed>
     */
    protected function resourceData($request): array
    {
        return [
            'siteSettings' => SiteSettingResource::collection($this->whenLoaded('siteSettings')),
        ];
    }
}
