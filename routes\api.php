<?php

use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\CardPrintController;
use App\Http\Controllers\Api\V1\CategoryController;
use App\Http\Controllers\Api\V1\DataLinkableController;
use App\Http\Controllers\Api\V1\GalleryGroupController;
use App\Http\Controllers\Api\V1\GalleryItemController;
use App\Http\Controllers\Api\V1\MenuController;
use App\Http\Controllers\Api\V1\MenuItemController;
use App\Http\Controllers\Api\V1\OverviewController;
use App\Http\Controllers\Api\V1\PostController;
use App\Http\Controllers\Api\V1\Public\CardController as PublicCardController;
use App\Http\Controllers\Api\V1\Public\CategoryController as PublicCategoryController;
use App\Http\Controllers\Api\V1\Public\GalleryGroupController as PublicGalleryGroupController;
use App\Http\Controllers\Api\V1\Public\GameController as PublicGameController;
use App\Http\Controllers\Api\V1\Public\MenuItemController as PublicMenuItemController;
use App\Http\Controllers\Api\V1\Public\PostController as PublicPostController;
use App\Http\Controllers\Api\V1\Public\SiteSettingGroupController as PublicSiteSettingGroupController;
use App\Http\Controllers\Api\V1\Public\StaticPageController as PublicStaticPageController;
use App\Http\Controllers\Api\V1\RoleController;
use App\Http\Controllers\Api\V1\SiteSettingController;
use App\Http\Controllers\Api\V1\SiteSettingGroupController;
use App\Http\Controllers\Api\V1\StaticPageController;
use App\Http\Controllers\Api\V1\UserController;
use App\Http\Controllers\Api\V1\UserLogController;
use App\Http\Controllers\HealthController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Định nghĩa các route cho API
|--------------------------------------------------------------------------
|
| Đây là nơi bạn đăng ký các route cho API của ứng dụng.
| Các route này được load bởi RouteServiceProvider và đều sử dụng group "api".
| Hãy định nghĩa endpoint API của bạn tại đây!
|
*/

/**
 * @group Hệ thống
 */
Route::get('/health', [HealthController::class, 'check'])
    ->middleware('scribe.auth')
    ->withoutMiddleware('jwt');

Route::prefix('v1')->group(function () {
    Route::group(['prefix' => '/auth', 'controller' => AuthController::class], function () {
        /**
         * @group Xác thực
         */
        Route::post('/login', 'login');
    });

    Route::group(['middleware' => 'jwt'], function () {
        Route::group(['prefix' => '/auth', 'controller' => AuthController::class], function () {
            /**
             * @group Xác thực
             */
            Route::get('/me', 'me');
            /**
             * @group Xác thực
             */
            Route::post('/logout', 'logout');
            /**
             * @group Xác thực
             */
            Route::post('/update-profile', 'updateProfile');
            Route::put('/change-password', 'changePassword');
        });

        Route::group(['prefix' => 'statistics'], function () {
            /**
             * @group Thống kê - Tổng quan
             */
            Route::group(['prefix' => 'overview', 'controller' => OverviewController::class], function () {
                Route::get('/', 'index')->middleware('permission:dashboard.view');
                Route::get('/by-date', 'getOverviewByDate')->middleware('permission:dashboard.view');
                Route::get('/daily', 'dailyStatistics')->middleware('permission:dashboard.view');
                Route::get('/revenue', 'revenueStatistics')->middleware('permission:dashboard.view');
            });
        });

        Route::prefix('cms')->group(function () {
            /**
             * @group Cài đặt trang web - Group
             */
            Route::group(['prefix' => 'site-setting-groups', 'as' => 'site-setting-groups.', 'controller' => SiteSettingGroupController::class], function () {
                Route::get('/', 'index')->name('index')->middleware('permission:site_setting_group_management.view');
                Route::post('/', 'store')->name('store')->middleware('permission:site_setting_group_management.create');
                Route::get('/{id}', 'show')->name('show')->middleware('permission:site_setting_group_management.view');
                Route::put('/{id}', 'update')->name('update')->middleware('permission:site_setting_group_management.edit');
                Route::delete('/{id}', 'destroy')->name('destroy')->middleware('permission:site_setting_group_management.delete');
            });

            /**
             * @group Cài đặt trang web
             */
            Route::group(['prefix' => 'site-setting-groups/{site_setting_group_id}/site-settings', 'as' => 'site-setting-groups.site-settings.', 'controller' => SiteSettingController::class], function () {
                Route::get('/', 'index')->name('index')->middleware('permission:site_setting_management.view');
                Route::post('/', 'store')->name('store')->middleware('permission:site_setting_management.create');
                Route::get('/{id}', 'show')->name('show')->middleware('permission:site_setting_management.view');
                Route::put('/{id}', 'update')->name('update')->middleware('permission:site_setting_management.edit');
                Route::delete('/{id}', 'destroy')->name('destroy')->middleware('permission:site_setting_management.delete');
                Route::patch('/{id}/attributes', 'updateAttributes')->name('update-attributes')->middleware('permission:site_setting_management.edit');
            });

            /**
             * @group Danh mục
             */
            Route::group(['prefix' => 'categories', 'as' => 'categories.', 'controller' => CategoryController::class], function () {
                Route::get('/', 'index')->name('index')->middleware('permission:category_management.view');
                Route::post('/', 'store')->name('store')->middleware('permission:category_management.create');
                Route::get('/{id}', 'show')->name('show')->middleware('permission:category_management.view');
                Route::put('/{id}', 'update')->name('update')->middleware('permission:category_management.edit');
                Route::delete('/{id}', 'destroy')->name('destroy')->middleware('permission:category_management.delete');
            });

            /**
             * @group Menu - Group
             */
            Route::group(['prefix' => 'menus', 'as' => 'menus.', 'controller' => MenuController::class], function () {
                Route::get('/', 'index')->name('index')->middleware('permission:menu_management.view');
                Route::post('/', 'store')->name('store')->middleware('permission:menu_management.create');
                Route::get('/{id}', 'show')->name('show')->middleware('permission:menu_management.view');
                Route::put('/{id}', 'update')->name('update')->middleware('permission:menu_management.edit');
                Route::delete('/{id}', 'destroy')->name('destroy')->middleware('permission:menu_management.delete');
            });

            /**
             * @group Menu - Item
             */
            Route::group(['prefix' => 'menus/{menu_id}/items', 'as' => 'menus.items.', 'controller' => MenuItemController::class], function () {
                Route::get('/', 'index')->name('index')->middleware('permission:menu_item_management.view');
                Route::post('/', 'store')->name('store')->middleware('permission:menu_item_management.create');
                Route::get('/{id}', 'show')->name('show')->middleware('permission:menu_item_management.view');
                Route::put('/{id}', 'update')->name('update')->middleware('permission:menu_item_management.edit');
                Route::delete('/{id}', 'destroy')->name('destroy')->middleware('permission:menu_item_management.delete');
            });

            /**
             * @group Menu - Data Linkables
             */
            Route::get('data-linkables', [DataLinkableController::class, 'index'])
                ->middleware('permission:menu_item_management.view|menu_item_management.create|menu_item_management.edit');

            /**
             * @group Bài viết
             */
            Route::group(['prefix' => 'posts', 'as' => 'posts.', 'controller' => PostController::class], function () {
                Route::get('/', 'index')->name('index')->middleware('permission:post_management.view');
                Route::post('/', 'store')->name('store')->middleware('permission:post_management.create');
                Route::get('/{id}', 'show')->name('show')->middleware('permission:post_management.view');
                Route::put('/{id}', 'update')->name('update')->middleware('permission:post_management.edit');
                Route::delete('/{id}', 'destroy')->name('destroy')->middleware('permission:post_management.delete');
                Route::patch('/{id}/attributes', 'updateAttributes')->name('update-attributes')->middleware('permission:post_management.edit');
            });

            /**
             * @group Thư viện ảnh - Nhóm
             */
            Route::group(['prefix' => 'gallery-groups', 'as' => 'gallery-groups.', 'controller' => GalleryGroupController::class], function () {
                Route::get('/', 'index')->name('index')->middleware('permission:gallery_group_management.view');
                Route::post('/', 'store')->name('store')->middleware('permission:gallery_group_management.create');
                Route::get('/{id}', 'show')->name('show')->middleware('permission:gallery_group_management.view');
                Route::put('/{id}', 'update')->name('update')->middleware('permission:gallery_group_management.edit');
                Route::delete('/{id}', 'destroy')->name('destroy')->middleware('permission:gallery_group_management.delete');
            });

            /**
             * @group Thư viện ảnh - Ảnh
             */
            Route::group(['prefix' => 'gallery-groups/{gallery_group_id}/items', 'as' => 'gallery-groups.items.', 'controller' => GalleryItemController::class], function () {
                Route::get('/', 'index')->name('index')->middleware('permission:gallery_item_management.view');
                Route::post('/', 'store')->name('store')->middleware('permission:gallery_item_management.create');
                Route::get('/{id}', 'show')->name('show')->middleware('permission:gallery_item_management.view');
                Route::put('/{id}', 'update')->name('update')->middleware('permission:gallery_item_management.edit');
                Route::delete('/{id}', 'destroy')->name('destroy')->middleware('permission:gallery_item_management.delete');
            });

            /**
             * @group Trang tĩnh
             */
            Route::group(['prefix' => 'static-pages', 'as' => 'static-pages.', 'controller' => StaticPageController::class], function () {
                Route::get('/', 'index')->name('index')->middleware('permission:static_page_management.view');
                Route::post('/', 'store')->name('store')->middleware('permission:static_page_management.create');
                Route::get('/{id}', 'show')->name('show')->middleware('permission:static_page_management.view');
                Route::put('/{id}', 'update')->name('update')->middleware('permission:static_page_management.edit');
                Route::delete('/{id}', 'destroy')->name('destroy')->middleware('permission:static_page_management.delete');
            });

            /**
             * @group Lịch sử hoạt động
             */
            Route::group(['prefix' => 'user-logs', 'as' => 'user-logs.', 'controller' => UserLogController::class], function () {
                Route::get('/', 'index')->name('index')->middleware('permission:user_log_management.view');
            });
        });

        /**
         * @group Người dùng - Quản lý người dùng
         */
        Route::group(['prefix' => 'users', 'controller' => UserController::class], function () {
            Route::get('/', 'index')->middleware('permission:user_management.view');
            Route::get('/{id}', 'show')->middleware('permission:user_management.view');
            Route::post('/', 'store')->middleware('permission:user_management.create');
            Route::post('/{id}', 'update')->middleware('permission:user_management.edit');
            Route::put('/{id}/status', 'updateStatus')->middleware('permission:user_management.edit');
            Route::put('/{id}/change-password', 'changePassword')->middleware('permission:user_management.edit');
            Route::delete('/{id}', 'destroy')->middleware('permission:user_management.delete');
        });

        /**
         * @group Vai trò - Quản lý vai trò
         */
        Route::group(['prefix' => 'roles', 'controller' => RoleController::class], function () {
            Route::get('/', 'index')->middleware('permission:role_management.view');
            Route::get('/{id}', 'show')->middleware('permission:permission_management.view');
            Route::post('/', 'store')->middleware('permission:role_management.create');
            Route::post('/{id}', 'update')->middleware('permission:role_management.edit');
            Route::delete('/{id}', 'destroy')->middleware('permission:role_management.delete');
            Route::post('/{id}/assign-permissions', 'assignPermissions')->middleware('permission:permission_management.edit');
            Route::post('/{id}/revoke-permissions', 'revokePermissions')->middleware('permission:permission_management.edit');
        });

        /**
         * @group Giao dịch
         */
        Route::group(['prefix' => 'transactions'], function () {
            /**
             * @group thẻ Mcoin - Quản lý thẻ Mcoin
             */
            Route::group(['prefix' => 'card-prints', 'as' => 'card-prints.', 'controller' => CardPrintController::class], function () {
                Route::get('/', 'index')->name('index')->middleware('permission:card_print_management.view');
                Route::post('/', 'store')->name('store')->middleware('permission:card_print_management.create');
                Route::put('/cancel/{id}', 'cancelCard')->middleware('permission:card_print_management.edit');
            });
        });

        // Group data endpoints
        Route::prefix('data')->group(function () {
            Route::get('/all-roles', [RoleController::class, 'allRoles']);
        });
    });

    /*
    |--------------------------------------------------------------------------
    | Public API Routes (v1)
    |--------------------------------------------------------------------------
    |
    | Các route này dành cho phía client (trang chủ, app mobile...)
    | và không yêu cầu xác thực JWT.
    |
    */
    Route::prefix('public')
        ->middleware('public.api.auth')
        ->name('public.v1.')
        ->group(function () {
            /**
             * @group Public - Posts
             */
            Route::get('posts', [PublicPostController::class, 'index'])->name('posts.index');

            /**
             * @group Public - Posts
             */
            Route::get('posts/{slug}', [PublicPostController::class, 'show'])->name('posts.show');

            /**
             * @group Public - Categories
             */
            Route::get('categories', [PublicCategoryController::class, 'index'])->name('categories.index');

            /**
             * @group Public - Categories
             */
            Route::get('categories/{slug}', [PublicCategoryController::class, 'show'])->name('categories.show');

            /**
             * @group Public - Menu Items
             */
            Route::get('menus/{menu_location_key}', [PublicMenuItemController::class, 'listByMenuLocationKey'])->name('menus.list-by-menu-location-key');

            /**
             * @group Public - Static Pages
             */
            Route::get('static-pages/{slug}', [PublicStaticPageController::class, 'show'])->name('static-pages.show');

            /**
             * @group Public - Galleries
             */
            Route::get('galleries/{location_key}', [PublicGalleryGroupController::class, 'showByLocationKey'])->name('galleries.show-by-key');

            /**
             * @group Public - Site Settings
             */
            Route::get('site-settings/{group_key}', [PublicSiteSettingGroupController::class, 'showByGroupKey'])->name('site-settings.show-by-group-key');

            /**
             * @group Public - Card
             */
            Route::post('cards/verify', [PublicCardController::class, 'verify'])->name('cards.verify');

            /**
             * @group Public - Game
             */
            Route::get('game-info', [PublicGameController::class, 'getFirstGameWithPackages'])->name('games.first-with-packages');
        });
});
