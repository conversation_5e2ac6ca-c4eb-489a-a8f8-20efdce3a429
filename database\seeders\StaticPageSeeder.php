<?php

namespace Database\Seeders;

use App\Models\StaticPage;
use App\Models\User;
use Illuminate\Database\Seeder;

class StaticPageSeeder extends Seeder
{
    /**
     * Chạy seeder tạo dữ liệu mẫu cho trang tĩnh.
     */
    public function run(): void
    {
        // Lấy admin user để gán vào created_by và updated_by
        $admin = User::where('email', '<EMAIL>')->first();

        if (! $admin) {
            $this->command->error('Không tìm thấy admin user! Hãy chạy UserSeeder trước.');

            return;
        }

        // Danh sách các trang tĩnh mẫu
        $staticPages = [
            [
                'title' => 'Giới thiệu',
                'slug' => 'gioi-thieu',
                'body' => '<h1>Giới thiệu về Kiếm Hiệp Tình 1</h1><p>Kiếm Hiệp Tình 1 là tựa game nhập vai trực tuyến đa nền tảng với bối cảnh võ hiệp Trung Hoa cổ đại...</p>',
                'meta_title' => 'Giới thiệu về Kiếm Hiệp Tình 1',
                'meta_description' => 'Tìm hiểu về tựa game nhập vai trực tuyến Kiếm Hiệp Tình 1',
                'status' => 1,
            ],
            [
                'title' => 'Điều khoản sử dụng',
                'slug' => 'dieu-khoan-su-dung',
                'body' => '<h1>Điều khoản sử dụng</h1><p>Khi sử dụng dịch vụ của Kiếm Hiệp Tình 1, bạn đồng ý với các điều khoản sau đây...</p>',
                'meta_title' => 'Điều khoản sử dụng - Kiếm Hiệp Tình 1',
                'meta_description' => 'Điều khoản sử dụng dịch vụ game Kiếm Hiệp Tình 1',
                'status' => 1,
            ],
            [
                'title' => 'Chính sách bảo mật',
                'slug' => 'chinh-sach-bao-mat',
                'body' => '<h1>Chính sách bảo mật</h1><p>Chúng tôi cam kết bảo vệ thông tin cá nhân của người chơi...</p>',
                'meta_title' => 'Chính sách bảo mật - Kiếm Hiệp Tình 1',
                'meta_description' => 'Chính sách bảo mật thông tin người dùng của Kiếm Hiệp Tình 1',
                'status' => 1,
            ],
            [
                'title' => 'Hướng dẫn thanh toán',
                'slug' => 'huong-dan-thanh-toan',
                'body' => '<h1>Hướng dẫn thanh toán</h1><p>Các phương thức thanh toán được hỗ trợ trong game Kiếm Hiệp Tình 1...</p>',
                'meta_title' => 'Hướng dẫn thanh toán - Kiếm Hiệp Tình 1',
                'meta_description' => 'Hướng dẫn các phương thức thanh toán trong game Kiếm Hiệp Tình 1',
                'status' => 1,
            ],
            [
                'title' => 'Câu hỏi thường gặp',
                'slug' => 'cau-hoi-thuong-gap',
                'body' => '<h1>Câu hỏi thường gặp (FAQ)</h1><p>Danh sách các câu hỏi và giải đáp thường gặp khi chơi Kiếm Hiệp Tình 1...</p>',
                'meta_title' => 'FAQ - Câu hỏi thường gặp - Kiếm Hiệp Tình 1',
                'meta_description' => 'Giải đáp các câu hỏi thường gặp khi chơi game Kiếm Hiệp Tình 1',
                'status' => 1,
            ],
        ];

        // Tạo các trang tĩnh
        foreach ($staticPages as $pageData) {
            StaticPage::firstOrCreate(
                ['slug' => $pageData['slug']],
                [
                    'title' => $pageData['title'],
                    'body' => $pageData['body'],
                    'meta_title' => $pageData['meta_title'],
                    'meta_description' => $pageData['meta_description'],
                    'status' => $pageData['status'],
                    'created_by' => $admin->id,
                    'updated_by' => $admin->id,
                ]
            );
        }

        $this->command->info('✅ Dữ liệu mẫu cho trang tĩnh đã được tạo thành công!');
    }
}
