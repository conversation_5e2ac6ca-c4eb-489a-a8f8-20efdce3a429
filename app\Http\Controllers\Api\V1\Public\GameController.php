<?php

namespace App\Http\Controllers\Api\V1\Public;

use App\Http\Controllers\Api\V1\BaseController;
use App\Services\GameService;
use Illuminate\Http\JsonResponse;

/**
 * @group Public - Game
 */
class GameController extends BaseController
{
    public function __construct(protected GameService $gameService)
    {
    }

    /**
     * Get the first game with its packages.
     *
     * @return JsonResponse
     */
    public function getFirstGameWithPackages(): JsonResponse
    {
        $result = $this->gameService->getFirstGameWithPackages();

        return $result['success']
            ? $this->success($result['data'], $result['message'])
            : $this->error($result['message'], $result['code']);
    }
}
