<?php

namespace App\Services;

use App\Repositories\Interfaces\CategoryRepositoryInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Class CategoryService.
 * Chứa logic nghiệp vụ cho việc quản lý Category.
 */
class CategoryService extends BaseCrudService
{
    /**
     * Service xử lý upload file
     *
     * @var FileUploadService
     */
    protected FileUploadService $fileUploadService;

    /**
     * Khởi tạo service
     */
    public function __construct(FileUploadService $fileUploadService)
    {
        parent::__construct();
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Trả về tên class của repository.
     *
     * @return string
     */
    protected function getRepositoryClass(): string
    {
        return CategoryRepositoryInterface::class;
    }

    /**
     * Tạo danh mục mới
     * Hỗ trợ upload file hoặc sử dụng URL có sẵn cho meta_image
     *
     * @param array $data Dữ liệu đầu vào
     * @param array $with Các quan hệ cần eager load
     * @return Model
     */
    public function create(array $data, array $with = []): Model
    {
        // Xử lý meta_image nếu là file upload
        if (isset($data['meta_image']) && $data['meta_image'] instanceof UploadedFile) {
            // Upload file và lấy thông tin
            $fileInfo = $this->handleImageUpload($data['meta_image']);

            // Cập nhật đường dẫn vào data
            $data['meta_image'] = $fileInfo['data']['url'];
        }
        // Nếu không phải file upload thì giữ nguyên giá trị (string URL)

        return parent::create($data, $with);
    }

    /**
     * Cập nhật danh mục
     * Hỗ trợ upload file mới hoặc sử dụng URL mới cho meta_image
     *
     * @param int $id ID của danh mục cần cập nhật
     * @param array $data Dữ liệu đầu vào
     * @param array $with Các quan hệ cần eager load
     * @return Model
     */
    public function update($id, array $data, array $with = []): Model
    {
        // Xử lý meta_image nếu là file upload
        if (isset($data['meta_image']) && $data['meta_image'] instanceof UploadedFile) {
            // Lấy đường dẫn file hiện tại (nếu có)
            $oldImageUrl = $this->repository->find($id)->meta_image;

            // Upload file và lấy thông tin
            $fileInfo = $this->handleImageUpload($data['meta_image'], $oldImageUrl);

            // Cập nhật đường dẫn vào data
            $data['meta_image'] = $fileInfo['data']['url'];
        }
        // Nếu không phải file upload thì giữ nguyên giá trị (string URL)

        return parent::update($id, $data, $with);
    }

    /**
     * Xử lý upload ảnh meta_image
     *
     * @param UploadedFile $file File ảnh upload
     * @param string|null $oldImageUrl URL của ảnh cũ (nếu có) để xóa
     * @return array Kết quả upload
     */
    protected function handleImageUpload(UploadedFile $file, $oldImageUrl = null): array
    {
        return $this->fileUploadService->uploadImage(
            $file,
            'categories',  // Tên bảng
            'public',      // Disk
            $oldImageUrl,  // URL của ảnh cũ (nếu có)
            [             // Options bổ sung
                'max_width' => 1200,
                'max_height' => 630,
            ]
        );
    }

    /**
     * Lấy danh sách các danh mục công khai.
     *
     * @param Request $request
     * @return LengthAwarePaginator
     */
    public function getPublicCategories(Request $request)
    {
        $request->merge(['status' => 1]);

        if (! $request->has('sort_by')) {
            $request->merge(['sort_by' => 'order', 'sort_order' => 'asc']);
        }

        return $this->list($request, );
    }

    /**
     * Lấy chi tiết danh mục và các bài viết liên quan.
     *
     * @param string $slug Slug của danh mục.
     * @param Request $request
     * @return Model
     * @throws ModelNotFoundException Nếu không tìm thấy danh mục.
     */
    public function getCategoryDetailWithPosts(string $slug, Request $request)
    {
        $category = $this->repository->findByOrFail('slug', $slug);

        $category->load(['posts' => function ($query) use ($request) {
            $query->where('status', 'published')->orderBy('published_at', 'desc')->paginate($request->input('limit', 10));
        }]);

        return $category;
    }
}
