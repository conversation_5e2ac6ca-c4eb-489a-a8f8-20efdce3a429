<?php

namespace App\Http\Controllers\Api\V1\Public;

use App\Http\Controllers\Api\V1\BaseController;
use App\Resources\Public\Categories\CategoryResource;
use App\Services\CategoryService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Public - Categories
 *
 * API công khai để lấy dữ liệu danh mục
 */
class CategoryController extends BaseController
{
    /**
     * Khởi tạo controller với service
     *
     * @param CategoryService $service
     */
    public function __construct(protected CategoryService $service)
    {
    }

    /**
     * Lấy danh sách danh mục công khai
     *
     * Lấy danh sách các danh mục có trạng thái 'active', hỗ trợ sắp xếp và phân trang.
     *
     * @param Request $request
     * @header X-Public-Api-Secret string required Khóa bí mật API công khai. Example: your_super_secret_key_here
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $categories = $this->service->getPublicCategories($request);

        return $this->success(CategoryResource::collection($categories), 'Lấy danh sách danh mục thành công.');
    }

    /**
     * Lấy chi tiết danh mục và các bài viết
     *
     * Hiển thị thông tin chi tiết của một danh mục và danh sách các bài viết đã xuất bản thuộc danh mục đó.
     *
     * @urlParam slug string required Slug của danh mục. Example: tin-tuc
     *
     * @param string $slug Slug của danh mục
     * @param Request $request
     * @header X-Public-Api-Secret string required Khóa bí mật API công khai. Example: your_super_secret_key_here
     * @return JsonResponse
     */
    public function show(string $slug, Request $request): JsonResponse
    {
        try {
            $category = $this->service->getCategoryDetailWithPosts($slug, $request);

            return $this->success(new CategoryResource($category), 'Lấy thông tin danh mục thành công.');
        } catch (ModelNotFoundException $e) {
            return $this->error('Không tìm thấy danh mục.', 404);
        }
    }
}
