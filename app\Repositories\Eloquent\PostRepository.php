<?php

namespace App\Repositories\Eloquent;

use App\Models\Post;
use App\Repositories\Interfaces\PostRepositoryInterface;

class PostRepository extends BaseRepository implements PostRepositoryInterface
{
    protected array $searchableFields = ['title', 'slug', 'excerpt', 'body', 'meta_title', 'meta_description', 'meta_keywords'];

    protected function getModelClass(): string
    {
        return Post::class;
    }
}
