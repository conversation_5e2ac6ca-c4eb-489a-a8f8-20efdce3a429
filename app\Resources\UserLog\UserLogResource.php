<?php

namespace App\Resources\UserLog;

use App\Resources\BaseResource;

class UserLogResource extends BaseResource
{
    /**
     * Dữ liệu đặc biệt cho resource (relationships, computed properties)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    protected function resourceData($request): array
    {
        return [
            // Load relationships khi có eager loading
            'user' => $this->whenLoaded(
                'user',
                fn () => [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                ]
            ),
            'actor' => $this->whenLoaded(
                'actor',
                fn () => [
                    'id' => $this->actor->id,
                    'name' => $this->actor->name,
                ]
            ),
        ];
    }
}
