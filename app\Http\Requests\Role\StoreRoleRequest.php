<?php

namespace App\Http\Requests\Role;

use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class StoreRoleRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'min:2',
                'max:255',
                Rule::unique('roles', 'name'),
            ],
            'guard_name' => [
                'sometimes',
                'string',
                Rule::in(['api', 'web']),
            ],
            'description' => [
                'nullable',
                'string',
                'max:500',
            ],
            'is_protected' => [
                'sometimes',
                'boolean',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Tên vai trò là bắt buộc',
            'name.string' => 'Tên vai trò phải là chuỗi ký tự',
            'name.min' => 'Tên vai trò phải có ít nhất 2 ký tự',
            'name.max' => 'Tên vai trò không được vượt quá 255 ký tự',
            'name.unique' => 'Tên vai trò đã tồn tại',
            'guard_name.string' => 'Guard name phải là chuỗi ký tự',
            'guard_name.in' => 'Guard name phải là api hoặc web',
            'description.string' => 'Mô tả phải là chuỗi ký tự',
            'description.max' => 'Mô tả không được quá 500 ký tự',
            'is_protected.boolean' => 'Trạng thái bảo vệ phải là boolean',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'tên vai trò',
            'guard_name' => 'guard name',
            'description' => 'mô tả',
            'is_protected' => 'trạng thái bảo vệ',
        ];
    }

    /**
     * Cung cấp thông tin mô tả cho các tham số yêu cầu trong API
     */
    public function bodyParameters(): array
    {
        return [
            'name' => [
                'description' => 'Tên của vai trò mới (2-255 ký tự)',
                'example' => 'Quản trị viên',
                'required' => true,
            ],
            'guard_name' => [
                'description' => 'Guard name cho vai trò (api hoặc web)',
                'example' => 'api',
                'required' => false,
            ],
            'description' => [
                'description' => 'Mô tả vai trò (tối đa 500 ký tự)',
                'example' => 'Vai trò quản trị viên hệ thống với đầy đủ quyền hạn',
                'required' => false,
            ],
            'is_protected' => [
                'description' => 'Trạng thái bảo vệ vai trò (true/false)',
                'example' => false,
                'required' => false,
            ],
        ];
    }
}
