<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ThirdPartyService extends BaseService
{
    /**
     * @var string Base URL for the third-party API
     */
    protected string $baseUrl;

    /**
     * @var string API key for authentication
     */
    protected string $apiKey;

    /**
     * @var int Request timeout in seconds
     */
    protected int $timeout;

    /**
     * @var bool Whether to enable caching
     */
    protected bool $enableCache;

    /**
     * @var int Cache TTL in minutes
     */
    protected int $cacheTtl;

    public function __construct()
    {
        $this->baseUrl = config('services.third_party.base_url', 'https://api.example.com');
        $this->apiKey = config('services.third_party.api_key', '');
        $this->timeout = config('services.third_party.timeout', 30);
        $this->enableCache = config('services.third_party.cache.enabled', true);
        $this->cacheTtl = config('services.third_party.cache.ttl', 60);
    }

    /**
     * Make an HTTP request to the third-party API
     */
    public function makeRequest(string $method, string $endpoint, array $data = [], array $headers = []): array
    {
        $cacheKey = $this->generateCacheKey($endpoint, $data);

        // Try to get cached response if enabled
        if ($this->enableCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            $url = rtrim($this->baseUrl, '/') . '/' . ltrim($endpoint, '/');

            $defaultHeaders = [
                'Accept' => 'application/json',
                'Authorization' => 'Bearer ' . $this->apiKey,
                'X-Service-Name' => config('app.name', 'internal-api'),
            ];

            $response = Http::withHeaders(array_merge($defaultHeaders, $headers))
                ->timeout($this->timeout)
                ->$method($url, $data);

            if ($response->successful()) {
                $result = $response->json();

                // Cache the successful response
                if ($this->enableCache) {
                    Cache::put($cacheKey, $result, now()->addMinutes($this->cacheTtl));
                }

                return $result;
            }

            throw new Exception("API request failed with status: " . $response->status());

        } catch (Exception $e) {
            Log::error('ThirdParty API request failed', [
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
                'trace' => config('app.debug') ? $e->getTraceAsString() : null,
            ]);

            Log::error('Third-party API request failed', [
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
                'trace' => config('app.debug') ? $e->getTraceAsString() : null,
            ]);

            throw $e;
        }
    }

    /**
     * Generate a cache key based on endpoint and parameters
     */
    private function generateCacheKey(string $endpoint, array $params = []): string
    {
        $key = 'third_party_' . md5($endpoint . json_encode($params));

        return str_replace(['/', '\\', '?', '%', '*', ':', '|', '"', '<', '>'], '_', $key);
    }
}
