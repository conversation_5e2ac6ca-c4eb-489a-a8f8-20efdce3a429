<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ThirdPartyService extends BaseService
{
    /**
     * @var string Base URL for the third-party API
     */
    protected string $baseUrl;

    /**
     * @var string API key for authentication
     */
    protected string $apiKey;

    /**
     * @var int Request timeout in seconds
     */
    protected int $timeout;

    /**
     * @var bool Whether to enable caching
     */
    protected bool $enableCache;

    /**
     * @var int Cache TTL in minutes
     */
    protected int $cacheTtl;

    public function __construct()
    {
        $this->baseUrl = config('services.third_party.base_url', 'https://api.example.com');
        $this->apiKey = config('services.third_party.api_key', '');
        $this->timeout = config('services.third_party.timeout', 30);
        $this->enableCache = config('services.third_party.cache.enabled', true);
        $this->cacheTtl = config('services.third_party.cache.ttl', 60);
    }

    /**
     * Thực hiện gọi GET tới API bên thứ ba
     * Phương thức GET – truyền query vào tham số thứ 2
     *
     * @param string $endpoint
     * @param array $query
     * @param array $headers
     * @return array
     * @throws Exception
     */
    public function get(string $endpoint, array $query = [], array $headers = []): array
    {
        return $this->sendRequest('get', $endpoint, $query, $headers);
    }

    /**
    * Thực hiện gọi POST tới API bên thứ ba
    * Phương thức POST – truyền body vào tham số thứ 2 (JSON theo mặc định của Http client)
    *
    * @param string $endpoint
    * @param array $data
    * @param array $headers
    * @return array
    * @throws Exception
    */
    public function post(string $endpoint, array $data = [], array $headers = []): array
    {
        return $this->sendRequest('post', $endpoint, $data, $headers);
    }

    /**
     * Make an HTTP request to the third-party API
     * (Tương thích ngược) – Hạn chế dùng trực tiếp. Hãy dùng get()/post() cho rõ ràng.
     *
     * @deprecated Sử dụng get() hoặc post() thay thế để tránh nhập nhằng.
     */
    public function makeRequest(string $method, string $endpoint, array $data = [], array $headers = []): array
    {
        // Điều hướng tới phương thức cụ thể cho rõ ràng
        $method = strtolower($method);
        if ($method === 'get') {
            return $this->get($endpoint, $data, $headers);
        }
        if ($method === 'post') {
            return $this->post($endpoint, $data, $headers);
        }

        // Trường hợp khác, vẫn gửi theo method tự do (ít dùng)
        return $this->sendRequest($method, $endpoint, $data, $headers);
    }

    /**
     * Generate a cache key based on endpoint and parameters
     * Tạo cache key dựa trên endpoint và tham số
     */
    private function generateCacheKey(string $endpoint, array $params = []): string
    {
        $key = 'third_party_' . md5($endpoint . json_encode($params));

        return str_replace(['/', '\\', '?', '%', '*', ':', '|', '"', '<', '>'], '_', $key);
    }

    /**
     * Gửi request tới API bên thứ ba, gom phần dùng chung cho GET/POST
     * Hàm nội bộ, xử lý URL, header, timeout, cache và lỗi
     *
     * @param string $method 'get' | 'post' | method khác
     * @param string $endpoint
     * @param array $params
     * @param array $headers
     * @return array
     * @throws Exception
     */
    private function sendRequest(string $method, string $endpoint, array $params = [], array $headers = []): array
    {
        $cacheKey = $this->generateCacheKey($endpoint, $params);

        // Lấy cache nếu có bật cache
        if ($this->enableCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            $url = rtrim($this->baseUrl, '/') . '/' . ltrim($endpoint, '/');

            $defaultHeaders = [
                'Accept' => 'application/json',
                'Authorization' => 'Bearer ' . $this->apiKey,
                'X-Service-Name' => config('app.name', 'internal-api'),
            ];

            $http = Http::withHeaders(array_merge($defaultHeaders, $headers))
                ->timeout($this->timeout);

            $method = strtolower($method);
            if ($method === 'get') {
                $response = $http->get($url, $params);
            } elseif ($method === 'post') {
                $response = $http->post($url, $params);
            } else {
                $response = $http->send(strtoupper($method), $url, ['json' => $params]);
            }

            if ($response->successful()) {
                $result = $response->json();

                if ($this->enableCache) {
                    Cache::put($cacheKey, $result, now()->addMinutes($this->cacheTtl));
                }

                return $result;
            }

            throw new Exception('API request failed with status: ' . $response->status());
        } catch (Exception $e) {
            Log::error('Third-party API request failed', [
                'endpoint' => $endpoint,
                'method' => $method,
                'error' => $e->getMessage(),
                'trace' => config('app.debug') ? $e->getTraceAsString() : null,
            ]);

            throw $e;
        }
    }
}
