<?php

namespace App\Enums;

enum TimePeriod: int
{
    case TODAY = 1;
    case YESTERDAY = 2;
    case LAST_7_DAYS = 3;
    case LAST_30_DAYS = 4;
    case THIS_MONTH = 5;
    case LAST_MONTH = 6;

    public function getLabel(): string
    {
        return match($this) {
            self::TODAY => 'Hôm nay',
            self::YESTERDAY => 'Hôm qua',
            self::LAST_7_DAYS => '7 Ngày trước',
            self::LAST_30_DAYS => '30 Ngày trước',
            self::THIS_MONTH => 'Tháng này',
            self::LAST_MONTH => 'Tháng trước',
        };
    }

    public function getDateRange(): array
    {
        $today = now();

        return match($this) {
            self::TODAY => [
                'start_date' => $today->format('Y-m-d'),
                'end_date' => $today->format('Y-m-d'),
            ],
            self::YESTERDAY => [
                'start_date' => $today->subDay()->format('Y-m-d'),
                'end_date' => $today->subDay()->format('Y-m-d'),
            ],
            self::LAST_7_DAYS => [
                'start_date' => $today->subDays(7)->format('Y-m-d'),
                'end_date' => $today->format('Y-m-d'),
            ],
            self::LAST_30_DAYS => [
                'start_date' => $today->subDays(30)->format('Y-m-d'),
                'end_date' => $today->format('Y-m-d'),
            ],
            self::THIS_MONTH => [
                'start_date' => $today->startOfMonth()->format('Y-m-d'),
                'end_date' => $today->endOfMonth()->format('Y-m-d'),
            ],
            self::LAST_MONTH => [
                'start_date' => $today->subMonth()->startOfMonth()->format('Y-m-d'),
                'end_date' => $today->subMonth()->endOfMonth()->format('Y-m-d'),
            ],
        };
    }

    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
