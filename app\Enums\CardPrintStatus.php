<?php

namespace App\Enums;

enum CardPrintStatus: int
{
    case PENDING = 0;
    case USED = 1;
    case CANCELED = 2;

    public function label(): string
    {
        return match ($this) {
            self::PENDING => 'Chưa sử dụng',
            self::USED => 'Đã sử dụng',
            self::CANCELED => 'Đã hủy',
        };
    }

    public static function toArray(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function isPending(): bool
    {
        return $this === self::PENDING;
    }

    public function isUsed(): bool
    {
        return $this === self::USED;
    }

    public function isCanceled(): bool
    {
        return $this === self::CANCELED;
    }
}
