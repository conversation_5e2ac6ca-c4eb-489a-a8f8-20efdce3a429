<?php

namespace App\Http\Requests\DataLinkables;

use Illuminate\Foundation\Http\FormRequest;

/**
 * @group CMS - Data Linkables
 * Request validate cho API lấy options linkables cho MenuItem
 */
class IndexDataLinkableRequest extends FormRequest
{
    /**
     * <PERSON><PERSON><PERSON> thực quyền thực thi request
     * Ở đây ủy quyền cho middleware permission x<PERSON> lý, nên luôn trả true
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Quy tắc validate
     */
    public function rules(): array
    {
        return [
            // Kiểu linkable (enum int): 1=post, 2=category, 3=static_page
            'type' => ['required', 'integer', 'in:1,2,3'],

            // Từ khóa tìm kiếm
            'search' => ['nullable', 'string', 'max:255'],

            // Số lượng tối đa mỗi trang
            'limit' => ['nullable', 'integer', 'min:1', 'max:100'],
        ];
    }

    /**
     * Thông báo lỗi tiếng Việt
     */
    public function messages(): array
    {
        return [
            'type.required' => 'Trường type là bắt buộc.',
            'type.integer' => 'Trường type phải là số nguyên (1=post, 2=category, 3=static_page).',
            'type.in' => 'Giá trị type không hợp lệ. Chỉ chấp nhận: 1 (post), 2 (category), 3 (static_page).',

            'search.string' => 'Từ khóa tìm kiếm phải là chuỗi.',
            'search.max' => 'Từ khóa tìm kiếm không được vượt quá 255 ký tự.',

            'limit.integer' => 'Giới hạn phải là số nguyên.',
            'limit.min' => 'Giới hạn tối thiểu là 1.',
            'limit.max' => 'Giới hạn tối đa là 100.',
        ];
    }

    /**
     * Chuẩn hóa dữ liệu trước khi validate
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'limit' => $this->input('limit', 20),
            // Ép kiểu type về int nếu được truyền vào
            'type' => $this->has('type') ? (int) $this->input('type') : null,
        ]);
    }

    /**
     * Tài liệu tham số body cho Scribe
     */
    public function bodyParameters(): array
    {
        return [
            'type' => [
                'description' => 'Loại linkable (enum int): 1=post, 2=category, 3=static_page',
                'example' => 1,
            ],
            'search' => [
                'description' => 'Từ khóa tìm theo tiêu đề/tên.',
                'example' => 'Hướng dẫn',
            ],
            'limit' => [
                'description' => 'Số lượng mỗi trang (1-100). Mặc định 20.',
                'example' => 20,
            ],
        ];
    }
}
