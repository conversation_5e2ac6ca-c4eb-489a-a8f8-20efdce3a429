<?php

namespace App\Http\Requests\User;

use App\Enums\UserStatus;
use App\Http\Requests\BaseRequest;

class StoreUserRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|max:255',
            'avatar' => 'nullable|file',
            'status' => 'nullable|integer|in:' . implode(',', array_column(UserStatus::cases(), 'value')),
            'role_ids' => ['sometimes', 'array'],
            'role_ids.*' => ['integer', 'exists:roles,id'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Tên người dùng là bắt buộc',
            'name.max' => 'Tên người dùng không được vượt quá 255 ký tự',
            'email.required' => 'Email là bắt buộc',
            'email.email' => 'Email không đúng định dạng',
            'email.unique' => 'Email đã tồn tại trong hệ thống',
            'password.required' => 'Mật khẩu là bắt buộc',
            'password.min' => 'Mật khẩu phải có ít nhất 8 ký tự',
            'password.max' => 'Mật khẩu không được vượt quá 255 ký tự',
            'avatar.file' => 'File phải là file hợp lệ',
            'status.integer' => 'Trạng thái phải là số nguyên',
            'status.in' => 'Trạng thái không hợp lệ',
        ];
    }

    /**
     * Cung cấp thông tin mô tả cho các tham số yêu cầu trong API
     * Được sử dụng bởi Scribe để tạo tài liệu API
     *
     * @return array<string, array<string, mixed>>
     */
    public function bodyParameters(): array
    {
        return [
            'name' => [
                'description' => 'Tên người dùng',
                'example' => 'Nguyễn Văn A',
                'required' => true,
            ],
            'email' => [
                'description' => 'Địa chỉ email của người dùng',
                'example' => '<EMAIL>',
                'required' => true,
            ],
            'password' => [
                'description' => 'Mật khẩu của người dùng (ít nhất 8 ký tự)',
                'example' => 'password123',
                'required' => true,
            ],
            'avatar' => [
                'description' => 'File ảnh đại diện của người dùng',
                'example' => null,
                'required' => false,
            ],
            'status' => [
                'description' => 'Trạng thái của người dùng',
                'example' => 1,
                'required' => false,
            ],
            'role_ids' => [
                'description' => 'Danh sách ID của các vai trò của người dùng',
                'example' => [1, 2],
                'required' => false,
            ],
        ];
    }
}
