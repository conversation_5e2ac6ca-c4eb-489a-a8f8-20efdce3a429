<?php

namespace App\Http\Requests\StaticPage;

use Illuminate\Foundation\Http\FormRequest;

class StoreStaticPageRequest extends FormRequest
{
    /**
     * Xác định người dùng có được phép thực hiện request này không
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true; // Thường để true và kiểm tra quyền trong middleware
    }

    /**
     * Các quy tắc validation cho request
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'slug' => ['required', 'string', 'max:255', 'unique:static_pages,slug'],
            'body' => ['required', 'string'],
            'status' => ['required', 'integer', 'in:0,1'],
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:500'],
            'meta_keywords' => ['nullable', 'string', 'max:255'],
        ];
    }

    /**
     * <PERSON><PERSON><PERSON> thông báo lỗi tùy chỉnh cho các quy tắc validation
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Tiêu đề trang là bắt buộc.',
            'title.max' => 'Tiêu đề trang không được vượt quá 255 ký tự.',
            'slug.required' => 'Slug trang là bắt buộc.',
            'slug.max' => 'Slug trang không được vượt quá 255 ký tự.',
            'slug.unique' => 'Slug này đã tồn tại, vui lòng chọn slug khác.',
            'body.required' => 'Nội dung trang là bắt buộc.',
            'status.required' => 'Trạng thái trang là bắt buộc.',
            'status.integer' => 'Trạng thái trang phải là số nguyên.',
            'status.in' => 'Trạng thái trang không hợp lệ.',
            'meta_title.max' => 'Meta title không được vượt quá 255 ký tự.',
            'meta_description.max' => 'Meta description không được vượt quá 500 ký tự.',
            'meta_keywords.max' => 'Meta keywords không được vượt quá 255 ký tự.',
        ];
    }

    /**
     * Định nghĩa tham số cho tài liệu API (Scribe)
     *
     * @return array<string, array<string, mixed>>
     */
    public function bodyParameters(): array
    {
        return [
            'title' => [
                'type' => 'string',
                'description' => 'Tiêu đề trang tĩnh.',
                'example' => 'Giới thiệu về chúng tôi',
                'required' => true,
            ],
            'slug' => [
                'type' => 'string',
                'description' => 'Slug của trang tĩnh, dùng cho URL.',
                'example' => 'gioi-thieu',
                'required' => true,
            ],
            'body' => [
                'type' => 'string',
                'description' => 'Nội dung HTML của trang tĩnh.',
                'example' => '<h1>Giới thiệu về chúng tôi</h1><p>Nội dung chi tiết...</p>',
                'required' => true,
            ],
            'status' => [
                'type' => 'integer',
                'description' => 'Trạng thái trang: 1 (hiển thị), 0 (ẩn).',
                'example' => 1,
                'required' => true,
            ],
            'meta_title' => [
                'type' => 'string',
                'description' => 'Tiêu đề SEO cho trang.',
                'example' => 'Giới thiệu về công ty XYZ - Trang chính thức',
                'required' => false,
            ],
            'meta_description' => [
                'type' => 'string',
                'description' => 'Mô tả SEO cho trang.',
                'example' => 'Trang giới thiệu chính thức về công ty XYZ, lịch sử hình thành và phát triển...',
                'required' => false,
            ],
            'meta_keywords' => [
                'type' => 'string',
                'description' => 'Từ khóa SEO cho trang, phân cách bằng dấu phẩy.',
                'example' => 'giới thiệu, công ty, lịch sử, thành tựu',
                'required' => false,
            ],
        ];
    }
}
