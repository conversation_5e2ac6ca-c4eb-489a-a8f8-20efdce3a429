<?php

namespace App\Resources\Public\SiteSettingGroups;

use App\Resources\BaseResource;

/**
 * Resource để hiển thị một nhóm cài đặt dưới dạng key-value.
 */
class SiteSettingGroupResource extends BaseResource
{
    /**
     * D<PERSON> liệu đặc biệt cho resource (relationships, computed properties)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    protected function resourceData($request): array
    {
        return [
            'name' => $this->resource->name,
            'description' => $this->resource->description,
            'order' => $this->resource->order,
            'site_settings' => $this->whenLoaded('siteSettings', function () {
                return $this->siteSettings->mapWithKeys(function ($setting) {
                    return [$setting->key => $setting->value];
                });
            }),
        ];
    }
}
