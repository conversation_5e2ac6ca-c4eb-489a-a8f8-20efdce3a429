<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_logs', function (Blueprint $table) {
            $table->id();

            $table->string('action', 100)->index();
            $table->text('description')->nullable();
            $table->json('data_before')->nullable();
            $table->json('data_after')->nullable();
            $table->json('details')->nullable();

            $table->nullableMorphs('actor'); // Ai thực hiện hành động
            $table->nullableMorphs('model'); // Đ<PERSON>i tượng bị tác động

            $table->string('url', 2000)->nullable();
            $table->string('ip', 45)->nullable();
            $table->text('user_agent')->nullable();

            $table->timestamp('action_time')->useCurrent()->index();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_logs');
    }
};
