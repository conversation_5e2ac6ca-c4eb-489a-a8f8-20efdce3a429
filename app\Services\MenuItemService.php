<?php

namespace App\Services;

use App\Repositories\Eloquent\MenuItemRepository;
use App\Repositories\Interfaces\MenuItemRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Class MenuItemService
 * @package App\Services
 */
class MenuItemService extends BaseCrudService
{
    protected MenuService $menuService;

    public function __construct(MenuService $menuService)
    {
        parent::__construct();
        $this->menuService = $menuService;
    }

    /**
     * Trả về lớp Repository cho MenuItem.
     *
     * @return class-string<MenuItemRepositoryInterface>
     */
    protected function getRepositoryClass(): string
    {
        return MenuItemRepositoryInterface::class;
    }

    /**
     * Lấy danh sách các menu item, chỉ bao gồm các mục gốc (không có cha).
     *
     * @param Request $request
     * @param array $with
     * @return LengthAwarePaginator
     */
    public function list(Request $request, array $with = []): LengthAwarePaginator
    {
        // Thêm điều kiện lọc để chỉ lấy các menu item có parent_id là null
        $request->merge(['parent_id' => 'IS_NULL']);

        return parent::list($request, $with);
    }

    /**
     * Lấy cây menu item đang hoạt động dựa trên slug của nhóm menu.
     *
     * @param string $menuGroupSlug Slug của nhóm menu.
     * @return \Illuminate\Database\Eloquent\Collection
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException Nếu không tìm thấy menu.
     */
    public function getPublicMenuItemsByMenuLocationKey(string $menuLocationKey)
    {
        $menu = $this->menuService->repository->findByOrFail('location_key', $menuLocationKey);

        /** @var MenuItemRepository $menuItemRepository */
        $menuItemRepository = $this->repository;

        return $menuItemRepository->getMenuItemsTreeByMenuId($menu->id);
    }
}
