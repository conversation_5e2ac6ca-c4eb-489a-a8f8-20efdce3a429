<?php

namespace App\Repositories\Eloquent;

use App\Repositories\Interfaces\RoleRepositoryInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Spatie\Permission\Models\Role;

class RoleRepository extends BaseRepository implements RoleRepositoryInterface
{
    /**
     * @inheritDoc
     */
    protected function getModelClass(): string
    {
        return Role::class;
    }

    /**
     * Lấy tất cả roles
     *
     * @param array $params
     * @return Collection
     */
    public function getAllRoles(array $params = [], array $selects = []): Collection
    {
        $query = $this->model->newQuery();

        // Tìm kiếm theo tên
        if (! empty($params['search'])) {
            $search = $params['search'];
            $query->where(function (Builder $q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('description', 'LIKE', "%{$search}%");
            });
        }

        // Lọc theo guard_name
        if (! empty($params['guard_name'])) {
            $query->where('guard_name', $params['guard_name']);
        }

        // Lọc theo trạng thái bảo vệ
        if (isset($params['is_protected'])) {
            $query->where('is_protected', $params['is_protected']);
        }

        // Sắp xếp
        $sortBy = $params['sort_by'] ?? 'created_at';
        $sortOrder = $params['sort_order'] ?? 'desc';
        $query->orderBy($sortBy, $sortOrder);

        // Select
        if (! empty($selects)) {
            $query->select($selects);
        }

        return $query->get();
    }

    /**
     * Lấy tất cả roles với permissions
     *
     * @return Collection
     */
    public function getAllWithPermissions(): Collection
    {
        return $this->model->with('permissions')->get();
    }

    /**
     * Lấy role với permissions theo ID
     *
     * @param int $id
     * @return Role|null
     */
    public function findWithPermissions(int $id): ?Role
    {
        return $this->model->with('permissions')->find($id);
    }

    /**
     * Lấy role với permissions và users theo ID
     *
     * @param int $id
     * @return Role|null
     */
    public function findWithPermissionsAndUsers(int $id): ?Role
    {
        return $this->model->with(['permissions', 'users'])->find($id);
    }

    /**
     * Gán permissions cho role
     *
     * @param int $roleId
     * @param array $permissionIds
     * @return bool
     */
    public function assignPermissions(int $roleId, array $permissionIds): bool
    {
        $role = $this->findOrFail($roleId);
        $role->permissions()->syncWithoutDetaching($permissionIds);

        return true;
    }

    /**
     * Thu hồi permissions từ role
     *
     * @param int $roleId
     * @param array $permissionIds
     * @return bool
     */
    public function revokePermissions(int $roleId, array $permissionIds): bool
    {
        $role = $this->findOrFail($roleId);
        $role->permissions()->detach($permissionIds);

        return true;
    }

    /**
     * Lấy permission IDs của role
     *
     * @param int $roleId
     * @return array
     */
    public function getPermissionIds(int $roleId): array
    {
        $role = $this->findOrFail($roleId);

        return $role->permissions()->pluck('id')->toArray();
    }

    /**
     * Xóa role và detach tất cả relationships
     *
     * @param int $id
     * @return bool
     */
    public function deleteWithDetach(int $id): bool
    {
        $role = $this->findOrFail($id);
        $role->users()->detach();
        $role->permissions()->detach();

        return $role->delete();
    }
}
