<?php

namespace App\Observers;

use App\Services\UserLogService;

/**
 * Lớp cơ sở cho các Observer với khả năng kiểm tra trạng thái logging.
 */
abstract class BaseObserver
{
    /**
     * Khởi tạo observer với UserLogService.
     *
     * @param \App\Services\UserLogService $userLogService
     */
    public function __construct(protected UserLogService $userLogService)
    {
    }

    /**
     * Ghi log một cách an toàn bằng cách kiểm tra trạng thái logging.
     *
     * @param string $action
     * @param string $description
     * @param mixed|null $model
     * @param array $dataBefore
     * @param array $dataAfter
     * @param array $details
     * @return void
     */
    protected function log(
        string $action,
        string $description,
        $model = null,
        array $dataBefore = [],
        array $dataAfter = [],
        array $details = []
    ): void {
        // Chỉ ghi log nếu logging được bật
        if ($this->userLogService->isLoggingEnabled()) {
            $this->userLogService->log(
                action: $action,
                description: $description,
                model: $model,
                dataBefore: $dataBefore,
                dataAfter: $dataAfter,
                details: $details
            );
        }
    }
}
