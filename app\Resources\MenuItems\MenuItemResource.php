<?php

namespace App\Resources\MenuItems;

use App\Enums\LinkableType;
use App\Resources\BaseResource;

class MenuItemResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    protected function resourceData($request): array
    {
        return [
            'children' => MenuItemResource::collection($this->whenLoaded('children')),
            'linkable' => $this->whenLoaded('linkable', function () {
                $type = LinkableType::tryFromModel($this->linkable);

                return [
                    'id' => $this->linkable->id,
                    'label' => $this->linkable->title ?? $this->linkable->name,
                    'slug' => $this->linkable->slug,
                    'type' => $type?->value,
                ];
            }),
        ];
    }
}
