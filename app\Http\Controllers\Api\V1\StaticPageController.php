<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\StaticPage\StoreStaticPageRequest;
use App\Http\Requests\StaticPage\UpdateStaticPageRequest;
use App\Resources\StaticPages\StaticPageResource;
use App\Resources\StaticPages\StaticPageResourceCollection;
use App\Services\StaticPageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Trang tĩnh
 *
 * API quản lý trang tĩnh
 */
class StaticPageController extends BaseController
{
    /**
     * Khởi tạo controller với service
     *
     * @param StaticPageService $service
     */
    public function __construct(protected StaticPageService $service)
    {
    }

    /**
     * Danh sách trang tĩnh
     *
     * Lấy danh sách tất cả trang tĩnh với phân trang
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        // Gọi service để lấy danh sách trang tĩnh với eager loading các mối quan hệ
        $staticPages = $this->service->list($request, ['createdBy', 'updatedBy']);

        // Trả về kết quả sử dụng API Resource
        return $this->success(new StaticPageResourceCollection($staticPages), 'Lấy danh sách trang tĩnh thành công.');
    }

    /**
     * Tạo trang tĩnh mới
     *
     * Lưu thông tin trang tĩnh mới vào database
     *
     * @param StoreStaticPageRequest $request Request đã được validate
     * @return JsonResponse
     */
    public function store(StoreStaticPageRequest $request): JsonResponse
    {
        // Gọi service để tạo trang tĩnh mới với dữ liệu đã được validate
        $staticPage = $this->service->create($request->validated(), ['createdBy', 'updatedBy']);

        // Trả về kết quả với status code 201 (Created)
        return $this->success(new StaticPageResource($staticPage), 'Tạo trang tĩnh thành công.', 201);
    }

    /**
     * Chi tiết trang tĩnh
     *
     * Hiển thị thông tin chi tiết của một trang tĩnh
     *
     * @param int $id ID của trang tĩnh
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        // Gọi service để đọc thông tin trang tĩnh
        $staticPage = $this->service->read($id, ['createdBy', 'updatedBy']);

        // Trả về kết quả
        return $this->success(new StaticPageResource($staticPage), 'Lấy thông tin trang tĩnh thành công.');
    }

    /**
     * Cập nhật trang tĩnh
     *
     * Cập nhật thông tin của một trang tĩnh
     *
     * @param UpdateStaticPageRequest $request Request đã được validate
     * @param int $id ID của trang tĩnh
     * @return JsonResponse
     */
    public function update(UpdateStaticPageRequest $request, int $id): JsonResponse
    {
        // Gọi service để cập nhật trang tĩnh
        $staticPage = $this->service->update($id, $request->validated(), ['createdBy', 'updatedBy']);

        // Trả về kết quả
        return $this->success(new StaticPageResource($staticPage), 'Cập nhật trang tĩnh thành công.');
    }

    /**
     * Xóa trang tĩnh
     *
     * Xóa một trang tĩnh (soft delete)
     *
     * @param int $id ID của trang tĩnh
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        // Gọi service để xóa trang tĩnh
        $deleted = $this->service->delete($id);

        // Trả về kết quả tùy thuộc vào việc xóa có thành công hay không
        return $deleted
            ? $this->successNoContent('Xóa trang tĩnh thành công.')
            : $this->error('Không thể xóa trang tĩnh.', 500);
    }
}
