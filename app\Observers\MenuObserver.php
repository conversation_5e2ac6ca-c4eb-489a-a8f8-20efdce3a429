<?php

namespace App\Observers;

use App\Models\Menu;

/**
 * Observer xử lý các sự kiện liên quan đến Menu
 */
class MenuObserver extends BaseObserver
{
    /**
     * Xử lý sự kiện "created" của Menu.
     *
     * @param \App\Models\Menu $menu
     * @return void
     */
    public function created(Menu $menu): void
    {
        $this->log(
            action: 'create',
            description: "Tạo mới menu '{$menu->name}'",
            model: $menu,
            dataAfter: $menu->toArray()
        );
    }

    /**
     * Xử lý sự kiện "updated" của Menu.
     *
     * @param \App\Models\Menu $menu
     * @return void
     */
    public function updated(Menu $menu): void
    {
        $this->log(
            action: 'update',
            description: "Cập nhật menu '{$menu->name}'",
            model: $menu,
            dataBefore: $menu->getOriginal(),
            dataAfter: $menu->getChanges()
        );
    }

    /**
     * Xử lý sự kiện "deleting" của Menu.
     *
     * @param \App\Models\Menu $menu
     * @return void
     */
    public function deleting(Menu $menu): void
    {
        $this->log(
            action: 'delete',
            description: "Xóa menu '{$menu->name}'",
            model: $menu,
            dataBefore: $menu->toArray()
        );

        if (! $menu->isForceDeleting()) {
            $menu->location_key = $menu->location_key . '_deleted_' . time();
            $menu->saveQuietly();
        }
    }
}
