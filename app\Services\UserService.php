<?php

namespace App\Services;

use App\Enums\UserStatus;
use App\Models\User;
use App\Repositories\Eloquent\UserRepository;
use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserService extends BaseService
{
    public function __construct(
        protected UserRepository    $userRepository,
        protected FileUploadService $fileUploadService
    ) {
    }

    /**
     * Get users with filters and pagination
     *
     * @param array $filters
     * @param int $perPage
     * @return array
     */
    public function getUsers(array $filters = [], int $perPage = 15): array
    {
        try {
            $users = $this->userRepository->getUsersWithFilters($filters, $perPage);

            return $this->success([
                'users' => $users->items(),
                'pagination' => [
                    'current_page' => $users->currentPage(),
                    'per_page' => $users->perPage(),
                    'total' => $users->total(),
                    'last_page' => $users->lastPage(),
                    'from' => $users->firstItem(),
                    'to' => $users->lastItem(),
                ],
            ], '<PERSON><PERSON><PERSON> danh sách người dùng thành công');
        } catch (Exception $e) {
            $this->logError('Error getting users: ' . $e->getMessage());

            return $this->error('USER_LIST_ERROR', 500);
        }
    }

    /**
     * Get user by ID
     *
     * @param int $userId
     * @return array
     */
    public function getUser(int $userId): array
    {
        try {
            $user = $this->userRepository->find($userId);

            return $this->success(['user' => $user], 'Lấy thông tin người dùng thành công');
        } catch (Exception $e) {
            $this->logError('Error getting user: ' . $e->getMessage(), [
                'user_id' => $userId,
            ]);

            return $this->error('USER_GET_ERROR', 500);
        }
    }

    /**
     * Create a new user
     *
     * @param array $data
     * @param UploadedFile|null $avatarFile
     * @return array
     */
    public function createUser(array $data, ?UploadedFile $avatarFile = null): array
    {
        DB::beginTransaction();

        try {
            // Handle avatar upload
            if ($avatarFile) {
                $result = $this->fileUploadService->uploadImage($avatarFile, 'avatars', 'public', null, [
                    'max_size' => 5 * 1024 * 1024, // 5MB
                    'min_width' => 100,
                    'min_height' => 100,
                    'max_width' => 2048,
                    'max_height' => 2048,
                ]);

                if (! $result['success']) {
                    return $result;
                }

                $data['avatar'] = $result['data']['url'];
            }

            // Hash password
            $data['password'] = Hash::make($data['password']);

            // Set a default status if not provided
            if (! isset($data['status'])) {
                $data['status'] = UserStatus::ACTIVE->value;
            }

            $user = $this->userRepository->create($data);

            // Gán role nếu có
            if (! empty($data['role_ids']) && is_array($data['role_ids'])) {
                foreach ($data['role_ids'] as $roleId) {
                    DB::table('model_has_roles')->insert([
                        'role_id' => $roleId,
                        'model_type' => User::class,
                        'model_id' => $user->id,
                    ]);
                }
            }

            DB::commit();
            $this->logInfo('User created successfully', ['user_id' => $user->id, 'email' => $user->email]);

            return $this->success(['user' => $user], 'Tạo người dùng thành công');
        } catch (Exception $e) {
            DB::rollBack();
            $this->logError('Error creating user: ' . $e->getMessage(), [
                'data' => $data,
            ]);

            return $this->error('USER_CREATE_ERROR', 500);
        }
    }

    /**
     * Update user
     *
     * @param int $userId
     * @param array $data
     * @param UploadedFile|null $avatarFile
     * @return array
     */
    public function updateUser(int $userId, array $data, ?UploadedFile $avatarFile = null): array
    {
        DB::beginTransaction();

        try {
            $user = $this->userRepository->find($userId);

            $this->logInfo('User updated', ['user' => $user, 'data' => $data]);

            // Handle avatar upload
            if ($avatarFile) {
                $result = $this->fileUploadService->uploadImage($avatarFile, 'avatars', 'public', $user->avatar, [
                    'max_size' => 5 * 1024 * 1024, // 5MB
                    'min_width' => 100,
                    'min_height' => 100,
                    'max_width' => 2048,
                    'max_height' => 2048,
                ]);

                if (! $result['success']) {
                    return $result;
                }

                $data['avatar'] = $result['data']['url'];
            }

            // Loại bỏ email và password khỏi dữ liệu update
            unset($data['email'], $data['password']);

            $updatedUser = $this->userRepository->update($userId, $data);

            // Gán lại role nếu có
            if (! empty($data['role_ids']) && is_array($data['role_ids'])) {
                // Xóa roles cũ
                DB::table('model_has_roles')->where('model_id', $updatedUser->id)->where('model_type', User::class)->delete();

                // Thêm roles mới
                foreach ($data['role_ids'] as $roleId) {
                    DB::table('model_has_roles')->insert([
                        'role_id' => $roleId,
                        'model_type' => User::class,
                        'model_id' => $updatedUser->id,
                    ]);
                }
            }

            DB::commit();
            $this->logInfo('User updated successfully', ['user_id' => $userId]);

            return $this->success([
                'user' => $updatedUser,
            ], 'Cập nhật người dùng thành công');
        } catch (Exception $e) {
            DB::rollBack();
            $this->logError('Error updating user: ' . $e->getMessage(), [
                'user_id' => $userId,
                'data' => $data,
            ]);

            return $this->error('USER_UPDATE_ERROR', 500);
        }
    }

    /**
     * Delete user
     *
     * @param int $userId
     * @return array
     */
    public function deleteUser(int $userId): array
    {
        try {
            $user = $this->userRepository->find($userId);

            $this->userRepository->delete($userId);

            $this->logInfo('User deleted successfully', ['user_id' => $userId]);

            return $this->success([], 'Xóa người dùng thành công');
        } catch (Exception $e) {
            $this->logError('Error deleting user: ' . $e->getMessage(), [
                'user_id' => $userId,
            ]);

            return $this->error('USER_DELETE_ERROR', 500);
        }
    }

    /**
     * Update user status
     *
     * @param int $userId
     * @param int $status
     * @return array
     */
    public function updateUserStatus(int $userId, int $status): array
    {
        try {
            $user = $this->userRepository->find($userId);

            $updatedUser = $this->userRepository->update($userId, ['status' => $status]);

            $this->logInfo('User status updated successfully', ['user_id' => $userId, 'status' => $status]);

            return $this->success([
                'user' => $updatedUser,
            ], 'Cập nhật trạng thái người dùng thành công');
        } catch (Exception $e) {
            $this->logError('Error updating user status: ' . $e->getMessage(), [
                'user_id' => $userId,
                'status' => $status,
            ]);

            return $this->error('USER_STATUS_UPDATE_ERROR', 500);
        }
    }

    /**
     * Upload avatar for user
     *
     * @param int $userId
     * @param UploadedFile $avatarFile
     * @return array
     */
    public function uploadAvatar(int $userId, UploadedFile $avatarFile): array
    {
        try {
            $user = $this->userRepository->find($userId);

            $result = $this->fileUploadService->uploadImage($avatarFile, 'avatars', 'public', $user->avatar_url, [
                'max_size' => 5 * 1024 * 1024, // 5MB
                'min_width' => 100,
                'min_height' => 100,
                'max_width' => 2048,
                'max_height' => 2048,
            ]);

            if (! $result['success']) {
                return $result;
            }

            $updatedUser = $this->userRepository->update($userId, ['avatar_url' => $result['data']['url']]);

            $this->logInfo('User avatar uploaded successfully', ['user_id' => $userId]);

            return $this->success(['user' => $updatedUser], 'Upload ảnh đại diện thành công');
        } catch (Exception $e) {
            $this->logError('Error uploading avatar: ' . $e->getMessage(), [
                'user_id' => $userId,
            ]);

            return $this->error('AVATAR_UPLOAD_ERROR', 500);
        }
    }

    /**
     * Delete user avatar
     *
     * @param int $userId
     * @return array
     */
    public function deleteAvatar(int $userId): array
    {
        try {
            $user = $this->userRepository->find($userId);

            if (! $user->avatar_url) {
                return $this->error('AVATAR_NOT_FOUND', 404);
            }

            // Delete avatar file
            $this->fileUploadService->deleteFile($user->avatar_url, 'public');

            // Update user to remove avatar URL
            $updatedUser = $this->userRepository->update($userId, ['avatar_url' => null]);

            $this->logInfo('User avatar deleted successfully', ['user_id' => $userId]);

            return $this->success(['user' => $updatedUser], 'Xóa ảnh đại diện thành công');
        } catch (Exception $e) {
            $this->logError('Error deleting avatar: ' . $e->getMessage(), ['user_id' => $userId]);

            return $this->error('AVATAR_DELETE_ERROR', 500);
        }
    }

    /**
     * Change user password
     *
     * @param int $userId
     * @param string $password
     * @return array
     */
    public function changeUserPassword(int $userId, string $password): array
    {
        try {
            $user = $this->userRepository->find($userId);

            // Hash password
            $hashedPassword = Hash::make($password);

            // Update user password
            $updatedUser = $this->userRepository->update($userId, [
                'password' => $hashedPassword,
                'force_password_change' => false, // Reset force password change flag
            ]);

            $this->logInfo('User password changed successfully', [
                'user_id' => $userId,
                'changed_by' => \Illuminate\Support\Facades\Auth::id(),
            ]);

            return $this->success([
                'user' => $updatedUser,
            ], 'Thay đổi mật khẩu người dùng thành công');
        } catch (Exception $e) {
            $this->logError('Error changing user password: ' . $e->getMessage(), [
                'user_id' => $userId,
                'changed_by' => \Illuminate\Support\Facades\Auth::id(),
            ]);

            return $this->error('USER_PASSWORD_CHANGE_ERROR', 500);
        }
    }

    /**
     * Get error messages
     *
     * @param string $errorCode
     * @param array $params
     * @return string
     */
    protected function getErrorMessage(string $errorCode, array $params = []): string
    {
        $messages = [
            'USER_NOT_FOUND' => 'Không tìm thấy người dùng',
            'AVATAR_NOT_FOUND' => 'Không tìm thấy ảnh đại diện',
        ];

        $message = $messages[$errorCode] ?? 'Có lỗi xảy ra. Vui lòng thử lại sau.';

        return $this->interpolateMessage($message, $params);
    }
}
