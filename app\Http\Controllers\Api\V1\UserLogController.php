<?php

namespace App\Http\Controllers\Api\V1;

use App\Resources\UserLog\UserLogResourceCollection;
use App\Services\UserLogService;
use Illuminate\Http\Request;

/**
 * @group Lịch sử hoạt động
 */
class UserLogController extends BaseController
{
    /**
     * Khởi tạo controller với UserLogService.
     */
    public function __construct(protected UserLogService $service)
    {
    }

    /**
     * L<PERSON>y danh sách lịch sử hoạt động.
     *
     * API này yêu cầu quyền `user_log_management.view`.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $data = $this->service->list($request, ['actor', 'model']);

        return $this->success(new UserLogResourceCollection($data), 'L<PERSON>y danh sách lịch sử hoạt động thành công');
    }
}
