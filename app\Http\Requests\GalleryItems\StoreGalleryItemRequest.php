<?php

namespace App\Http\Requests\GalleryItems;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\UploadedFile;

/**
 * @group Thư viện ảnh - Item
 */
class StoreGalleryItemRequest extends FormRequest
{
    /**
     * Xác định xem người dùng có được phép thực hiện yêu cầu này không.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Lấy các quy tắc xác thực áp dụng cho yêu cầu.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'image_url' => ['nullable'],
            'alt_text' => 'nullable|string|max:255',
            'title' => 'nullable|string|max:255',
            'link' => 'nullable|url|max:2048',
            'target' => 'nullable|string|in:_blank,_self',
            'status' => 'nullable|integer|in:0,1',
            'order' => 'nullable|integer|min:0',
        ];
    }

    /**
     * L<PERSON>y thông báo lỗi tùy chỉnh cho các quy tắc xác thực.
     *
     * @return array<string, string>
     */
    /**
     * Validate sau khi các quy tắc validation đã chạy
     *
     * @param \Illuminate\Validation\Validator $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $imageUrl = $this->input('image_url');

            // Nếu image_url được cung cấp
            if ($imageUrl !== null) {
                // Nếu image_url không phải là file upload
                if (! ($imageUrl instanceof UploadedFile)) {
                    // Kiểm tra nếu là URL hợp lệ
                    if (! is_string($imageUrl) || ! filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                        $validator->errors()->add('image_url', 'Hình ảnh phải là file upload hoặc URL hợp lệ.');
                    } else {
                        // Kiểm tra đuôi file của URL có phải là ảnh không
                        $fileExtension = pathinfo(parse_url($imageUrl, PHP_URL_PATH), PATHINFO_EXTENSION);
                        $fileExtension = strtolower($fileExtension);

                        $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
                        if (! in_array($fileExtension, $validExtensions)) {
                            $validator->errors()->add('image_url', 'URL hình ảnh phải có đuôi hợp lệ (' . implode(', ', $validExtensions) . ').');
                        }
                    }
                } else {
                    // Kiểm tra loại file phù hợp
                    if (! in_array($imageUrl->getMimeType(), ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'])) {
                        $validator->errors()->add('image_url', 'File upload phải là một hình ảnh hợp lệ (jpeg, png, gif, webp, svg).');
                    }

                    // Kiểm tra kích thước file
                    $maxSize = 10240; // 10MB
                    if ($imageUrl->getSize() > $maxSize * 1024) {
                        $validator->errors()->add('image_url', 'Kích thước file không được vượt quá ' . ($maxSize / 1024) . 'MB.');
                    }
                }
            }
        });
    }

    public function messages(): array
    {
        return [
            'alt_text.string' => 'Alt text phải là chuỗi ký tự.',
            'alt_text.max' => 'Alt text không được vượt quá 255 ký tự.',
            'title.string' => 'Tiêu đề phải là chuỗi ký tự.',
            'title.max' => 'Tiêu đề không được vượt quá 255 ký tự.',
            'link.url' => 'Liên kết phải là một URL hợp lệ.',
            'link.max' => 'Liên kết không được vượt quá 2048 ký tự.',
            'target.string' => 'Target phải là chuỗi ký tự.',
            'target.in' => 'Target chỉ được phép là _blank hoặc _self.',
            'status.integer' => 'Trạng thái phải là số nguyên.',
            'status.in' => 'Trạng thái chỉ được phép là 0 (Inactive) hoặc 1 (Active).',
            'order.integer' => 'Thứ tự sắp xếp phải là số nguyên.',
            'order.min' => 'Thứ tự sắp xếp phải lớn hơn hoặc bằng 0.',
        ];
    }

    /**
     * Lấy các tham số body cho tài liệu API.
     *
     * @return array<string, array<string, mixed>>
     */
    public function bodyParameters(): array
    {
        return [
            'image_url' => [
                'type' => 'file|string',
                'description' => 'Hình ảnh gallery item. Có thể nhập URL hoặc upload file ảnh (tối đa 10MB). Hỗ trợ các định dạng: jpg, jpeg, png, gif, webp.',
                'example' => 'file hoặc https://cdn.example.com/images/beautiful-image.jpg',
            ],
            'alt_text' => [
                'description' => 'Alt text cho hình ảnh (SEO)',
                'example' => 'Hình ảnh đẹp',
            ],
            'title' => [
                'description' => 'Tiêu đề của hình ảnh',
                'example' => 'Hình ảnh banner trang chủ',
            ],
            'link' => [
                'description' => 'Liên kết khi click vào hình ảnh',
                'example' => 'https://example.com/promotion',
            ],
            'target' => [
                'description' => 'Cách mở liên kết (_blank: tab mới, _self: tab hiện tại)',
                'example' => '_blank',
            ],
            'status' => [
                'description' => 'Trạng thái của item (0: Inactive, 1: Active)',
                'example' => 1,
            ],
            'order' => [
                'description' => 'Thứ tự sắp xếp (số càng nhỏ càng ưu tiên)',
                'example' => 1,
            ],
        ];
    }
}
