<?php

namespace App\Http\Requests\GalleryGroups;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateGalleryGroupRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'required', 'string', 'max:255'],
            'location_key' => [
                'sometimes',
                'required',
                'string',
                'max:255',
                Rule::unique('gallery_groups', 'location_key')->ignore($this->route('id')),
            ],
            'location_display' => ['sometimes', 'required', 'integer', 'in:1,2,3'],
            'description' => ['nullable', 'string', 'max:255'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Tên nhóm thư viện ảnh là bắt buộc.',
            'name.max' => 'Tên nhóm thư viện ảnh không được vượt quá 255 ký tự.',
            'location_key.required' => 'Key vị trí là bắt buộc.',
            'location_key.unique' => 'Key vị trí đã tồn tại.',
            'location_key.max' => 'Key vị trí không được vượt quá 255 ký tự.',
            'location_display.required' => 'Nơi hiển thị là bắt buộc.',
            'location_display.integer' => 'Nơi hiển thị phải là số nguyên.',
            'location_display.in' => 'Nơi hiển thị không hợp lệ (1: Homepage, 2: Landing, 3: Both).',
            'description.max' => 'Mô tả không được vượt quá 255 ký tự.',
        ];
    }

    /**
     * Định nghĩa tham số cho tài liệu API
     */
    public function bodyParameters(): array
    {
        return [
            'name' => [
                'type' => 'string',
                'description' => 'Tên định danh cho admin (e.g., "Menu đầu trang", "Menu chân trang").',
                'example' => 'Banner trang chủ',
            ],
            'location_key' => [
                'type' => 'string',
                'description' => 'Key để frontend gọi lên (e.g., header, footer).',
                'example' => 'homepage_banner',
            ],
            'location_display' => [
                'type' => 'integer',
                'description' => 'Nơi hiển thị: 1 - Homepage, 2 - Landing, 3 - Both.',
                'example' => 1,
            ],
            'description' => [
                'type' => 'string',
                'description' => 'Mô tả nhóm thư viện ảnh.',
                'example' => 'Banner hiển thị trên trang chủ',
            ],
        ];
    }
}
