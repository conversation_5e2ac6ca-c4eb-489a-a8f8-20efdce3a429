<?php

namespace App\Http\Requests\Menus;

use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class UpdateMenuRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Thay đổi thành true hoặc logic phân quyền cụ thể
        return true;
    }

    /**
     * Lấy các quy tắc xác thực áp dụng cho yêu cầu.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $menuId = $this->route('id');

        return [
            'name' => ['sometimes', 'required', 'string', 'max:255'],
            'location_key' => [
                'sometimes',
                'required',
                'string',
                'max:255',
                'regex:/^[a-z0-9_-]+$/',
                Rule::unique('menus', 'location_key')->ignore($menuId),
            ],
        ];
    }

    /**
     * Defines the parameters for the request body.
     *
     * @return array
     */
    public function bodyParameters(): array
    {
        return [
            'name' => [
                'description' => 'Tên mới của menu.',
                'example' => 'Menu chân trang',
            ],
            'location_key' => [
                'description' => 'Khóa vị trí mới duy nhất cho menu (chỉ chứa a-z, 0-9, _, -).',
                'example' => 'footer_menu',
            ],
        ];
    }

    /**
     * Lấy các thông báo lỗi cho các quy tắc xác thực đã xác định.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Vui lòng nhập tên menu.',
            'name.string' => 'Tên menu phải là một chuỗi ký tự.',
            'name.max' => 'Tên menu không được vượt quá 255 ký tự.',

            'location_key.required' => 'Mã vị trí là bắt buộc.',
            'location_key.string' => 'Mã vị trí phải là một chuỗi ký tự.',
            'location_key.max' => 'Mã vị trí không được vượt quá 255 ký tự.',
            'location_key.regex' => 'Mã vị trí chỉ được chứa chữ thường, số, dấu gạch dưới (_) và gạch ngang (-).',
            'location_key.unique' => 'Mã vị trí đã tồn tại. Vui lòng chọn mã khác.',
        ];
    }
}
