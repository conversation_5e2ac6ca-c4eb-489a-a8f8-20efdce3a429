<?php

namespace App\Http\Middleware;

use App\Traits\ApiResponse;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PublicApiAuthMiddleware
{
    use ApiResponse;

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Kiểm tra xem header 'X-Public-Api-Secret' có tồn tại không
        if (! $request->hasHeader('X-Public-Api-Secret')) {
            return $this->error('Unauthorized: Missing X-Public-Api-Secret header', 401);
        }

        // Lấy giá trị từ header
        $secretKey = $request->header('X-Public-Api-Secret');

        // Lấy giá trị PUBLIC_API_SECRET_KEY từ .env
        $expectedSecretKey = env('PUBLIC_API_SECRET_KEY');

        // So sánh giá trị
        if ($secretKey !== $expectedSecretKey) {
            return $this->error('Unauthorized: Invalid X-Public-Api-Secret', 401);
        }

        return $next($request);
    }
}
