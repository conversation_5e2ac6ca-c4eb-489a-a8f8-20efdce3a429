<?php

namespace App\Observers;

use App\Models\SiteSetting;

/**
 * Observer xử lý các sự kiện liên quan đến SiteSetting
 */
class SiteSettingObserver extends BaseObserver
{
    /**
     * Xử lý sự kiện "created" của SiteSetting.
     *
     * @param \App\Models\SiteSetting $siteSetting
     * @return void
     */
    public function created(SiteSetting $siteSetting): void
    {
        $this->log(
            action: 'create',
            description: "Tạo mới cài đặt '{$siteSetting->name}'",
            model: $siteSetting,
            dataAfter: $siteSetting->toArray()
        );
    }

    /**
     * Xử lý sự kiện "updated" của SiteSetting.
     *
     * @param \App\Models\SiteSetting $siteSetting
     * @return void
     */
    public function updated(SiteSetting $siteSetting): void
    {
        $this->log(
            action: 'update',
            description: "Cập nhật cài đặt '{$siteSetting->name}'",
            model: $siteSetting,
            dataBefore: $siteSetting->getOriginal(),
            dataAfter: $siteSetting->getChanges()
        );
    }

    /**
     * Xử lý sự kiện "deleting" của SiteSetting.
     *
     * @param \App\Models\SiteSetting $siteSetting
     * @return void
     */
    public function deleting(SiteSetting $siteSetting): void
    {
        $this->log(
            action: 'delete',
            description: "Xóa cài đặt '{$siteSetting->name}'",
            model: $siteSetting,
            dataBefore: $siteSetting->toArray()
        );

        if (! $siteSetting->isForceDeleting()) {
            $siteSetting->key = $siteSetting->key . '_deleted_' . time();
            $siteSetting->saveQuietly();
        }
    }
}
