<?php

namespace App\Providers;

use App\Repositories\Eloquent\BaseRepository;
use App\Repositories\Eloquent\CardPrintRepository;
use App\Repositories\Eloquent\CategoryRepository;
use App\Repositories\Eloquent\GalleryGroupRepository;
use App\Repositories\Eloquent\GalleryItemRepository;
use App\Repositories\Eloquent\GameRepository;
use App\Repositories\Eloquent\MenuItemRepository;
use App\Repositories\Eloquent\MenuRepository;
use App\Repositories\Eloquent\PermissionRepository;
use App\Repositories\Eloquent\PostRepository;
use App\Repositories\Eloquent\RoleRepository;
use App\Repositories\Eloquent\SiteSettingGroupRepository;
use App\Repositories\Eloquent\SiteSettingRepository;
use App\Repositories\Eloquent\StaticPageRepository;
use App\Repositories\Eloquent\UserLogRepository;
use App\Repositories\Eloquent\UserRepository;
use App\Repositories\Interfaces\BaseRepositoryInterface;
use App\Repositories\Interfaces\CardPrintRepositoryInterface;
use App\Repositories\Interfaces\CategoryRepositoryInterface;
use App\Repositories\Interfaces\GalleryGroupRepositoryInterface;
use App\Repositories\Interfaces\GalleryItemRepositoryInterface;
use App\Repositories\Interfaces\GameRepositoryInterface;
use App\Repositories\Interfaces\MenuItemRepositoryInterface;
use App\Repositories\Interfaces\MenuRepositoryInterface;
use App\Repositories\Interfaces\PermissionRepositoryInterface;
use App\Repositories\Interfaces\PostRepositoryInterface;
use App\Repositories\Interfaces\RoleRepositoryInterface;
use App\Repositories\Interfaces\SiteSettingGroupRepositoryInterface;
use App\Repositories\Interfaces\SiteSettingRepositoryInterface;
use App\Repositories\Interfaces\StaticPageRepositoryInterface;
use App\Repositories\Interfaces\UserLogRepositoryInterface;
use App\Repositories\Interfaces\UserRepositoryInterface;
use Illuminate\Support\ServiceProvider;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(BaseRepositoryInterface::class, BaseRepository::class);
        $this->app->singleton(UserRepositoryInterface::class, UserRepository::class);
        $this->app->singleton(RoleRepositoryInterface::class, RoleRepository::class);
        $this->app->singleton(PermissionRepositoryInterface::class, PermissionRepository::class);
        $this->app->singleton(SiteSettingGroupRepositoryInterface::class, SiteSettingGroupRepository::class);
        $this->app->singleton(SiteSettingRepositoryInterface::class, SiteSettingRepository::class);
        $this->app->singleton(CategoryRepositoryInterface::class, CategoryRepository::class);
        $this->app->singleton(MenuRepositoryInterface::class, MenuRepository::class);
        $this->app->singleton(MenuItemRepositoryInterface::class, MenuItemRepository::class);
        $this->app->singleton(PostRepositoryInterface::class, PostRepository::class);
        $this->app->singleton(GalleryGroupRepositoryInterface::class, GalleryGroupRepository::class);
        $this->app->singleton(GalleryItemRepositoryInterface::class, GalleryItemRepository::class);
        $this->app->singleton(StaticPageRepositoryInterface::class, StaticPageRepository::class);
        $this->app->singleton(CardPrintRepositoryInterface::class, CardPrintRepository::class);
        $this->app->singleton(GameRepositoryInterface::class, GameRepository::class);
        $this->app->singleton(UserLogRepositoryInterface::class, UserLogRepository::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
