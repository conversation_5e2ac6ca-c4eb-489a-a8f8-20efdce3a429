<?php

namespace App\Http\Requests\User;

use App\Enums\UserStatus;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class UpdateUserRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'id' => $this->route('id'),
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = $this->route('id');

        return [
            'id' => ['required', Rule::exists('users', 'id')],
            'name' => 'sometimes|string|max:255',
            'avatar' => 'sometimes|nullable|file',
            'avatar_url' => 'sometimes|nullable|url|max:500',
            'status' => 'sometimes|integer|in:' . implode(',', array_column(UserStatus::cases(), 'value')),
            'role_ids' => ['sometimes', 'array'],
            'role_ids.*' => ['integer', 'exists:roles,id'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'id.required' => 'ID người dùng là bắt buộc.',
            'id.exists' => 'Người dùng không tồn tại.',
            'name.string' => 'Tên người dùng phải là chuỗi ký tự.',
            'name.max' => 'Tên người dùng không được vượt quá 255 ký tự.',
            'avatar.file' => 'File ảnh đại diện phải là file hợp lệ.',
            'avatar_url.url' => 'URL ảnh đại diện không đúng định dạng.',
            'avatar_url.max' => 'URL ảnh đại diện không được vượt quá 500 ký tự.',
            'status.integer' => 'Trạng thái phải là số nguyên.',
            'status.in' => 'Trạng thái không hợp lệ.',
        ];
    }

    /**
     * Cung cấp thông tin mô tả cho các tham số yêu cầu trong API
     * Được sử dụng bởi Scribe để tạo tài liệu API
     *
     * @return array<string, array<string, mixed>>
     */
    public function bodyParameters(): array
    {
        return [
            'name' => [
                'description' => 'Tên người dùng',
                'example' => 'Nguyễn Văn A',
                'required' => false,
            ],
            'avatar' => [
                'description' => 'File ảnh đại diện của người dùng',
                'example' => null,
                'required' => false,
            ],
            'avatar_url' => [
                'description' => 'URL đến ảnh đại diện của người dùng',
                'example' => 'https://example.com/avatar.jpg',
                'required' => false,
            ],
            'status' => [
                'description' => 'Trạng thái của người dùng',
                'example' => 1,
                'required' => false,
            ],
            'role_ids' => [
                'description' => 'Danh sách ID của các vai trò của người dùng',
                'example' => [1, 2],
                'required' => false,
            ],
        ];
    }
}
