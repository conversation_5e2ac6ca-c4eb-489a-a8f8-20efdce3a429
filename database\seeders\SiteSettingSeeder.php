<?php

namespace Database\Seeders;

use App\Models\SiteSetting;
use App\Models\SiteSettingGroup;
use App\Models\User;
use Illuminate\Database\Seeder;

class SiteSettingSeeder extends Seeder
{
    /**
     * Tạo dữ liệu mẫu cho bảng site_settings.
     */
    public function run(): void
    {
        $user = User::first();
        // Lấy danh sách các nhóm cài đặt để mapping
        $groups = SiteSettingGroup::pluck('id', 'group_key');

        // Mảng dữ liệu cài đặt mẫu
        $settings = [
            // Cài đặt chung
            [
                'group_key' => 'general_info',
                'name' => 'Tên trang web',
                'key' => 'site_name',
                'value' => 'Kiếm Hiệp Tình 1',
                'type' => 'text',
                'description' => 'Tên hiển thị trên tab trình duyệt và các vị trí khác.',
                'is_private' => false,
                'is_deletable' => false,
                'status' => 1,
            ],
            [
                'group_key' => 'general_info',
                'name' => 'Logo chính',
                'key' => 'site_logo',
                'value' => '/storage/assets/logo.png',
                'type' => 'image',
                'description' => 'Logo hiển thị ở đầu trang.',
                'is_private' => false,
                'is_deletable' => false,
                'status' => 1,
            ],
            [
                'group_key' => 'general_info',
                'name' => 'Favicon',
                'key' => 'site_favicon',
                'value' => '/storage/assets/favicon.ico',
                'type' => 'image',
                'description' => 'Icon hiển thị trên tab trình duyệt.',
                'is_private' => false,
                'is_deletable' => false,
                'status' => 1,
            ],

            // Mạng xã hội
            [
                'group_key' => 'social_media',
                'name' => 'Facebook Fanpage',
                'key' => 'social_facebook_url',
                'value' => 'https://www.facebook.com/your-page',
                'type' => 'text',
                'description' => 'Đường dẫn đến trang Facebook chính thức.',
                'is_private' => false,
                'is_deletable' => true,
                'status' => 1,
            ],
            [
                'group_key' => 'social_media',
                'name' => 'Youtube Channel',
                'key' => 'social_youtube_url',
                'value' => 'https://www.youtube.com/your-channel',
                'type' => 'text',
                'description' => 'Đường dẫn đến kênh Youtube chính thức.',
                'is_private' => false,
                'is_deletable' => true,
                'status' => 1,
            ],

            // Thông tin liên hệ
            [
                'group_key' => 'contact_config',
                'name' => 'Email hỗ trợ',
                'key' => 'contact_support_email',
                'value' => '<EMAIL>',
                'type' => 'text',
                'description' => 'Email để người dùng liên hệ hỗ trợ.',
                'is_private' => false,
                'is_deletable' => false,
                'status' => 1,
            ],
            [
                'group_key' => 'contact_config',
                'name' => 'Hotline',
                'key' => 'contact_hotline',
                'value' => '1900 1234',
                'type' => 'text',
                'description' => 'Số điện thoại hỗ trợ khách hàng.',
                'is_private' => false,
                'is_deletable' => false,
                'status' => 1,
            ],

            // Cài đặt SEO
            [
                'group_key' => 'default_seo',
                'name' => 'Meta Title mặc định',
                'key' => 'seo_default_meta_title',
                'value' => 'Kiếm Hiệp Tình 1 - Trang chủ',
                'type' => 'text',
                'description' => 'Thẻ meta title mặc định cho các trang không có cài đặt riêng.',
                'is_private' => false,
                'is_deletable' => false,
                'status' => 1,
            ],
            [
                'group_key' => 'default_seo',
                'name' => 'Meta Description mặc định',
                'key' => 'seo_default_meta_description',
                'value' => 'Trải nghiệm thế giới võ lâm Kiếm Hiệp Tình 1, nơi tình yêu và kiếm hiệp hòa quyện. Tham gia ngay!',
                'type' => 'textarea',
                'description' => 'Thẻ meta description mặc định.',
                'is_private' => false,
                'is_deletable' => false,
                'status' => 1,
            ],
        ];

        foreach ($settings as $setting) {
            // Đảm bảo group_key tồn tại trong DB
            if (isset($groups[$setting['group_key']])) {
                SiteSetting::updateOrCreate(
                    ['key' => $setting['key']], // Điều kiện để tìm hoặc tạo
                    [
                        'group_id' => $groups[$setting['group_key']],
                        'name' => $setting['name'],
                        'value' => $setting['value'],
                        'type' => $setting['type'],
                        'description' => $setting['description'] ?? null,
                        'is_private' => $setting['is_private'] ?? false,
                        'is_deletable' => $setting['is_deletable'] ?? true,
                        'status' => $setting['status'] ?? 1,
                        'created_by' => $user->id,
                        'updated_by' => $user->id,
                    ]
                );
            }
        }

        $this->command->info('✅ Đã tạo/cập nhật thành công các cài đặt trang mẫu!');
    }
}
