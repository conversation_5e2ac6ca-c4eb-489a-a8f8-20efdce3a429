<?php

namespace Database\Seeders;

use App\Models\GalleryGroup;
use App\Models\GalleryItem;
use App\Models\User;
use Illuminate\Database\Seeder;

class GallerySeeder extends Seeder
{
    /**
     * Tạo dữ liệu mẫu cho bảng gallery_groups và gallery_items.
     */
    public function run(): void
    {
        $user = User::first();

        // 1. Tạo các Nhóm Thư viện ảnh
        $groups = [
            [
                'name' => 'Ảnh In-game',
                'description' => 'Những khoảnh khắc đẹp được ghi lại từ trong game.',
                'location_key' => 'ingame-photo',
                'location_display' => 1,
            ],
            [
                'name' => 'Hình nền máy tính',
                'description' => 'B<PERSON> sưu tập hình nền chất lượng cao cho máy tính.',
                'location_key' => 'wallpaper',
                'location_display' => 2,
            ],
            [
                'name' => 'Artwork nhân vật',
                'description' => '<PERSON><PERSON><PERSON> tác phẩm nghệ thuật về nhân vật trong game.',
                'location_key' => 'character-artwork',
                'location_display' => 3,
            ],
        ];

        foreach ($groups as $groupData) {
            $group = GalleryGroup::updateOrCreate(
                ['location_key' => $groupData['location_key']], // Điều kiện để tìm hoặc tạo
                [
                    'name' => $groupData['name'],
                    'location_display' => $groupData['location_display'],
                    'description' => $groupData['description'],
                    'created_by' => $user->id,
                    'updated_by' => $user->id,
                ]
            );

            // 2. Tạo các Ảnh mẫu cho từng nhóm
            for ($i = 1; $i <= 5; $i++) {
                GalleryItem::updateOrCreate(
                    [
                        'gallery_group_id' => $group->id,
                        'order' => $i,
                    ],
                    [
                        'gallery_group_id' => $group->id,
                        'image_url' => 'https://picsum.photos/800/600?random=' . rand(1, 1000), // Ảnh ngẫu nhiên
                        'alt_text' => $group->name . ' - Ảnh ' . $i,
                        'title' => $group->name . ' - Tiêu đề ảnh ' . $i,
                        'link' => 'https://example.com/' . $group->location_key . '/' . $i,
                        'target' => $i % 2 === 0 ? '_blank' : '_self', // Xen kế giữa _blank và _self
                        'status' => 1,
                        'order' => $i,
                        'created_by' => $user->id,
                        'updated_by' => $user->id,
                    ]
                );
            }
        }

        $this->command->info('✅ Thư viện ảnh (Gallery) đã được tạo thành công!');
    }
}
