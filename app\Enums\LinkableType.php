<?php

namespace App\Enums;

/**
 * Enum chuẩn hóa kiểu linkable cho MenuItem
 *
 * - POST = 1
 * - CATEGORY = 2
 * - STATIC_PAGE = 3
 */
enum LinkableType: int
{
    case POST = 1;
    case CATEGORY = 2;
    case STATIC_PAGE = 3;

    /**
     * Tr<PERSON> về key type chuẩn hóa cho client
     */
    public function key(): string
    {
        return match ($this) {
            self::POST => 'post',
            self::CATEGORY => 'category',
            self::STATIC_PAGE => 'static_page',
        };
    }

    /**
     * Trả về FQCN của model tương ứng
     */
    public function modelClass(): string
    {
        return match ($this) {
            self::POST => 'App\\Models\\Post',
            self::CATEGORY => 'App\\Models\\Category',
            self::STATIC_PAGE => 'App\\Models\\StaticPage',
        };
    }

    /**
     * <PERSON><PERSON> lọc mặc định cho từng loại khi lấy options
     */
    public function defaultFilters(): array
    {
        return match ($this) {
            self::POST => [
                'status' => 'published',
                'sort_by' => 'published_at',
                'sort_order' => 'desc',
            ],
            self::CATEGORY => [
                'status' => 1,
                'sort_by' => 'order',
                'sort_order' => 'asc',
            ],
            self::STATIC_PAGE => [
                'status' => 1,
                'sort_by' => 'created_at',
                'sort_order' => 'desc',
            ],
        };
    }

    /**
     * Xác định enum type từ model instance
     */
    public static function tryFromModel($model): ?self
    {
        foreach (self::cases() as $case) {
            if ($model instanceof ($case->modelClass())) {
                return $case;
            }
        }

        return null;
    }
}
