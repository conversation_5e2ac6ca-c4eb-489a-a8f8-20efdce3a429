<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class CardPrint extends BaseModel
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'card_prints';

    protected $primaryKey = 'id';

    protected $hidden = [
        'created_by',
        'updated_by',
    ];

    protected $fillable = [
        'serial',
        'pin',
        'amount',
        'note',
        'status',
        'used_at',
        'used_by_ip',
        'created_by',
        'updated_by',
    ];

    protected function getModelCasts(): array
    {
        return [
            'amount' => 'float',
            'status' => 'integer',
            'used_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
