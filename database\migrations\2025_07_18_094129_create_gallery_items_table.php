<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gallery_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('gallery_group_id')->constrained('gallery_groups')->cascadeOnDelete()->comment('Liên kết đến bảng gallery_groups');
            $table->string('image_url', 2048)->nullable()->comment('URL của ảnh');
            $table->string('alt_text', 255)->nullable()->comment('<PERSON>ô tả ảnh');
            $table->string('title')->nullable()->comment('Tiêu đề ảnh');
            $table->string('link', 2048)->nullable()->comment('Liên kết ảnh');
            $table->enum('target', ['_blank', '_self'])->default('_self')->comment('Mở trong tab mới hay tab hiện tại');
            $table->tinyInteger('status')->default(1)->comment('Trạng thái ảnh (1: Active, 0: Inactive)');
            $table->integer('order')->default(0)->comment('Thứ tự sắp xếp ảnh');
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete()->comment('Người tạo danh mục');
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete()->comment('Người cập nhật danh mục');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gallery_items');
    }
};
