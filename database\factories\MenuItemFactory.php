<?php

namespace Database\Factories;

use App\Models\Menu;
use App\Models\MenuItem;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class MenuItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = MenuItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->words(2, true);

        return [
            'menu_id' => Menu::factory(),
            'title' => ucwords($title),
            'slug' => Str::slug($title),
            'order' => $this->faker->randomNumber(2),
            'parent_id' => null,
            'target' => '_self',
            'linkable_id' => null,
            'linkable_type' => null,
            'custom_url' => '/' . Str::slug($title),
            'status' => true,
        ];
    }
}
