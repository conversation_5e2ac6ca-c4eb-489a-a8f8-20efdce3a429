<?php

namespace App\Observers;

use App\Models\Post;

/**
 * Observer xử lý các sự kiện liên quan đến Post
 */
class PostObserver extends BaseObserver
{
    /**
     * Xử lý sự kiện "created" của Post.
     *
     * @param \App\Models\Post $post
     * @return void
     */
    public function created(Post $post): void
    {
        $this->log(
            action: 'create',
            description: "Tạo mới bài viết '{$post->title}'",
            model: $post,
            dataAfter: $post->toArray()
        );
    }

    /**
     * Xử lý sự kiện "updated" của Post.
     *
     * @param \App\Models\Post $post
     * @return void
     */
    public function updated(Post $post): void
    {
        $this->log(
            action: 'update',
            description: "Cập nhật bài viết '{$post->title}'",
            model: $post,
            dataBefore: $post->getOriginal(),
            dataAfter: $post->getChanges()
        );
    }

    /**
     * Xử lý sự kiện "deleting" của Post.
     *
     * @param \App\Models\Post $post
     * @return void
     */
    public function deleting(Post $post): void
    {
        $this->log(
            action: 'delete',
            description: "Xóa bài viết '{$post->title}'",
            model: $post,
            dataBefore: $post->toArray()
        );

        if (! $post->isForceDeleting()) {
            $post->slug = $post->slug . '_deleted_' . time();
            $post->saveQuietly();
        }
    }
}
