<?php

namespace App\Http\Requests\Statistics;

use App\Http\Requests\BaseRequest;

class GetDailyStatisticsRequest extends BaseRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'start_date' => 'required|date|date_format:Y-m-d',
            'end_date' => 'required|date|date_format:Y-m-d|after_or_equal:start_date',
            'type' => 'nullable|integer|in:1,2',
        ];
    }

    public function messages(): array
    {
        return [
            'start_date.required' => 'Ngày bắt đầu là bắt buộc',
            'start_date.date' => 'Ng<PERSON>y bắt đầu phải là ngày hợp lệ',
            'start_date.date_format' => 'Ngày bắt đầu phải có định dạng Y-m-d',
            'end_date.required' => 'Ngày kết thúc là bắt buộc',
            'end_date.date' => 'Ng<PERSON>y kết thúc phải là ngày hợp lệ',
            'end_date.date_format' => '<PERSON><PERSON><PERSON> kết thúc phải có định dạng Y-m-d',
            'end_date.after_or_equal' => 'Ngày kết thúc phải sau hoặc bằng ngày bắt đầu',
            'type.integer' => 'Loại dữ liệu phải là số nguyên',
            'type.in' => 'Loại dữ liệu chỉ được phép là 1 (Tài khoản) hoặc 2 (IP)',
        ];
    }

    /**
     * Mô tả các tham số query cho Scribe.
     *
     * Ghi chú:
     * - Endpoint sử dụng GET nên dùng queryParameters().
     * - start_date, end_date yêu cầu định dạng Y-m-d và end_date >= start_date.
     *
     * @return array<string, array<string, mixed>>
     */
    public function queryParameters(): array
    {
        return [
            'start_date' => [
                'description' => 'Ngày bắt đầu theo định dạng Y-m-d. Ví dụ: 2024-01-01',
                'example' => '2024-01-01',
                'required' => true,
            ],
            'end_date' => [
                'description' => 'Ngày kết thúc theo định dạng Y-m-d và phải >= start_date. Ví dụ: 2024-01-07',
                'example' => '2024-01-07',
                'required' => true,
            ],
            'type' => [
                'description' => 'Loại thống kê (tùy chọn): 1 - Tài khoản, 2 - IP',
                'example' => 1,
                'required' => false,
            ],
        ];
    }
}
