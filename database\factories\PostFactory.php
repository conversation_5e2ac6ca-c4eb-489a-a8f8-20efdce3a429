<?php

namespace Database\Factories;

use App\Models\Category;
use App\Models\Post;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Post>
 */
class PostFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Post::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $title = $this->faker->sentence(rand(5, 10));
        $slug = Str::slug($title);

        return [
            'category_id' => Category::query()->inRandomOrder()->first()->id ?? Category::factory(),
            'title' => $title,
            'slug' => $slug,
            'excerpt' => $this->faker->paragraph(2),
            'body' => $this->faker->paragraph(10),
            'cover_image' => $this->faker->imageUrl(),
            'meta_title' => $title,
            'meta_description' => $this->faker->sentence(),
            'meta_keywords' => implode(', ', $this->faker->words(5)),
            'status' => $this->faker->randomElement(['published', 'draft']),
            'is_hot' => $this->faker->boolean(20), // 20% chance of being hot
            'show_on_homepage' => $this->faker->boolean(30), // 30% chance of showing on homepage
            'published_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'created_by' => User::query()->inRandomOrder()->first()->id ?? User::factory(),
            'updated_by' => User::query()->inRandomOrder()->first()->id ?? User::factory(),
        ];
    }
}
