<?php

namespace App\Observers;

use App\Models\GalleryItem;

/**
 * Observer xử lý các sự kiện liên quan đến GalleryItem
 */
class GalleryItemObserver extends BaseObserver
{
    /**
     * Xử lý sự kiện "created" của GalleryItem.
     *
     * @param \App\Models\GalleryItem $galleryItem
     * @return void
     */
    public function created(GalleryItem $galleryItem): void
    {
        $this->log(
            action: 'create',
            description: "Thêm ảnh mới vào thư viện",
            model: $galleryItem,
            dataAfter: $galleryItem->toArray()
        );
    }

    /**
     * Xử lý sự kiện "updated" của GalleryItem.
     *
     * @param \App\Models\GalleryItem $galleryItem
     * @return void
     */
    public function updated(GalleryItem $galleryItem): void
    {
        $this->log(
            action: 'update',
            description: "Cập nhật thông tin ảnh trong thư viện",
            model: $galleryItem,
            dataBefore: $galleryItem->getOriginal(),
            dataAfter: $galleryItem->getChanges()
        );
    }

    /**
     * Xử lý sự kiện "deleting" của GalleryItem.
     *
     * @param \App\Models\GalleryItem $galleryItem
     * @return void
     */
    public function deleting(GalleryItem $galleryItem): void
    {
        $this->log(
            action: 'delete',
            description: "Xóa ảnh khỏi thư viện",
            model: $galleryItem,
            dataBefore: $galleryItem->toArray()
        );
    }
}
