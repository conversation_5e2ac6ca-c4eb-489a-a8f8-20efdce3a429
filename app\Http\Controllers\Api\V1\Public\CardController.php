<?php

namespace App\Http\Controllers\Api\V1\Public;

use App\Http\Controllers\Api\V1\BaseController;
use App\Http\Requests\CardPrint\VerifyCardRequest;
use App\Services\CardPrintService;
use Illuminate\Http\JsonResponse;

/**
 * @group Public - Card
 */
class CardController extends BaseController
{
    public function __construct(protected CardPrintService $cardPrintService)
    {
    }

    /**
     * Handle the card verification request.
     *
     * @param VerifyCardRequest $request
     * @return JsonResponse
     */
    public function verify(VerifyCardRequest $request): JsonResponse
    {
        $result = $this->cardPrintService->verifyAndUseCard(
            $request->validated(),
            $request->ip()
        );

        return $result['success']
            ? $this->success($result['data'], $result['message'])
            : $this->error($result['message'], $result['code']);
    }
}
