<?php

namespace App\Services;

use App\Repositories\Interfaces\SiteSettingRepositoryInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;

/**
 * Service xử lý các logic liên quan đến cài đặt hệ thống
 */
class SiteSettingService extends BaseCrudService
{
    /**
     * Service xử lý upload file
     *
     * @var FileUploadService
     */
    protected FileUploadService $fileUploadService;

    /**
     * Constructor
     *
     * @param FileUploadService $fileUploadService
     */
    public function __construct(FileUploadService $fileUploadService)
    {
        parent::__construct();
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Trả về lớp Repository cho Site Settings.
     *
     * @return class-string<SiteSettingRepositoryInterface>
     */
    protected function getRepositoryClass(): string
    {
        return SiteSettingRepositoryInterface::class;
    }

    /**
     * Tạo site setting mới
     * Hỗ trợ upload file cho các loại: image, document, video
     *
     * @param array $data Dữ liệu đầu vào
     * @param array $with <PERSON>ác quan hệ cần eager load
     * @return Model
     */
    public function create(array $data, array $with = []): Model
    {
        // Xử lý upload file nếu value là file và type thuộc các loại hỗ trợ
        if (isset($data['value']) && $data['value'] instanceof UploadedFile && isset($data['type'])) {
            // Upload file và lấy URL
            $fileInfo = $this->handleFileUpload($data['value'], $data['type']);

            // Cập nhật giá trị value thành đường dẫn file
            $data['value'] = $fileInfo['data']['url'];
        }

        return parent::create($data, $with);
    }

    /**
     * Cập nhật site setting
     * Hỗ trợ upload file mới cho các loại: image, document, video
     *
     * @param int $id ID của site setting
     * @param array $data Dữ liệu cập nhật
     * @param array $with Các quan hệ cần eager load
     * @return Model
     */
    public function update($id, array $data, array $with = []): Model
    {
        // Lấy dữ liệu cũ
        $oldData = $this->read($id)->toArray();
        $type = $data['type'] ?? $oldData['type'] ?? null;

        // Xử lý upload file nếu value là file
        if (isset($data['value']) && $data['value'] instanceof UploadedFile) {
            // Lấy đường dẫn file cũ (nếu có)
            $oldFileUrl = $oldData['value'] ?? null;

            // Upload file và lấy URL
            $fileInfo = $this->handleFileUpload($data['value'], $type, $oldFileUrl);

            // Cập nhật giá trị value thành đường dẫn file
            $data['value'] = $fileInfo['data']['url'];
        }

        return parent::update($id, $data, $with);
    }

    /**
     * Xử lý upload file cho site setting
     *
     * @param UploadedFile $file File được upload
     * @param string|null $type Loại file (image, document, video)
     * @param string|null $oldFileUrl URL của file cũ (nếu có) để xóa
     * @return array Kết quả upload
     */
    protected function handleFileUpload(UploadedFile $file, ?string $type, ?string $oldFileUrl = null): array
    {
        switch ($type) {
            case 'image':
                return $this->fileUploadService->uploadImage(
                    $file,
                    'site_settings',
                    'public',
                    $oldFileUrl
                );
            case 'document':
                return $this->fileUploadService->uploadDocument(
                    $file,
                    'site_settings',
                    'public',
                    $oldFileUrl
                );
            case 'video':
                return $this->fileUploadService->uploadVideo(
                    $file,
                    'site_settings',
                    'public',
                    $oldFileUrl
                );
            default:
                // Mặc định xử lý như file thông thường
                $url = $this->fileUploadService->uploadFile(
                    $file,
                    'site_settings',
                    'images', // mặc định là thư mục images
                    'public',
                    $oldFileUrl
                );

                return ['data' => ['url' => $url]];
        }
    }
}
