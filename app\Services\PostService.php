<?php

namespace App\Services;

use App\Repositories\Interfaces\CategoryRepositoryInterface;
use App\Repositories\Interfaces\PostRepositoryInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Service xử lý các logic liên quan đến bài viết
 */
class PostService extends BaseCrudService
{
    /**
     * Service xử lý upload file
     *
     * @var FileUploadService
     */
    protected FileUploadService $fileUploadService;

    /**
     * Repository xử lý các logic liên quan đến category
     *
     * @var CategoryRepositoryInterface
     */
    protected CategoryRepositoryInterface $categoryRepository;

    /**
     * Constructor
     *
     * @param FileUploadService $fileUploadService
     * @param CategoryRepositoryInterface $categoryRepository
     */
    public function __construct(FileUploadService $fileUploadService, CategoryRepositoryInterface $categoryRepository)
    {
        parent::__construct();
        $this->fileUploadService = $fileUploadService;
        $this->categoryRepository = $categoryRepository;
    }

    /**
     * Trả về lớp Repository cho Post.
     *
     * @return class-string<PostRepositoryInterface>
     */
    protected function getRepositoryClass(): string
    {
        return PostRepositoryInterface::class;
    }

    /**
     * Tạo bài viết mới
     * Hỗ trợ upload file hoặc sử dụng URL có sẵn cho cover_image
     *
     * @param array $data Dữ liệu đầu vào
     * @param array $with Các quan hệ cần eager load
     * @return Model
     */
    public function create(array $data, array $with = []): Model
    {
        // Xử lý cover_image nếu là file upload
        if (isset($data['cover_image']) && $data['cover_image'] instanceof UploadedFile) {
            // Upload file và lấy thông tin
            $fileInfo = $this->handleCoverImageUpload($data['cover_image']);
            // Cập nhật đường dẫn vào data
            $data['cover_image'] = $fileInfo['data']['url'];
        }

        // Nếu không phải file upload thì giữ nguyên giá trị (string URL)
        return parent::create($data, $with);
    }

    /**
     * Cập nhật bài viết
     * Hỗ trợ upload file mới hoặc sử dụng URL mới cho cover_image
     *
     * @param int $id ID của bài viết
     * @param array $data Dữ liệu cập nhật
     * @param array $with Các quan hệ cần eager load
     * @return Model
     */
    public function update($id, array $data, array $with = []): Model
    {
        // Xử lý cover_image nếu là file upload
        if (isset($data['cover_image']) && $data['cover_image'] instanceof UploadedFile) {
            // Lấy đường dẫn file hiện tại (nếu có)
            $oldImageUrl = $this->repository->find($id)->cover_image;
            // Upload file và lấy thông tin
            $fileInfo = $this->handleCoverImageUpload($data['cover_image'], $oldImageUrl);
            // Cập nhật đường dẫn vào data
            $data['cover_image'] = $fileInfo['data']['url'];
        }

        // Nếu không phải file upload thì giữ nguyên giá trị (string URL hoặc null để giữ ảnh cũ)
        return parent::update($id, $data, $with);
    }

    /**
     * Xử lý upload ảnh bìa cho bài viết
     *
     * @param UploadedFile $file File ảnh upload
     * @param string|null $oldImageUrl URL của ảnh cũ (nếu có) để xóa
     * @return array Kết quả upload
     */
    protected function handleCoverImageUpload(UploadedFile $file, $oldImageUrl = null): array
    {
        // Upload file và lấy thông tin
        return $this->fileUploadService->uploadImage(
            $file,
            'posts',
            'public',
            $oldImageUrl
        );
    }

    /**
     * Lấy danh sách các bài viết công khai.
     *
     * @param Request $request
     * @return LengthAwarePaginator
     */
    public function getPublicPosts(Request $request): LengthAwarePaginator
    {
        $filteredRequest = new Request($request->all());

        $filteredRequest->merge(['status' => 'published']);

        if ($filteredRequest->has('category_slug')) {
            $categorySlugs = is_array($filteredRequest->input('category_slug'))
                ? $filteredRequest->input('category_slug')
                : [$filteredRequest->input('category_slug')];

            $categoryIds = $this->categoryRepository->getIdsBySlugs($categorySlugs);

            if (! empty($categoryIds)) {
                $filteredRequest->merge(['category_id' => $categoryIds]);
            }
            $filteredRequest->offsetUnset('category_slug');
        }

        return $this->list($filteredRequest, ['category', 'createdBy']);
    }

    /**
     * Lấy chi tiết bài viết công khai theo slug.
     *
     * @param string $slug Slug của bài viết.
     * @return Model
     * @throws ModelNotFoundException Nếu không tìm thấy bài viết.
     */
    public function getPostDetailBySlug(string $slug): Model
    {
        return $this->repository->findByOrFail('slug', $slug, ['category', 'createdBy']);
    }
}
