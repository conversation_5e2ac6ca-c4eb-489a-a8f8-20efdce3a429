<?php

namespace App\Listeners\Auth;

use App\Events\Auth\LogoutSuccess;
use App\Listeners\BaseListener;

class LogSuccessfulLogout extends BaseListener
{
    /**
     * Xử lý sự kiện.
     *
     * @param \App\Events\Auth\LogoutSuccess $event
     * @return void
     */
    public function handle(LogoutSuccess $event): void
    {
        $this->log(
            action: 'logout',
            description: "Người dùng '{$event->user->name}' đã đăng xuất.",
            model: $event->user
        );
    }
}
