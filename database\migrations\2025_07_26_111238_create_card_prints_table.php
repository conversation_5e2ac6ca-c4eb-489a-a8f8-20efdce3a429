<?php

use App\Enums\CardPrintStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('card_prints', function (Blueprint $table) {
            $table->id();
            $table->char('serial', 15)->unique()->index();
            $table->char('pin', 12)->unique()->index();
            $table->decimal('amount', 10, 2);
            $table->string('note')->nullable();
            $table->tinyInteger('status')->default(CardPrintStatus::PENDING->value);

            $table->timestamp('used_at')->nullable();
            $table->ipAddress('used_by_ip')->nullable();

            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('card_prints');
    }
};
