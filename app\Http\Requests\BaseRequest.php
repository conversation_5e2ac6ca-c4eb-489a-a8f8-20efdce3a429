<?php

namespace App\Http\Requests;

use App\Traits\ApiResponse;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

/**
 * Base class cho tất cả Form Request trong hệ thống
 *
 * Class này customize cách Laravel handle validation errors cho API:
 * - Override default redirect behavior (không phù hợp cho API)
 * - Trả về JSON response với format chuẩn của hệ thống
 * - Đảm bảo consistency trong error response
 *
 * Tất cả Form Request khác nên extend từ class này thay vì FormRequest
 */
abstract class BaseRequest extends FormRequest
{
    use ApiResponse;

    /**
     * Override phương thức xử lý khi validation thất bại.
     *
     * Mặc định, Laravel sẽ redirect lại với lỗi (cho web) hoặc trả về JSON
     * với format mặc định (cho API). Method này được override để:
     * - <PERSON>ôn trả về JSON response.
     * - Sử dụng `ApiResponse` trait để có format lỗi nhất quán.
     *
     * @param Validator $validator Instance chứa các lỗi validation.
     * @throws HttpResponseException Luôn throw để trả về response ngay lập tức.
     */
    protected function failedValidation(Validator $validator)
    {
        // Throw HttpResponseException để Laravel trả về response ngay lập tức,
        // ngăn không cho request đi tiếp vào controller.
        throw new HttpResponseException(
            // Sử dụng phương thức error từ ApiResponse trait để tạo response lỗi.
            $this->error(
                'Dữ liệu không hợp lệ', // Message chung cho lỗi validation.
                422, // HTTP Status 422 Unprocessable Entity.
                $validator->errors()->toArray() // Chuyển MessageBag thành array.
            )
        );
    }
}
