<?php

namespace App\Services;

use App\Repositories\Interfaces\GalleryGroupRepositoryInterface;

class GalleryGroupService extends BaseCrudService
{
    protected function getRepositoryClass(): string
    {
        return GalleryGroupRepositoryInterface::class;
    }

    /**
     * L<PERSON>y chi tiết nhóm thư viện ảnh công khai theo location_key.
     *
     * @param string $locationKey location_key của nhóm thư viện ảnh.
     * @return \Illuminate\Database\Eloquent\Model
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException Nếu không tìm thấy nhóm thư viện ảnh.
     */
    public function getGalleryGroupByLocationKey(string $locationKey)
    {
        $galleryGroup = $this->repository->findByOrFail('location_key', $locationKey);

        $galleryGroup->load(['galleryItems' => function ($query) {
            $query->where('status', 1)->orderBy('order', 'asc');
        }]);

        return $galleryGroup;
    }
}
