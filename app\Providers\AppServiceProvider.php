<?php

namespace App\Providers;

use App\Models\CardPrint;
use App\Models\Category;
use App\Models\GalleryGroup;
use App\Models\GalleryItem;
use App\Models\Game;
use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\Post;
use App\Models\Role;
use App\Models\SiteSetting;
use App\Models\SiteSettingGroup;
use App\Models\StaticPage;
use App\Models\User;
use App\Observers\CardPrintObserver;
use App\Observers\CategoryObserver;
use App\Observers\GalleryGroupObserver;
use App\Observers\GalleryItemObserver;
use App\Observers\GameObserver;
use App\Observers\MenuItemObserver;
use App\Observers\MenuObserver;
use App\Observers\PostObserver;
use App\Observers\RoleObserver;
use App\Observers\SiteSettingGroupObserver;
use App\Observers\SiteSettingObserver;
use App\Observers\StaticPageObserver;
use App\Observers\UserObserver;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if ($this->app->environment('staging', 'production')) {
            URL::forceScheme('https');
        }

        // Đăng ký các observer để ghi log hoạt động
        Category::observe(CategoryObserver::class);
        Post::observe(PostObserver::class);
        MenuItem::observe(MenuItemObserver::class);
        SiteSetting::observe(SiteSettingObserver::class);
        SiteSettingGroup::observe(SiteSettingGroupObserver::class);
        StaticPage::observe(StaticPageObserver::class);
        GalleryGroup::observe(GalleryGroupObserver::class);
        GalleryItem::observe(GalleryItemObserver::class);
        Menu::observe(MenuObserver::class);
        User::observe(UserObserver::class);
        Role::observe(RoleObserver::class);
        CardPrint::observe(CardPrintObserver::class);
        Game::observe(GameObserver::class);
    }
}
