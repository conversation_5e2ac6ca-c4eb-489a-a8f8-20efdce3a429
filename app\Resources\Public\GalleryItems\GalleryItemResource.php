<?php

namespace App\Resources\Public\GalleryItems;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource để hiển thị thông tin của một gallery item cho người dùng cuối.
 */
class GalleryItemResource extends JsonResource
{
    /**
     * Dữ liệu đặc biệt cho resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'image_url' => $this->resource->image_url,
            'alt_text' => $this->resource->alt_text,
            'title' => $this->resource->title,
            'link' => $this->resource->link,
            'target' => $this->resource->target,
            'order' => $this->resource->order,
        ];
    }
}
