<?php

namespace App\Resources\CardPrint;

use App\Enums\CardPrintStatus;
use App\Resources\BaseResource;

class CardPrintResource extends BaseResource
{
    /**
     * @param $request
     * @return array
     */
    protected function resourceData($request): array
    {
        $statusEnum = CardPrintStatus::from($this->status);

        return [
            'status_label' => $statusEnum->label(),
            'created_by' => $this->whenLoaded('createdBy', function () {
                return $this->createdBy->name;
            }),
        ];
    }
}
