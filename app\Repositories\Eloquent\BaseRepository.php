<?php

namespace App\Repositories\Eloquent;

use App\Repositories\Interfaces\BaseRepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;

/**
 * Class BaseRepository
 *
 * Lớp triển khai cơ sở cho BaseRepositoryInterface, cung cấp logic CRUD chung.
 * Các repository cụ thể sẽ kế thừa lớp này và chỉ cần định nghĩa getModelClass().
 */
abstract class BaseRepository implements BaseRepositoryInterface
{
    /**
     * Đối tượng model Eloquent được sử dụng trong repository.
     *
     * @var Model
     */
    protected Model $model;

    // Định nghĩa các trường mặc định có thể tìm kiếm toàn văn
    // Mỗi repository con sẽ override nếu cần
    protected array $searchableFields = [];

    /**
     * Tr<PERSON> về tên lớp Model mà repository này sẽ thao tác.
     *
     * Repository con BẮT BUỘC phải triển khai phương thức này.
     * Ví dụ:
     * `return \App\Models\User::class;`
     *
     * @return string
     */
    abstract protected function getModelClass(): string;

    /**
     * Khởi tạo repository và inject model tương ứng.
     */
    public function __construct()
    {
        $modelClass = $this->getModelClass();
        $this->model = app($modelClass);
    }

    /**
     * {@inheritdoc}
     */
    public function getModelName(): string
    {
        return class_basename($this->model);
    }

    /**
     * {@inheritdoc}
     */
    public function all(): Collection
    {
        return $this->model->all();
    }

    /**
     * {@inheritdoc}
     */
    public function paginate(array $params = [], array $with = []): LengthAwarePaginator
    {
        $limit = $params['limit'] ?? config('common.pagination.limit', 15);

        $query = $this->model->with($with);

        // Các tham số dành riêng cho phân trang và sắp xếp, không dùng để lọc
        $specialParams = ['page', 'limit', 'sort_by', 'sort_order'];

        // Lọc theo các tham số còn lại
        foreach ($params as $key => $value) {
            if (! in_array($key, $specialParams)) {
                if ($value === 'IS_NULL') {
                    $query->whereNull($key);
                } else {
                    $query->where($key, $value);
                }
            }
        }

        // Sắp xếp kết quả
        if (isset($params['sort_by'])) {
            $sortOrder = $params['sort_order'] ?? 'asc';
            $query->orderBy($params['sort_by'], $sortOrder);
        }

        return $query->paginate($limit);
    }

    /**
     * Phân trang dữ liệu dựa trên các tham số tìm kiếm và truy vấn chung.
     *
     * @param array $searchParams Các tham số tìm kiếm cụ thể (ví dụ: ['status' => 'active'])
     * @param array $params Các tham số phân trang và truy vấn chung (ví dụ: ['limit' => 20, 'search_query' => 'keyword'])
     * @param array $with Các quan hệ cần eager load
     * @return LengthAwarePaginator
     */
    public function paginateBySearchParams(array $searchParams, array $params = [], array $with = []): LengthAwarePaginator
    {
        // Lấy giới hạn phân trang từ tham số hoặc cấu hình mặc định
        $limit = $params['limit'] ?? config('common.pagination.limit', 15);

        // Khởi tạo query builder với các quan hệ cần eager load
        $query = $this->model->with($with);

        // Lấy từ khóa tìm kiếm chung từ tham số
        $searchQuery = $params['search_query'] ?? null;

        // 1. Áp dụng các tham số tìm kiếm cụ thể
        $this->applySpecificSearchParams($query, $searchParams);

        // 2. Áp dụng tìm kiếm chung nếu có từ khóa và có trường tìm kiếm
        if (! empty($searchQuery) && ! empty($this->searchableFields)) {
            $this->applyGeneralSearchQuery($query, $searchQuery, $this->searchableFields);
        }

        // Sắp xếp và phân trang kết quả
        return $query
            ->orderBy($params['sort_by'] ?? 'id', $params['sort_order'] ?? 'asc')
            ->paginate($limit);
    }

    /**
     * Danh sách các trường sẽ sử dụng so sánh chính xác (=) thay vì LIKE
     *
     * @var array
     */
    protected $exactMatchFields = [
        'id',
        'gallery_group_id',
        'user_id',
        'created_by',
        'updated_by',
        'status',
        'type',
        'parent_id',
        // Thêm các trường khác cần so sánh chính xác vào đây
    ];

    /**
     * Áp dụng các tham số tìm kiếm cụ thể cho query.
     *
     * @param Builder $query
     * @param array $searchParams
     * @return void
     */
    protected function applySpecificSearchParams(Builder $query, array $searchParams): void
    {
        foreach ($searchParams as $field => $value) {
            if ($value === 'IS_NULL') {
                $query->whereNull($field);
            } elseif (is_array($value)) {
                // Hỗ trợ tìm kiếm với IN (ví dụ: status[]=active&status[]=inactive)
                $query->whereIn($field, $value);
            } elseif (in_array($field, $this->exactMatchFields) || str_ends_with($field, '_id') || str_ends_with($field, '_type')) {
                $query->where($field, '=', $value);
            } else {
                $query->where($field, 'LIKE', "%$value%");
            }
        }
    }

    /**
     * Áp dụng truy vấn tìm kiếm chung lên các trường có thể tìm kiếm.
     *
     * @param Builder $query
     * @param string $searchQuery
     * @param array $searchableFields
     * @return void
     */
    protected function applyGeneralSearchQuery(Builder $query, string $searchQuery, array $searchableFields): void
    {
        $query->where(function (Builder $q) use ($searchQuery, $searchableFields) {
            foreach ($searchableFields as $index => $field) {
                // Áp dụng OR where cho các trường khác nhau
                if ($index === 0) {
                    $q->where($field, 'LIKE', "%$searchQuery%");
                } else {
                    $q->orWhere($field, 'LIKE', "%$searchQuery%");
                }
            }
        });
    }

    /**
     * {@inheritdoc}
     */
    public function find(int $id): ?Model
    {
        return $this->model->find($id);
    }

    /**
     * {@inheritdoc}
     */
    public function findOrFail(int $id, array $with = []): Model
    {
        try {
            return $this->model->with($with)->findOrFail($id);
        } catch (ModelNotFoundException $e) {
            $modelName = method_exists($this, 'getModelName') ? $this->getModelName() : class_basename($this->getModelClass());

            throw new ModelNotFoundException("Không tìm thấy {$modelName} với ID {$id}");
        }
    }

    /**
     * {@inheritdoc}
     */
    public function findBy(string $field, mixed $value): ?Model
    {
        return $this->model->where($field, $value)->first();
    }

    /**
     * Tìm hoặc tạo mới record
     *
     * @param array $attributes
     * @param array $values
     * @return Model
     */
    public function firstOrCreate(array $attributes, array $values = []): Model
    {
        return $this->model->firstOrCreate($attributes, $values);
    }

    /**
     * {@inheritdoc}
     */
    public function findByOrFail(string $field, mixed $value, array $with = []): Model
    {
        return $this->model->with($with)->where($field, $value)->firstOrFail();
    }

    /**
     * {@inheritdoc}
     */
    public function create(array $data): Model
    {
        return $this->model->create($data);
    }

    /**
     * {@inheritdoc}
     */
    public function update(int $id, array $data): Model
    {
        $model = $this->findOrFail($id);
        $model->update($data);

        return $model;
    }

    /**
     * {@inheritdoc}
     */
    public function delete(int $id): bool
    {
        $model = $this->findOrFail($id);

        return $model->delete();
    }

    /**
     * {@inheritdoc}
     */
    public function restore(int $id): bool
    {
        $model = $this->findWithTrashed($id);
        if ($model && method_exists($model, 'restore')) {
            return $model->restore();
        }

        return false;
    }

    /**
     * {@inheritdoc}
     */
    public function forceDelete(int $id): bool
    {
        $model = $this->findWithTrashed($id);
        if ($model) {
            return $model->forceDelete();
        }

        return false;
    }

    /**
     * {@inheritdoc}
     */
    public function findWithTrashed(int $id): ?Model
    {
        if (method_exists($this->model, 'withTrashed')) {
            return $this->model->withTrashed()->find($id);
        }

        return $this->find($id);
    }

    /**
     * {@inheritdoc}
     */
    public function findOnlyTrashed(int $id): ?Model
    {
        if (method_exists($this->model, 'onlyTrashed')) {
            return $this->model->onlyTrashed()->find($id);
        }

        return null;
    }
}
