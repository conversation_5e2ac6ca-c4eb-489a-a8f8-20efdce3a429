<?php

namespace App\Repositories\Eloquent;

use App\Repositories\Interfaces\PermissionRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Spatie\Permission\Models\Permission;

class PermissionRepository extends BaseRepository implements PermissionRepositoryInterface
{
    /**
     * @inheritDoc
     */
    protected function getModelClass(): string
    {
        return Permission::class;
    }

    /**
     * Lấy tất cả permissions
     *
     * @return Collection
     */
    public function getAllPermissions(): Collection
    {
        return $this->model->all();
    }

    /**
     * Lấy permissions theo group code
     *
     * @param string $groupCode
     * @return Collection
     */
    public function getPermissionsByGroup(string $groupCode): Collection
    {
        return $this->model->where('group_code', $groupCode)->get();
    }

    /**
     * Lấy permissions được nhóm theo group_code
     *
     * @return array
     */
    public function getPermissionsGroupedByCode(): array
    {
        $permissions = $this->model->all();
        $grouped = [];

        foreach ($permissions as $permission) {
            $grouped[$permission->group_code][] = $permission;
        }

        return $grouped;
    }
}
