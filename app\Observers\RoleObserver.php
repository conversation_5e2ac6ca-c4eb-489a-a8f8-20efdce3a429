<?php

namespace App\Observers;

use App\Models\Role;

/**
 * Observer xử lý các sự kiện liên quan đến Role
 */
class RoleObserver extends BaseObserver
{
    /**
     * Xử lý sự kiện "created" của Role.
     *
     * @param \App\Models\Role $role
     * @return void
     */
    public function created(Role $role): void
    {
        $this->log(
            action: 'create',
            description: "Tạo mới vai trò '{$role->name}'",
            model: $role,
            dataAfter: $role->toArray()
        );
    }

    /**
     * Xử lý sự kiện "updated" của Role.
     *
     * @param \App\Models\Role $role
     * @return void
     */
    public function updated(Role $role): void
    {
        $this->log(
            action: 'update',
            description: "Cập nhật vai trò '{$role->name}'",
            model: $role,
            dataBefore: $role->getOriginal(),
            dataAfter: $role->getChanges()
        );
    }

    /**
     * Xử lý sự kiện "deleting" của Role.
     *
     * @param \App\Models\Role $role
     * @return void
     */
    public function deleting(Role $role): void
    {
        $this->log(
            action: 'delete',
            description: "Xóa vai trò '{$role->name}'",
            model: $role,
            dataBefore: $role->toArray()
        );
    }
}
