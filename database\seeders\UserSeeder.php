<?php

namespace Database\Seeders;

use App\Enums\UserStatus;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // ✅ Kiểm tra role "Super Administrator"
        $adminRole = Role::where('name', 'Super Administrator')->first();
        if (! $adminRole) {
            $this->command->error('Vai trò Super Administrator chưa tồn tại! Hãy chạy RoleSeeder trước.');

            return;
        }

        // ✅ Tạo hoặc lấy admin
        $admin = User::query()->firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => '<EMAIL>',
                'avatar' => '', // Sử dụng cột 'avatar' đã đ<PERSON> cập nhật
                'status' => UserStatus::ACTIVE->value, // Sử dụng enum
            ]
        );

        // ✅ <PERSON>án role đúng cách với model_type
        if (! DB::table('model_has_roles')->where('model_id', $admin->id)->where('role_id', $adminRole->id)->exists()) {
            DB::table('model_has_roles')->insert([
                'role_id' => $adminRole->id,
                'model_type' => User::class,
                'model_id' => $admin->id,
            ]);
        }

        $this->command->info('Admin user đã được tạo hoặc cập nhật thành công!');
    }
}
