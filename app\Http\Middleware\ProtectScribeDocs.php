<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ProtectScribeDocs
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Lấy thông tin đăng nhập được định nghĩa trong file .env
        $expectedUser = env('SCRIBE_BASIC_AUTH_USER');
        $expectedPass = env('SCRIBE_BASIC_AUTH_PASS');

        // Lấy thông tin người dùng cung cấp từ request header
        $user = $request->getUser();
        $pass = $request->getPassword();

        // Kiểm tra xem thông tin có khớp không
        if ($user === $expectedUser && $pass === $expectedPass) {
            // <PERSON><PERSON><PERSON> khớp, cho phép truy cập
            return $next($request);
        }

        // Nếu không khớp, tr<PERSON> về lỗi 401 và yêu cầu xác thực
        return response('Unauthorized.', 401, ['WWW-Authenticate' => 'Basic']);
    }
}
