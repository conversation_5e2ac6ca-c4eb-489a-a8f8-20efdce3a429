<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gallery_groups', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('Tên định danh cho admin (e.g., "Menu đầu trang", "Menu chân trang")');
            $table->string('location_key')->unique()->comment('Key để frontend gọi lên (e.g., header, footer)');
            $table->tinyInteger('location_display')->comment('<PERSON><PERSON>i hiển thị: 1 - Homepage, 2 - Landing, 3 - Both');
            $table->string('description')->nullable()->comment('Mô tả');
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete()->comment('Ngườ<PERSON> tạo danh mục');
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete()->comment('<PERSON><PERSON>ời cập nhật danh mục');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gallery_groups');
    }
};
