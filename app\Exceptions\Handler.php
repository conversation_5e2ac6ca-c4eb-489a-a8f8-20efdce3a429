<?php

namespace App\Exceptions;

use App\Traits\ApiResponse;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

/**
 * Trình xử lý ngoại lệ (Exception Handler) tùy chỉnh cho ứng dụng.
 *
 * Class này kế thừa và mở rộng Exception Handler mặc định của Laravel,
 * đảm bảo mọi lỗi phát sinh trong ứng dụng API đều được trả về dưới dạng
 * một JSON response nhất quán và chuẩn hóa.
 *
 * Chức năng chính:
 * - Bắt và xử lý tất cả các ngoại lệ (exceptions).
 * - Chuyển đổi các lỗi HTTP (404, 403, 405...) thành JSON response.
 * - <PERSON><PERSON><PERSON> dạng lỗi validation (422) với danh sách các trường bị lỗi.
 * - Cung cấp một định dạng lỗi chung cho các trường hợp còn lại.
 */
class Handler extends ExceptionHandler
{
    use ApiResponse;

    /**
     * Các exception sẽ không được report (log)
     * Ví dụ: thường để trống hoặc bỏ qua lỗi không nghiêm trọng
     *
     * @var array
     */
    protected $dontReport = [];

    /**
     * Các input sẽ không được flash lại khi xảy ra lỗi validation
     * (chủ yếu dùng cho web, tuy nhiên vẫn giữ để đảm bảo an toàn)
     *
     * @var array
     */
    protected $dontFlash = ['password', 'password_confirmation'];

    /**
     * Đăng ký custom render logic cho exception
     *
     * - Chặn toàn bộ exception nếu là request API (expectsJson)
     * - Chuyển sang method xử lý riêng `handleApiException`
     */
    public function register(): void
    {
        $this->renderable(function (Throwable $e, $request) {
            // Kiểm tra request là API (expects JSON) hoặc path bất kỳ (API-only)
            if ($request->expectsJson() || $request->is('*')) {
                return $this->handleApiException($request, $e);
            }
        });
    }

    /**
     * Xử lý lỗi chưa xác thực (AuthenticationException)
     *
     * - Ghi đè để đảm bảo API luôn trả về JSON
     *
     * @param \Illuminate\Http\Request $request
     * @param AuthenticationException $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function unauthenticated($request, AuthenticationException $exception)
    {
        // Đối với các request API, luôn trả về JSON với mã lỗi 401.
        return $this->error($exception->getMessage(), 401);
    }

    /**
     * Xử lý tất cả các loại exception cho API
     *
     * - Trả về response JSON thống nhất
     * - Nhận biết các lỗi thường gặp của Laravel/Symfony
     * - Hỗ trợ exception có mã lỗi tùy chỉnh (Custom Exception)
     *
     * @param \Illuminate\Http\Request $request
     * @param \Throwable $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleApiException($request, Throwable $exception)
    {
        // Mặc định là lỗi hệ thống
        $status = 500;
        $message = 'Internal server error.';

        /**
         * Nhận diện các exception phổ biến của Laravel
         */
        if ($exception instanceof NotFoundHttpException) {
            $status = 404;
            $message = 'Resource not found.';
        } elseif ($exception instanceof MethodNotAllowedHttpException) {
            $status = 405;
            $message = 'Method not allowed.';
        } elseif ($exception instanceof \Illuminate\Auth\Access\AuthorizationException) {
            $status = 403;
            $message = 'Forbidden. You do not have permission to access this resource.';
        } elseif ($exception instanceof \Illuminate\Validation\ValidationException) {
            // Trả về lỗi validate theo format chuẩn
            return response()->json(
                [
                    'success' => false,
                    'message' => 'Dữ liệu gửi lên không hợp lệ.',
                    'errors' => $exception->errors(),
                ],
                422
            );
        } elseif ($exception instanceof AuthenticationException) {
            $status = 401;
            $message = 'Unauthenticated.';
        }

        /**
         * Nếu là custom exception có mã lỗi hợp lệ (100–599)
         * - Override mã HTTP code mặc định
         */
        if (method_exists($exception, 'getCode') && $exception->getCode() >= 100 && $exception->getCode() < 600) {
            $status = $exception->getCode();
        }

        /**
         * Nếu exception có message, override message mặc định
         */
        if ($exception->getMessage()) {
            $message = $exception->getMessage();
        }

        return $this->error($message, $status);
    }
}
