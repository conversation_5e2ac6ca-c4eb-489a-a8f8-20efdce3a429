<?php

namespace App\Http\Requests\Auth;

use App\Http\Requests\BaseRequest;

class UpdateProfileRequest extends BaseRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'string', 'max:255'],
            'avatar' => ['sometimes', 'nullable', 'image', 'max:2048'],
        ];
    }

    /**
     * Defines the parameters for the request body.
     *
     * @return array
     */
    public function bodyParameters(): array
    {
        return [
            'name' => [
                'description' => 'Tên mới của người dùng.',
                'example' => '<PERSON><PERSON><PERSON>',
            ],
            'avatar' => [
                'description' => 'Ảnh đại diện mới của người dùng (file ảnh).',
                'example' => null,
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'name.string' => 'Tên phải là chuỗi ký tự',
            'name.max' => 'Tên tối đa 255 ký tự',
            'avatar.image' => 'Avatar phải là file ảnh',
            'avatar.max' => 'Avatar tối đa 2MB',
        ];
    }
}
