<?php

namespace App\Repositories\Eloquent;

use App\Models\MenuItem;
use App\Repositories\Interfaces\MenuItemRepositoryInterface;

class MenuItemRepository extends BaseRepository implements MenuItemRepositoryInterface
{
    /**
     * <PERSON>h sách các trường có thể tìm kiếm toàn văn.
     *
     * @var array
     */
    protected array $searchableFields = [
        'title',
        'slug',
        'custom_url',
        'target',
    ];

    /**
     * Chỉ định model sẽ được sử dụng
     *
     * @return string
     */
    protected function getModelClass(): string
    {
        return MenuItem::class;
    }

    /**
     * Lấy cây menu item đang hoạt động dựa trên menu ID.
     *
     * @param int $menuId ID của menu.
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getMenuItemsTreeByMenuId(int $menuId)
    {
        return $this->model
            ->where('menu_id', $menuId)
            ->whereNull('parent_id')
            ->where('status', true)
            ->with(['children', 'linkable'])
            ->orderBy('order', 'asc')
            ->get();
    }
}
