<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\LinkableType;
use App\Http\Requests\DataLinkables\IndexDataLinkableRequest;
use App\Services\CategoryService;
use App\Services\PostService;
use App\Services\StaticPageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group CMS - Data Linkables
 *
 * API nội bộ (cho trang Quản trị) để lấy danh sách options cho MenuItem
 * nhằm liên kết đa hình tới Bài viết/Danh mục/Trang tĩnh.
 */
class DataLinkableController extends BaseController
{
    public function __construct(
        protected PostService $postService,
        protected CategoryService $categoryService,
        protected StaticPageService $staticPageService,
    ) {
    }

    /**
     * Lấy danh sách options cho linkable theo type
     *
     * Hỗ trợ các type: post | category | static_page
     * - post: lọc status = published, order by published_at desc
     * - category: lọc status = 1, order theo order asc, name asc
     * - static_page: lọc status = 1, order created_at desc
     *
     * @param IndexDataLinkableRequest $request
     * @return JsonResponse
     */
    public function index(IndexDataLinkableRequest $request): JsonResponse
    {
        // Lấy dữ liệu đã validate
        $validated = $request->validated();
        // type là số nguyên, ánh xạ về Enum để dùng nhất quán
        $type = LinkableType::from((int) $validated['type']);
        $search = $validated['search'] ?? null;
        $limit = (int) ($validated['limit'] ?? 20);

        // Tạo một Request mới để truyền vào Service::list()
        $serviceRequest = new Request();
        $serviceRequest->merge([
            'limit' => $limit,
        ]);

        if (! empty($search)) {
            $serviceRequest->merge(['search' => $search]);
        }

        // Áp dụng filter mặc định theo từng loại
        $serviceRequest->merge($type->defaultFilters());

        switch ($type) {
            case LinkableType::POST:
                $paginator = $this->postService->list($serviceRequest);
                $items = collect($paginator->items())->map(function ($item) use ($type) {
                    return [
                        'id' => $item->id,
                        'label' => $item->title,
                        'slug' => $item->slug,
                        'type' => $type->key(),
                        // Gợi ý model cho phía client set thẳng linkable_type
                        'model' => $type->modelClass(),
                    ];
                });

                break;

            case LinkableType::CATEGORY:
                $paginator = $this->categoryService->list($serviceRequest);
                $items = collect($paginator->items())->map(function ($item) use ($type) {
                    return [
                        'id' => $item->id,
                        'label' => $item->name,
                        'slug' => $item->slug,
                        'type' => $type->key(),
                        'model' => $type->modelClass(),
                    ];
                });

                break;

            case LinkableType::STATIC_PAGE:
                $paginator = $this->staticPageService->list($serviceRequest);
                $items = collect($paginator->items())->map(function ($item) use ($type) {
                    return [
                        'id' => $item->id,
                        'label' => $item->title,
                        'slug' => $item->slug,
                        'type' => $type->key(),
                        'model' => $type->modelClass(),
                    ];
                });

                break;
        }

        return $this->success([
            'items' => $items->values(),
            'pagination' => [
                'total' => $paginator->total(),
                'per_page' => $paginator->perPage(),
                'current_page' => $paginator->currentPage(),
                'last_page' => $paginator->lastPage(),
            ],
        ], 'Lấy danh sách options thành công');
    }
}
