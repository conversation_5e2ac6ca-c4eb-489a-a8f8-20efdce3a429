<?php

namespace App\Http\Controllers\Api\V1\Public;

use App\Http\Controllers\Api\V1\BaseController;
use App\Resources\Public\MenuItems\MenuItemResource;
use App\Services\MenuItemService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;

/**
 * @group Public - Menus
 *
 * API công khai để lấy dữ liệu Menu
 */
class MenuItemController extends BaseController
{
    /**
     * Khởi tạo controller với các service cần thiết.
     *
     * @param MenuItemService $menuItemService
     */
    public function __construct(
        protected MenuItemService $menuItemService
    ) {
    }

    /**
     * Lấy cây menu theo slug của nhóm menu
     *
     * Trả về một cấu trúc cây các menu item đang hoạt động,
     * dựa trên slug của nhóm menu (ví dụ: 'main-menu').
     *
     * @urlParam menu_location_key string required Slug của nhóm menu. Example: main-menu
     *
     * @param string $menu_location_key
     * @header X-Public-Api-Secret string required Khóa bí mật API công khai. Example: your_super_secret_key_here
     * @return JsonResponse
     */
    public function listByMenuLocationKey(string $menu_location_key): JsonResponse
    {
        try {
            $menuItems = $this->menuItemService->getPublicMenuItemsByMenuLocationKey($menu_location_key);

            return $this->success(MenuItemResource::collection($menuItems), 'Lấy menu thành công.');
        } catch (ModelNotFoundException $e) {
            return $this->error('Không tìm thấy menu.', 404);
        }
    }
}
