<?php

namespace App\Services;

use App\Repositories\Interfaces\SiteSettingGroupRepositoryInterface;

/**
 * Service cho nhóm cài đặt hệ thống
 */
class SiteSettingGroupService extends BaseCrudService
{
    /**
     * Lấy class repository tương ứng
     *
     * @return string
     */
    protected function getRepositoryClass(): string
    {
        return SiteSettingGroupRepositoryInterface::class;
    }

    /**
     * Lấy chi tiết nhóm cài đặt và các cài đặt công khai của nó.
     *
     * @param string $groupKey Key của nhóm cài đặt.
     * @return \Illuminate\Database\Eloquent\Model
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException Nếu không tìm thấy nhóm.
     */
    public function getPublicGroupWithSettings(string $groupKey)
    {
        $settingGroup = $this->repository->findByOrFail('group_key', $groupKey);

        // Tải các site settings đang hoạt động (status = 1) và không riêng tư (is_private = false)
        $settingGroup->load(['siteSettings' => function ($query) {
            $query->where('status', 1)->where('is_private', false);
        }]);

        return $settingGroup;
    }
}
