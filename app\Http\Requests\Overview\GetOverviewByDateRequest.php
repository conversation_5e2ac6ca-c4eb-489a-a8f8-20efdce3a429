<?php

namespace App\Http\Requests\Overview;

use App\Enums\OverviewType;
use App\Enums\TimePeriod;
use App\Http\Requests\BaseRequest;

class GetOverviewByDateRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'time_period' => 'required|integer|in:' . implode(',', TimePeriod::getValues()),
            'type' => 'required|integer|in:' . implode(',', OverviewType::getValues()),
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'time_period.required' => 'Khoảng thời gian là bắt buộc',
            'time_period.integer' => 'Khoảng thời gian phải là số nguyên',
            'time_period.in' => '<PERSON><PERSON>ảng thời gian phải là một trong các giá trị: ' . implode(', ', TimePeriod::getValues()),
            'type.required' => 'Loại dữ liệu là bắt buộc',
            'type.integer' => 'Loại dữ liệu phải là số nguyên',
            'type.in' => 'Loại phải là một trong các giá trị: ' . implode(', ', OverviewType::getValues()),
        ];
    }

    /**
     * Mô tả các tham số query cho Scribe.
     *
     * Ghi chú:
     * - Endpoint sử dụng phương thức GET nên tài liệu sẽ đọc từ queryParameters().
     * - Sử dụng enum để liệt kê giá trị hợp lệ giúp tài liệu rõ ràng, đồng bộ với validation rules().
     *
     * @return array<string, array<string, mixed>>
     */
    public function queryParameters(): array
    {
        $timePeriodOptions = array_map(
            fn (TimePeriod $case) => $case->value . ' - ' . $case->getLabel(),
            TimePeriod::cases()
        );
        $typeOptions = array_map(
            fn (OverviewType $case) => $case->value . ' - ' . $case->getLabel(),
            OverviewType::cases()
        );

        return [
            'time_period' => [
                'description' => 'Khoảng thời gian cần thống kê. Giá trị hợp lệ: ' . implode(', ', $timePeriodOptions),
                'example' => TimePeriod::TODAY->value,
                'required' => true,
            ],
            'type' => [
                'description' => 'Loại dữ liệu thống kê. Giá trị hợp lệ: ' . implode(', ', $typeOptions),
                'example' => OverviewType::REGISTERED_ACCOUNTS->value,
                'required' => true,
            ],
        ];
    }
}
