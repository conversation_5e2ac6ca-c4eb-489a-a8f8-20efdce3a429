{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"prepare": "husky", "format:php": "./vendor/bin/php-cs-fixer fix", "format:js": "prettier --write ."}, "devDependencies": {"@prettier/plugin-php": "^0.22.4", "concurrently": "^9.0.1", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2"}, "lint-staged": {"*.php": ["php ./vendor/bin/php-cs-fixer fix --config .php-cs-fixer.dist.php"], "*.{js,ts,css,scss,json,md}": "prettier --write"}}