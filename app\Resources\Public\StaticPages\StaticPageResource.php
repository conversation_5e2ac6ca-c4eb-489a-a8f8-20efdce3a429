<?php

namespace App\Resources\Public\StaticPages;

use App\Resources\BaseResource;

/**
 * Resource để hiển thị thông tin của một trang tĩnh cho người dùng cuối.
 */
class StaticPageResource extends BaseResource
{
    /**
     * Dữ liệu đặc biệt cho resource (relationships, computed properties)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    protected function resourceData($request): array
    {
        return [
            'title' => $this->resource->title,
            'slug' => $this->resource->slug,
            'body' => $this->resource->body,
            'meta_title' => $this->resource->meta_title,
            'meta_description' => $this->resource->meta_description,
            'meta_keywords' => $this->resource->meta_keywords,
        ];
    }
}
