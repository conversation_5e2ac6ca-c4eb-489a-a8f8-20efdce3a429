<?php

namespace App\Http\Requests\CardPrint;

use App\Enums\CardPrintStatus;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class CancelCardPrintRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
    * Prepare the data for validation.
    */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'id' => $this->route('id'),
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => [
                Rule::exists('card_prints', 'id')->where('status', CardPrintStatus::PENDING->value),
            ],
        ];
    }

    /**
     * <PERSON><PERSON> tả các tham số của request body cho tài liệu API.
     *
     * @return array
     */
    public function bodyParameters(): array
    {
        return [];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'id.exists' => 'Thẻ Mcoin không tồn tại hoặc không ở trạng thái chưa sử dụng.',
        ];
    }
}
