<?php

namespace App\Repositories\Eloquent;

use App\Models\UserLog;
use App\Repositories\Interfaces\UserLogRepositoryInterface;

/**
 * Repository xử lý các thao tác với model UserLog.
 */
class UserLogRepository extends BaseRepository implements UserLogRepositoryInterface
{
    /**
     * <PERSON>h sách các trường có thể tìm kiếm toàn văn.
     *
     * @var array
     */
    protected array $searchableFields = [
        'action',
        'description',
        'details',
        'ip',
        'user_agent',
        'url',
        'user_agent',
    ];

    /**
     * <PERSON><PERSON>c đ<PERSON>nh model class sử dụng cho repository này.
     *
     * @return string
     */
    protected function getModelClass(): string
    {
        return UserLog::class;
    }
}
