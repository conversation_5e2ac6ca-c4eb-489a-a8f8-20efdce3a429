<?php

namespace App\Observers;

use App\Models\StaticPage;

/**
 * Observer cho model StaticPage
 */
class StaticPageObserver extends BaseObserver
{
    /**
     * Xử lý sự kiện "created" của StaticPage.
     *
     * @param \App\Models\StaticPage $staticPage
     * @return void
     */
    public function created(StaticPage $staticPage): void
    {
        $this->log(
            action: 'create',
            description: "Tạo mới trang tĩnh '{$staticPage->title}'",
            model: $staticPage,
            dataAfter: $staticPage->toArray()
        );
    }

    /**
     * Xử lý sự kiện "updated" của StaticPage.
     *
     * @param \App\Models\StaticPage $staticPage
     * @return void
     */
    public function updated(StaticPage $staticPage): void
    {
        $this->log(
            action: 'update',
            description: "Cập nhật trang tĩnh '{$staticPage->title}'",
            model: $staticPage,
            dataBefore: $staticPage->getOriginal(),
            dataAfter: $staticPage->getChanges()
        );
    }

    /**
     * <PERSON>ử lý sự kiện "deleting" của StaticPage.
     *
     * @param \App\Models\StaticPage $staticPage
     * @return void
     */
    public function deleting(StaticPage $staticPage): void
    {
        $this->log(
            action: 'delete',
            description: "Xóa trang tĩnh '{$staticPage->title}'",
            model: $staticPage,
            dataBefore: $staticPage->toArray()
        );

        if (! $staticPage->isForceDeleting()) {
            $staticPage->slug = $staticPage->slug . '_deleted_' . time();
            $staticPage->saveQuietly();
        }
    }
}
