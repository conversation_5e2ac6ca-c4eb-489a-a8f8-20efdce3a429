<?php

namespace App\Repositories\Interfaces;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;

/**
 * <PERSON><PERSON><PERSON> diện cho UserRepository, kế thừa các phương thức cơ bản từ BaseRepositoryInterface.
 * <PERSON><PERSON> thể định nghĩa thêm các phương thức đặc thù cho User tại đây.
 */
interface UserRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Get users with filters and pagination
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getUsersWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator;
}
