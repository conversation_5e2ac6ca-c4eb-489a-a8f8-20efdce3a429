<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\Categories\StoreCategoryRequest;
use App\Http\Requests\Categories\UpdateCategoryRequest;
use App\Resources\Category\CategoryResource;
use App\Resources\Category\CategoryResourceCollection;
use App\Services\CategoryService;
use Illuminate\Http\Request;

/**
 * @group Danh mục
 */
class CategoryController extends BaseController
{
    /**
     * CategoryService instance.
     */
    protected CategoryService $service;

    /**
     * Khởi tạo controller với CategoryService.
     */
    public function __construct(CategoryService $service)
    {
        $this->service = $service;
    }

    /**
     * Lấy danh sách các category.
     */
    public function index(Request $request)
    {
        $data = $this->service->list($request, ['createdBy', 'updatedBy']);

        return $this->success(new CategoryResourceCollection($data), 'Lấy danh sách category thành công');
    }

    /**
     * <PERSON><PERSON>u một category mới.
     */
    public function store(StoreCategoryRequest $request)
    {
        $data = $this->service->create($request->validated(), ['createdBy', 'updatedBy']);

        return $this->created(new CategoryResource($data), 'Tạo category thành công');
    }

    /**
     * Hiển thị một category cụ thể.
     */
    public function show(string $id)
    {
        $data = $this->service->read($id, ['createdBy', 'updatedBy']);

        return $this->success(new CategoryResource($data), 'Lấy thông tin category thành công');
    }

    /**
     * Cập nhật một category.
     *
     * Lưu ý: Khi cần upload file (meta_image), hãy sử dụng Method Spoofing:
     * - Gửi request HTTP POST thay vì PUT
     * - Thêm trường _method="PUT" vào form-data
     * - Thêm file meta_image vào form-data
     *
     * Nguyên nhân: PHP không xử lý được file upload trong PUT requests trực tiếp.
     */
    public function update(UpdateCategoryRequest $request, string $id)
    {
        $data = $this->service->update($id, $request->validated(), ['createdBy', 'updatedBy']);

        return $this->success(new CategoryResource($data), 'Cập nhật category thành công');
    }

    /**
     * Xóa một category.
     */
    public function destroy(string $id)
    {
        $deleted = $this->service->delete($id);

        if (! $deleted) {
            return $this->error(message: "Không tìm thấy {$this->service->getModelName()} với ID là {$id}");
        }

        return $this->successNoContent(message: 'Đã xoá thành công');
    }
}
