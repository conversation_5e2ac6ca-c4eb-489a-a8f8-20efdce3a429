<?php

namespace App\Traits;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Response;

/**
 * Trait ApiResponse
 * @package App\Traits
 *
 * Cung cấp các phương thức chuẩn hóa cho việc trả về API response.
 * Logic được chuyển từ ApiResponseMacro để dễ bảo trì và được IDE hỗ trợ tốt hơn.
 */
trait ApiResponse
{
    /**
     * 200 OK - Thành công chung
     */
    protected function success($data = [], string $message = 'Thành công', int $status = 200): JsonResponse
    {
        return Response::json(
            [
                'success' => true,
                'message' => $message,
                'data' => $data,
            ],
            $status
        );
    }

    /**
     * 204 No Content style - Thành công nhưng không có data, chỉ có success và message
     */
    protected function successNoContent(string $message = 'Thành công', int $status = 200): JsonResponse
    {
        return Response::json(
            [
                'success' => true,
                'message' => $message,
            ],
            $status
        );
    }

    /**
     * 201 Created - Tạo mới thành công
     */
    protected function created($data = [], string $message = 'Tạo thành công'): JsonResponse
    {
        return $this->success($data, $message, 201);
    }

    /**
     * 204 No Content - Không trả về nội dung
     */
    protected function noContent(): JsonResponse
    {
        return Response::json(null, 204);
    }

    /**
     * Lỗi chung
     */
    protected function error(string $message, $status = 400, array $errors = [], $data = []): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if (! empty($errors)) {
            $response['errors'] = $errors;
        }

        if (! empty($data)) {
            $response['data'] = $data;
        }

        // Đảm bảo mã trạng thái luôn là một số nguyên hợp lệ
        $finalStatus = is_numeric($status) && $status >= 100 && $status < 600 ? (int) $status : 500;

        return Response::json($response, $finalStatus);
    }

    /**
     * 401 Unauthorized - Chưa đăng nhập hoặc token sai
     */
    protected function unauthorized(string $message = 'Chưa xác thực'): JsonResponse
    {
        return $this->error($message, 401);
    }

    /**
     * 403 Forbidden - Đã xác thực nhưng không có quyền
     */
    protected function forbidden(string $message = 'Không có quyền truy cập'): JsonResponse
    {
        return $this->error($message, 403);
    }

    /**
     * 404 Not Found - Không tìm thấy tài nguyên
     */
    protected function notFound(string $message = 'Không tìm thấy tài nguyên'): JsonResponse
    {
        return $this->error($message, 404);
    }

    /**
     * 409 Conflict - Dữ liệu xung đột
     */
    protected function conflict(string $message = 'Xung đột dữ liệu'): JsonResponse
    {
        return $this->error($message, 409);
    }

    /**
     * 422 Unprocessable Entity - Validation thất bại
     */
    protected function validationError(array $errors, string $message = 'Dữ liệu không hợp lệ'): JsonResponse
    {
        return $this->error($message, 422, $errors);
    }
}
