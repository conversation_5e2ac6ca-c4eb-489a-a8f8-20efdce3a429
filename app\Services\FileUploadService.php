<?php

namespace App\Services;

use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileUploadService extends BaseService
{
    /**
     * Upload the file to a specified disk and path
     *
     * @param UploadedFile $file
     * @param string $tableName Tên của bảng (ví dụ: gallery_items)
     * @param int|null $recordId ID của bản ghi (ví dụ: 123)
     * @param string $type Loại file (images, documents, videos)
     * @param string $disk
     * @param string|null $oldFileUrl
     * @param array $options
     * @return string
     */
    public function uploadFile(
        UploadedFile $file,
        string       $tableName = 'general',
        string       $type = 'images',
        string       $disk = 'public',
        ?string      $oldFileUrl = null,
        array        $options = []
    ): string {
        // Delete the old file
        if ($oldFileUrl) {
            $this->deleteFile($oldFileUrl, $disk);
        }

        // Tạo đường dẫn theo cấu trúc {type}/{table_name}/{filename}
        $path = $type . '/' . $tableName;

        // Generate filename
        $filename = $this->generateFilename($file, $options);

        // Store file
        $filePath = $file->storeAs($path, $filename, $disk);

        // Return full URL
        return $this->getFileUrl($filePath, $disk);
    }

    /**
     * Upload image with validation
     *
     * @param UploadedFile $file
     * @param string $tableName Tên của bảng (ví dụ: gallery_items)
     * @param int|null $recordId ID của bản ghi (ví dụ: 123)
     * @param string $disk
     * @param string|null $oldFileUrl
     * @param array $options
     * @return array
     */
    public function uploadImage(
        UploadedFile $file,
        string       $tableName = 'general',
        string       $disk = 'public',
        ?string      $oldFileUrl = null,
        array        $options = []
    ): array {
        // Validate image
        $validationErrors = $this->validateImage($file, $options);

        if (! empty($validationErrors)) {
            throw new Exception(json_encode([
                'error' => 'IMAGE_VALIDATION_ERROR',
                'status' => 422,
                'errors' => $validationErrors,
            ]), 422);
        }

        try {
            $fileUrl = $this->uploadFile($file, $tableName, 'images', $disk, $oldFileUrl, $options);

            return $this->success(['url' => $fileUrl], 'Upload ảnh thành công');
        } catch (Exception $e) {
            $this->logError('Error uploading image: ' . $e->getMessage());

            return $this->error('IMAGE_UPLOAD_ERROR', 500);
        }
    }

    /**
     * Upload a document with validation
     *
     * @param UploadedFile $file
     * @param string $tableName Tên của bảng (ví dụ: gallery_items)
     * @param int|null $recordId ID của bản ghi (ví dụ: 123)
     * @param string $disk
     * @param string|null $oldFileUrl
     * @param array $options
     * @return array
     */
    public function uploadDocument(
        UploadedFile $file,
        string       $tableName = 'general',
        string       $disk = 'public',
        ?string      $oldFileUrl = null,
        array        $options = []
    ): array {
        // Validate document
        $validationErrors = $this->validateDocument($file, $options);

        if (! empty($validationErrors)) {
            throw new Exception(json_encode([
                'error' => 'DOCUMENT_VALIDATION_ERROR',
                'status' => 422,
                'errors' => $validationErrors,
            ]), 422);
        }

        try {
            $fileUrl = $this->uploadFile($file, $tableName, 'documents', $disk, $oldFileUrl, $options);

            return $this->success(['url' => $fileUrl], 'Upload tài liệu thành công');
        } catch (Exception $e) {
            $this->logError('Error uploading document: ' . $e->getMessage());

            return $this->error('DOCUMENT_UPLOAD_ERROR', 500);
        }
    }

    /**
     * Upload video with validation
     *
     * @param UploadedFile $file
     * @param string $tableName Tên của bảng (ví dụ: gallery_items)
     * @param int|null $recordId ID của bản ghi (ví dụ: 123)
     * @param string $disk
     * @param string|null $oldFileUrl
     * @param array $options
     * @return array
     */
    public function uploadVideo(
        UploadedFile $file,
        string       $tableName = 'general',
        string       $disk = 'public',
        ?string      $oldFileUrl = null,
        array        $options = []
    ): array {
        // Validate video
        $validationErrors = $this->validateVideo($file, $options);

        if (! empty($validationErrors)) {
            throw new Exception(json_encode([
                'error' => 'VIDEO_VALIDATION_ERROR',
                'status' => 422,
                'errors' => $validationErrors,
            ]), 422);
        }

        try {
            $fileUrl = $this->uploadFile($file, $tableName, 'videos', $disk, $oldFileUrl, $options);

            return $this->success(['url' => $fileUrl], 'Upload video thành công');
        } catch (Exception $e) {
            $this->logError('Error uploading video: ' . $e->getMessage());

            return $this->error('VIDEO_UPLOAD_ERROR', 500);
        }
    }

    /**
     * Delete a file
     *
     * @param string $fileUrl
     * @param string $disk
     * @return bool
     */
    public function deleteFile(string $fileUrl, string $disk = 'public'): bool
    {
        $path = $this->extractPathFromUrl($fileUrl, $disk);

        if ($path && Storage::disk($disk)->exists($path)) {
            return Storage::disk($disk)->delete($path);
        }

        return false;
    }

    /**
     * Generate unique filename
     *
     * @param UploadedFile $file
     * @param array $options
     * @return string
     */
    protected function generateFilename(UploadedFile $file, array $options = []): string
    {
        $extension = $file->getClientOriginalExtension();
        $prefix = $options['prefix'] ?? '';
        $suffix = $options['suffix'] ?? '';

        return $prefix . Str::uuid() . $suffix . '.' . $extension;
    }

    /**
     * Extract file path from URL
     *
     * @param string $url
     * @param string $disk
     * @return string|null
     */
    public function extractPathFromUrl(string $url, string $disk = 'public'): ?string
    {
        $baseUrl = config('app.url') . '/storage/';

        if (Str::startsWith($url, $baseUrl)) {
            return Str::after($url, $baseUrl);
        }

        return null;
    }

    /**
     * Get file URL
     *
     * @param string $path
     * @param string $disk
     * @return string
     */
    protected function getFileUrl(string $path, string $disk = 'public'): string
    {
        return asset('storage/' . $path);
    }

    /**
     * Validate image file
     *
     * @param UploadedFile $file
     * @param array $options
     * @return array
     */
    public function validateImage(UploadedFile $file, array $options = []): array
    {
        $errors = [];

        // Default options
        $maxSize = $options['max_size'] ?? 10 * 1024 * 1024; // 10MB
        $allowedTypes = $options['allowed_types'] ?? ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        $minWidth = $options['min_width'] ?? 100;
        $minHeight = $options['min_height'] ?? 100;
        $maxWidth = $options['max_width'] ?? 2048;
        $maxHeight = $options['max_height'] ?? 2048;

        // Check file size
        if ($file->getSize() > $maxSize) {
            $errors[] = "Kích thước file không được vượt quá " . $this->formatBytes($maxSize);
        }

        // Check file type
        if (! in_array($file->getMimeType(), $allowedTypes)) {
            $errors[] = "Chỉ chấp nhận file ảnh (" . implode(', ', array_map('strtoupper', array_unique(array_map(function ($type) {
                return pathinfo($type, PATHINFO_EXTENSION) ?: 'JPEG';
            }, $allowedTypes)))) . ")";
        }

        // Check image dimensions
        $imageInfo = getimagesize($file->getPathname());
        if ($imageInfo) {
            $width = $imageInfo[0];
            $height = $imageInfo[1];

            if ($width < $minWidth || $height < $minHeight) {
                $errors[] = "Kích thước ảnh tối thiểu là {$minWidth}x{$minHeight} pixels";
            }

            if ($width > $maxWidth || $height > $maxHeight) {
                $errors[] = "Kích thước ảnh tối đa là {$maxWidth}x{$maxHeight} pixels";
            }
        }

        return $errors;
    }

    /**
     * Validate document file
     *
     * @param UploadedFile $file
     * @param array $options
     * @return array
     */
    public function validateDocument(UploadedFile $file, array $options = []): array
    {
        $errors = [];

        // Default options
        $maxSize = $options['max_size'] ?? 10 * 1024 * 1024; // 10MB
        $allowedTypes = $options['allowed_types'] ?? [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain',
        ];

        // Check file size
        if ($file->getSize() > $maxSize) {
            $errors[] = "Kích thước file không được vượt quá " . $this->formatBytes($maxSize);
        }

        // Check a file type
        if (! in_array($file->getMimeType(), $allowedTypes)) {
            $errors[] = "Chỉ chấp nhận file tài liệu (PDF, DOC, DOCX, XLS, XLSX, TXT)";
        }

        return $errors;
    }

    /**
     * Validate video file
     *
     * @param UploadedFile $file
     * @param array $options
     * @return array
     */
    public function validateVideo(UploadedFile $file, array $options = []): array
    {
        $errors = [];

        // Default options
        $maxSize = $options['max_size'] ?? 100 * 1024 * 1024; // 100MB
        $allowedTypes = $options['allowed_types'] ?? [
            'video/mp4',
            'video/avi',
            'video/mov',
            'video/wmv',
            'video/flv',
            'video/webm',
        ];

        // Check file size
        if ($file->getSize() > $maxSize) {
            $errors[] = "Kích thước file không được vượt quá " . $this->formatBytes($maxSize);
        }

        // Check a file type
        if (! in_array($file->getMimeType(), $allowedTypes)) {
            $errors[] = "Chỉ chấp nhận file video (MP4, AVI, MOV, WMV, FLV, WEBM)";
        }

        return $errors;
    }

    /**
     * Format bytes to human-readable format
     *
     * @param int $bytes
     * @return string
     */
    protected function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get file info
     *
     * @param string $fileUrl
     * @param string $disk
     * @return array|null
     */
    public function getFileInfo(string $fileUrl, string $disk = 'public'): ?array
    {
        $path = $this->extractPathFromUrl($fileUrl, $disk);

        if ($path && Storage::disk($disk)->exists($path)) {
            return [
                'path' => $path,
                'size' => Storage::disk($disk)->size($path),
                'last_modified' => Storage::disk($disk)->lastModified($path),
            ];
        }

        return null;
    }

    /**
     * Check if file exists
     *
     * @param string $fileUrl
     * @param string $disk
     * @return bool
     */
    public function fileExists(string $fileUrl, string $disk = 'public'): bool
    {
        $path = $this->extractPathFromUrl($fileUrl, $disk);

        return $path && Storage::disk($disk)->exists($path);
    }

    /**
     * Get error messages
     *
     * @param string $errorCode
     * @param array $params
     * @return string
     */
    protected function getErrorMessage(string $errorCode, array $params = []): string
    {
        $messages = [
            'IMAGE_VALIDATION_ERROR' => 'Lỗi validation ảnh',
            'IMAGE_UPLOAD_ERROR' => 'Lỗi khi upload ảnh',
            'DOCUMENT_VALIDATION_ERROR' => 'Lỗi validation tài liệu',
            'DOCUMENT_UPLOAD_ERROR' => 'Lỗi khi upload tài liệu',
            'VIDEO_VALIDATION_ERROR' => 'Lỗi validation video',
            'VIDEO_UPLOAD_ERROR' => 'Lỗi khi upload video',
        ];

        $message = $messages[$errorCode] ?? 'Có lỗi xảy ra';

        return $this->interpolateMessage($message, $params);
    }
}
