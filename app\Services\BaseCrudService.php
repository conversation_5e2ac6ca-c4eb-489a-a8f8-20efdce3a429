<?php

namespace App\Services;

use App\Repositories\Interfaces\BaseRepositoryInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Class BaseCrudService
 * @package App\Services
 *
 * Service cơ sở cho các hành động CRUD, tách biệt logic khỏi controller.
 * Service này sử dụng Repository Pattern để tương tác với cơ sở dữ liệu.
 *
 * @property-read BaseRepositoryInterface $repository
 * @mixin BaseRepositoryInterface
 */
abstract class BaseCrudService extends BaseService
{
    /**
     * @var BaseRepositoryInterface
     */
    protected BaseRepositoryInterface $repository;

    /**
     * Khởi tạo service và inject repository tương ứng.
     */
    public function __construct()
    {
        $repositoryClass = $this->getRepositoryClass();
        $this->repository = app($repositoryClass);
    }

    /**
     * Trả về tên lớp Repository.
     *
     * @return class-string<BaseRepositoryInterface>
     */
    abstract protected function getRepositoryClass(): string;

    /**
     * Lấy tên của Model đang được sử dụng.
     *
     * @return string
     */
    public function getModelName(): string
    {
        return $this->repository->getModelName();
    }

    /**
     * Lấy danh sách tài nguyên, hỗ trợ phân trang.
     *
     * @param Request $request
     * @return LengthAwarePaginator
     */
    /**
     * Lấy danh sách tài nguyên, hỗ trợ phân trang và tìm kiếm.
     *
     * Phương thức này xử lý hai loại tham số tìm kiếm:
     * 1. Tìm kiếm chung: sử dụng tham số 'search' hoặc 'q' để tìm kiếm trên nhiều trường
     * 2. Tìm kiếm cụ thể: sử dụng các tham số khác để lọc theo trường cụ thể
     *    - Hỗ trợ tìm kiếm đơn giản: status=active
     *    - Hỗ trợ tìm kiếm với mảng giá trị: status[]=active&status[]=inactive
     *
     * @param Request $request Request chứa các tham số tìm kiếm và phân trang
     * @param array $with Các quan hệ cần eager load
     * @return LengthAwarePaginator
     */
    public function list(Request $request, array $with = []): LengthAwarePaginator
    {
        // Khởi tạo các mảng tham số
        $generalParams = [];
        $specificSearchParams = [];

        // Lấy tất cả các tham số từ request
        $allParams = $request->all();

        // Lấy tham số đặc biệt (page, limit, sort_by, sort_order)
        $specialParams = ['page', 'limit', 'sort_by', 'sort_order'];

        // Lấy tham số tìm kiếm chung ('search' hoặc 'q')
        // Tham số này sẽ được sử dụng để tìm kiếm trên nhiều trường (searchableFields)
        if ($request->has('search')) {
            $generalParams['search_query'] = $request->input('search');
        } elseif ($request->has('q')) {
            $generalParams['search_query'] = $request->input('q');
        }

        // Phân loại các tham số:
        // - Các tham số đặc biệt (page, limit, sort_by, sort_order) -> generalParams
        // - Các tham số khác (trừ search và q) -> specificSearchParams
        foreach ($allParams as $key => $value) {
            if (in_array($key, $specialParams)) {
                // Các tham số phân trang và sắp xếp
                $generalParams[$key] = $value;
            } elseif ($key !== 'search' && $key !== 'q') {
                // Các tham số còn lại là tham số tìm kiếm cụ thể
                // Hỗ trợ cả giá trị đơn (status=active) và mảng (status[]=active&status[]=inactive)
                $specificSearchParams[$key] = $value;
            }
        }

        // Gọi repository để thực hiện tìm kiếm và phân trang
        return $this->repository->paginateBySearchParams($specificSearchParams, $generalParams, $with);
    }

    /**
     * Tạo một tài nguyên mới.
     *
     * @param array $data
     * @return Model
     */
    public function create(array $data, array $with = []): Model
    {
        $model = $this->repository->create($data);
        $model->load($with);

        return $model;
    }

    /**
     * Lấy thông tin chi tiết của một tài nguyên.
     *
     * @param int $id
     * @return Model
     */
    public function read(int $id, array $with = []): Model
    {
        return $this->repository->findOrFail($id, $with);
    }

    /**
     * Cập nhật một tài nguyên.
     *
     * @param int $id
     * @param array $data
     * @return Model
     */
    public function update(int $id, array $data, array $with = []): Model
    {
        $model = $this->repository->update($id, $data);
        $model->load($with);

        return $model;
    }

    /**
     * Xóa một tài nguyên.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        try {
            return $this->repository->delete($id);
        } catch (ModelNotFoundException $e) {
            return false;
        }
    }

    /**
     * Cập nhật một thuộc tính duy nhất của tài nguyên.
     *
     * @param int $id ID của tài nguyên
     * @param string $attribute Tên thuộc tính cần cập nhật
     * @param mixed $value Giá trị mới của thuộc tính
     * @param array $with Các quan hệ cần eager load
     * @return Model
     */
    public function updateSingleAttribute(int $id, string $attribute, mixed $value, array $with = []): Model
    {
        $data = [$attribute => $value];

        $model = $this->repository->update($id, $data);
        $model->load($with);

        return $model;
    }

    /**
     * Cập nhật một thuộc tính duy nhất của tài nguyên với validate theo FormRequest.
     *
     * @param int $id ID của tài nguyên
     * @param string $attribute Tên thuộc tính cần cập nhật
     * @param mixed $value Giá trị mới của thuộc tính
     * @param string $requestClass Tên lớp FormRequest để validate
     * @param array $with Các quan hệ cần eager load
     * @return Model
     * @throws \Illuminate\Validation\ValidationException nếu dữ liệu không hợp lệ
     */
    public function updateSingleAttributeWithValidation(
        int $id,
        string $attribute,
        mixed $value,
        string $requestClass,
        array $with = []
    ): Model {
        // Tạo một mảng dữ liệu chỉ với thuộc tính cần cập nhật
        $data = [$attribute => $value];

        // Tạo một instance của FormRequest để validate
        $request = app($requestClass);

        // Lấy rules từ FormRequest
        $rules = $request->rules();

        // Chỉ validate cho thuộc tính cần cập nhật
        if (isset($rules[$attribute])) {
            $validator = validator($data, [
                $attribute => $rules[$attribute],
            ], $request->messages());

            // Nếu validate thất bại, sẽ ném ra ValidationException
            $validator->validate();
        }

        // Cập nhật thuộc tính sau khi đã validate thành công
        return $this->updateSingleAttribute($id, $attribute, $value, $with);
    }

    /**
     * Cập nhật nhiều thuộc tính của tài nguyên với validate theo FormRequest.
     *
     * @param int $id ID của tài nguyên
     * @param array $attributes Mảng các thuộc tính cần cập nhật
     * @param string $requestClass Tên lớp FormRequest để validate
     * @param array $with Các quan hệ cần eager load
     * @return Model
     * @throws \Illuminate\Validation\ValidationException nếu dữ liệu không hợp lệ
     */
    public function updateAttributesWithValidation(
        int $id,
        array $attributes,
        string $requestClass,
        array $with = []
    ): Model {
        // Tạo một instance của FormRequest để validate
        $request = app($requestClass);

        // Lấy rules từ FormRequest
        $rules = $request->rules();

        // Lọc các rules chỉ để áp dụng cho các thuộc tính được gửi lên
        $applicableRules = [];
        foreach ($attributes as $key => $value) {
            if (isset($rules[$key])) {
                $applicableRules[$key] = $rules[$key];
            }
        }

        // Validate các thuộc tính được gửi lên
        if (! empty($applicableRules)) {
            $validator = validator($attributes, $applicableRules, $request->messages());
            $validator->validate();
        }

        // Cập nhật các thuộc tính sau khi đã validate thành công
        $model = $this->repository->update($id, $attributes);
        $model->load($with);

        return $model;
    }
}
