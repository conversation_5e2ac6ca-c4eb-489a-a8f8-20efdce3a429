{
    // Tự động định dạng khi lưu file
    "editor.formatOnSave": true,

    // Chỉ định Prettier là trình định dạng mặc định cho tất cả các file
    // Điều này giúp đảm bả<PERSON> t<PERSON>h nhất quán cho cả PHP, JS, CSS, JSON, v.v.
    "editor.defaultFormatter": "esbenp.prettier-vscode",

    // Cấu hình để sử dụng PHP CS Fixer làm trình định dạng cho file PHP
    "[php]": {
        "editor.defaultFormatter": "junstyle.php-cs-fixer",
        "editor.formatOnSave": true
    },

    // Cấu hình cho extension PHP CS Fixer
    // Trỏ đến file thực thi và file cấu hình trong dự án
    "php-cs-fixer.executablePath": "${workspaceFolder}/vendor/bin/php-cs-fixer",
    "php-cs-fixer.config": ".php-cs-fixer.dist.php"
}
