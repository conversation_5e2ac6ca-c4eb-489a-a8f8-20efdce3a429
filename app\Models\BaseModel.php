<?php

namespace App\Models;

use App\Models\Concerns\HasBaseModelLogic;
use App\Models\Concerns\HasUserStamps;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;

/**
 * Base Model cho tất cả Eloquent Models trong hệ thống
 *
 * Class này cung cấp các cấu hình và behavior chung:
 * - Soft delete mặc định cho mọi model
 * - Format datetime chuẩn cho toàn hệ thống
 * - C<PERSON> chế merge casts linh hoạt
 * - Mass assignment protection
 * - Tự động điền created_by và updated_by
 *
 * Tất cả models nên extend từ class này thay vì Eloquent Model
 */
abstract class BaseModel extends Model
{
    use HasBaseModelLogic;
    use HasUserStamps;

    /**
     * Bảo vệ chống lại Mass Assignment.
     * Cho phép gán hàng loạt tất cả các thuộc tính.
     * Việc validation phải được xử lý ở tầng FormRequest.
     */
    protected $guarded = [];

    /**
     * Chuẩn bị định dạng ngày tháng khi serialize sang array/JSON.
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }
}
