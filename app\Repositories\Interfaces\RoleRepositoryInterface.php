<?php

namespace App\Repositories\Interfaces;

use Illuminate\Database\Eloquent\Collection;
use Spatie\Permission\Models\Role;

interface RoleRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Lấy tất cả roles
     *
     * @param array $params
     * @return Collection
     */
    public function getAllRoles(array $params = []): Collection;

    /**
     * Lấy tất cả roles với permissions
     *
     * @return Collection
     */
    public function getAllWithPermissions(): Collection;

    /**
     * Lấy role với permissions theo ID
     *
     * @param int $id
     * @return Role|null
     */
    public function findWithPermissions(int $id): ?Role;

    /**
     * Lấy role với permissions và users theo ID
     *
     * @param int $id
     * @return Role|null
     */
    public function findWithPermissionsAndUsers(int $id): ?Role;

    /**
     * <PERSON>án permissions cho role
     *
     * @param int $roleId
     * @param array $permissionIds
     * @return bool
     */
    public function assignPermissions(int $roleId, array $permissionIds): bool;

    /**
     * Thu hồi permissions từ role
     *
     * @param int $roleId
     * @param array $permissionIds
     * @return bool
     */
    public function revokePermissions(int $roleId, array $permissionIds): bool;

    /**
     * Lấy permission IDs của role
     *
     * @param int $roleId
     * @return array
     */
    public function getPermissionIds(int $roleId): array;

    /**
     * Xóa role và detach tất cả relationships
     *
     * @param int $id
     * @return bool
     */
    public function deleteWithDetach(int $id): bool;
}
