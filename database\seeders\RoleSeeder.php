<?php

namespace Database\Seeders;

use App\Repositories\Eloquent\RoleRepository;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roleRepository = app(RoleRepository::class);

        $roles = [
            [
                'name' => 'Super Administrator',
                'guard_name' => 'api',
                'description' => 'Vai trò quản trị viên cao cấp với đầy đủ quyền hạn trong hệ thống',
                'is_protected' => true,
            ],
        ];

        foreach ($roles as $role) {
            $roleRepository->firstOrCreate(
                [
                    'name' => $role['name'],
                    'guard_name' => $role['guard_name'],
                ],
                $role
            );
        }

        $this->command->info('Roles đã được tạo thành công!');
    }
}
