<?php

namespace App\Http\Requests\Auth;

use App\Http\Requests\BaseRequest;

class LoginRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'string', 'email', 'max:255'],
            'password' => ['required', 'string', 'min:6'],
            'remember_me' => ['nullable', 'boolean'],
        ];
    }

    /**
     * Defines the parameters for the request body.
     *
     * @return array
     */
    public function bodyParameters(): array
    {
        return [
            'email' => [
                'description' => 'Email của người dùng.',
                'example' => '<EMAIL>',
            ],
            'password' => [
                'description' => 'Mật khẩu của người dùng.',
                'example' => 'password',
            ],
            'remember_me' => [
                'description' => '<PERSON>hi nhớ người dùng.',
                'example' => true,
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'email.required' => 'Email là trường bắt buộc',
            'email.email' => 'Email không đúng định dạng',
            'email.max' => 'Email không được vượt quá 255 ký tự',
            'password.required' => 'Mật khẩu là trường bắt buộc',
            'password.min' => 'Mật khẩu phải có ít nhất 6 ký tự',
            'remember_me.boolean' => 'Remember me phải là giá trị boolean',
        ];
    }
}
