<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\User\ChangeUserPasswordRequest;
use App\Http\Requests\User\DetailUserRequest;
use App\Http\Requests\User\StoreUserRequest;
use App\Http\Requests\User\UpdateUserRequest;
use App\Http\Requests\User\UpdateUserStatusRequest;
use App\Resources\User\UserResource;
use App\Resources\User\UserResourceCollection;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Người dùng - Quản lý người dùng
 */
class UserController extends BaseController
{
    protected UserService $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    /**
     * Get a list of users with pagination and filters
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $filters = $request->only(['search', 'status', 'role_id', 'sort_by', 'sort_direction']);
        $perPage = $request->get('per_page', 20);

        $result = $this->userService->getUsers($filters, $perPage);

        if (! $result['success']) {
            return response()->json($result, $result['code'] ?? 400);
        }

        $users = new UserResourceCollection($result['data']['users']);

        return response()->json([
            'success' => true,
            'message' => $result['message'],
            'data' => [
                'users' => $users,
                'pagination' => $result['data']['pagination'],
            ],
        ]);
    }

    /**
     * Get user by ID
     *
     * @param DetailUserRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function show(DetailUserRequest $request, int $id): JsonResponse
    {
        $result = $this->userService->getUser($id);

        if (! $result['success']) {
            return response()->json($result, $result['code'] ?? 400);
        }

        $userResource = new UserResource($result['data']['user']);

        return response()->json([
            'success' => true,
            'message' => $result['message'],
            'data' => [
                'user' => $userResource,
            ],
        ]);
    }

    /**
     * Create a new user
     *
     * @param StoreUserRequest $request
     * @return JsonResponse
     */
    public function store(StoreUserRequest $request): JsonResponse
    {
        $data = $request->validated();
        $avatarFile = $request->file('avatar');

        $result = $this->userService->createUser($data, $avatarFile);

        if (! $result['success']) {
            return response()->json($result, $result['code'] ?? 400);
        }

        return response()->json([
            'success' => true,
            'message' => $result['message'],
            'data' => [
                'user' => new UserResource($result['data']['user']),
            ],
        ], 201);
    }

    /**
     * Update user
     *
     * @param UpdateUserRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateUserRequest $request, int $id): JsonResponse
    {
        $data = $request->validated();
        $avatarFile = $request->file('avatar');

        $result = $this->userService->updateUser($id, $data, $avatarFile);

        if (! $result['success']) {
            return response()->json($result, $result['code'] ?? 400);
        }

        return response()->json([
            'success' => true,
            'message' => $result['message'],
            'data' => [
                'user' => new UserResource($result['data']['user']),
            ],
        ]);
    }

    /**
     * Delete user
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $result = $this->userService->deleteUser($id);

        if (! $result['success']) {
            return response()->json($result, $result['code'] ?? 400);
        }

        return response()->json([
            'success' => true,
            'message' => $result['message'],
            'data' => [],
        ]);
    }

    /**
     * Update user status
     *
     * @param UpdateUserStatusRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateStatus(UpdateUserStatusRequest $request, int $id): JsonResponse
    {
        $result = $this->userService->updateUserStatus($id, $request->status);

        if (! $result['success']) {
            return response()->json($result, $result['code'] ?? 400);
        }

        return response()->json([
            'success' => true,
            'message' => $result['message'],
            'data' => [
                'user' => new UserResource($result['data']['user']),
            ],
        ]);
    }

    /**
     * Change user password
     *
     * @param ChangeUserPasswordRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function changePassword(ChangeUserPasswordRequest $request, int $id): JsonResponse
    {
        $result = $this->userService->changeUserPassword($id, $request->password);

        if (! $result['success']) {
            return response()->json($result, $result['code'] ?? 400);
        }

        return response()->json([
            'success' => true,
            'message' => $result['message'],
            'data' => [
                'user' => new UserResource($result['data']['user']),
            ],
        ]);
    }
}
