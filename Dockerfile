# Sử dụng image PHP 8.2-fpm (Debian-based) theo yêu cầu
FROM php:8.2-fpm

# Thiết lập thư mục làm việc
WORKDIR /var/www/html

# Cài đặt các dependencies cần thiết cho hệ thống
RUN apt-get update && apt-get install -y \
    build-essential \
    libpng-dev \
    libjpeg62-turbo-dev \
    libfreetype6-dev \
    locales \
    zip \
    jpegoptim optipng pngquant gifsicle \
    vim \
    unzip \
    git \
    curl \
    libonig-dev \
    libxml2-dev \
    libzip-dev

# Dọn dẹp cache của apt
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Cài đặt các extension cần thiết cho PHP
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd zip

# Cài đặt Redis extension
RUN pecl install redis && docker-php-ext-enable redis

# Cài đặt Composer (trình quản lý package cho PHP)
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Sao chép mã nguồn ứng dụng vào container
COPY . /var/www/html

# Sao chép các file cấu hình Composer và chạy cài đặt dependencies
COPY composer.json composer.lock ./

# Cài đặt Composer dependencies
RUN composer install --no-dev --optimize-autoloader --no-interaction

# Thêm thư mục làm việc vào danh sách an toàn của Git để tránh lỗi dubious ownership
RUN git config --global --add safe.directory /var/www/html

# Cấp quyền cho thư mục vendor sau khi cài đặt Composer
RUN chown -R www-data:www-data /var/www/html/vendor

# Cấp quyền cho thư mục storage và bootstrap/cache
RUN chown -R www-data:www-data /var/www/html/storage /var/www/html/bootstrap/cache
RUN chmod -R 775 /var/www/html/storage /var/www/html/bootstrap/cache

# Tạo thư mục .config/psysh cho tinker và cấp quyền
RUN mkdir -p /var/www/.config/psysh
RUN chown -R www-data:www-data /var/www/.config
RUN chmod -R 775 /var/www/.config

# Expose port 9000 để Nginx có thể giao tiếp với PHP-FPM
EXPOSE 9000

# Chuyển sang người dùng www-data để giảm thiểu rủi ro bảo mật và giải quyết vấn đề quyền sở hữu tệp.
USER www-data

# Lệnh để khởi chạy PHP-FPM
CMD ["php-fpm"]
