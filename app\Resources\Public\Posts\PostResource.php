<?php

namespace App\Resources\Public\Posts;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource để hiển thị thông tin chi tiết của một bài viết cho người dùng cuối.
 * Chỉ bao gồm các trường cần thiết và an toàn để công khai.
 */
class PostResource extends JsonResource
{
    /**
     * Dữ liệu đặc biệt cho resource (relationships, computed properties)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request = null): array
    {
        return [
            // Các trường cơ bản của bài viết
            'title' => $this->resource->title,
            'slug' => $this->resource->slug,
            'excerpt' => $this->resource->excerpt,
            'body' => $this->resource->body,
            'cover_image' => $this->resource->cover_image,
            'published_at' => $this->resource->published_at,
            'is_hot' => $this->resource->is_hot,
            'show_on_homepage' => $this->resource->show_on_homepage,
            'meta_title' => $this->resource->meta_title,
            'meta_description' => $this->resource->meta_description,
            'meta_keywords' => $this->resource->meta_keywords,

            // Lấy thông tin danh mục (nếu có)
            'category' => $this->whenLoaded('category', fn () => [
                'name' => $this->category->name,
                'slug' => $this->category->slug,
            ]),

            // Lấy thông tin tác giả (chỉ lấy tên)
            'author' => $this->whenLoaded('createdBy', fn () => $this->createdBy->name),
        ];
    }
}
