<?php

namespace App\Repositories\Eloquent;

use App\Models\CardPrint;
use App\Repositories\Interfaces\CardPrintRepositoryInterface;

class CardPrintRepository extends BaseRepository implements CardPrintRepositoryInterface
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return CardPrint::class;
    }

    /**
     * @param array $params
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getCardPrints(array $params = []): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $query = $this->model->newQuery()->with('createdBy');

        if (isset($params['search'])) {
            $query->where(function ($q) use ($params) {
                $q->where('serial', 'like', '%' . $params['search'] . '%')
                    ->orWhere('pin', 'like', '%' . $params['search'] . '%');
            });
        }

        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        if (isset($params['start_date'])) {
            $query->whereDate('created_at', '>=', $params['start_date']);
        }

        if (isset($params['end_date'])) {
            $query->whereDate('created_at', '<=', $params['end_date']);
        }

        if (isset($params['sort_by'], $params['sort_order'])) {
            $query->orderBy($params['sort_by'], $params['sort_order']);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        return $query->paginate($params['limit'] ?? 15);
    }
}
