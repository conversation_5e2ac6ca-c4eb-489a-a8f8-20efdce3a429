<?php

namespace App\Repositories\Eloquent;

use App\Models\Menu;
use App\Repositories\Interfaces\MenuRepositoryInterface;

class MenuRepository extends BaseRepository implements MenuRepositoryInterface
{
    /**
     * <PERSON>h sách các trường có thể tìm kiếm toàn văn.
     *
     * @var array
     */
    protected array $searchableFields = [
        'name',
        'location_key',
    ];

    /**
     * Chỉ định model sẽ được sử dụng
     *
     * @return string
     */
    protected function getModelClass(): string
    {
        return Menu::class;
    }
}
