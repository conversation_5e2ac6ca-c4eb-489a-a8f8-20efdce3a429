<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\GalleryGroups\StoreGalleryGroupRequest;
use App\Http\Requests\GalleryGroups\UpdateGalleryGroupRequest;
use App\Resources\GalleryGroups\GalleryGroupResource;
use App\Resources\GalleryGroups\GalleryGroupResourceCollection;
use App\Services\GalleryGroupService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Thư viện ảnh - Nhóm
 */
class GalleryGroupController extends BaseController
{
    public function __construct(protected GalleryGroupService $service)
    {
    }

    /**
     * Hiển thị danh sách các nhóm thư viện ảnh.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $galleryGroups = $this->service->list($request, ['createdBy', 'updatedBy']);

        return $this->success(new GalleryGroupResourceCollection($galleryGroups), '<PERSON><PERSON><PERSON> danh sách nhóm thư viện ảnh thành công.');
    }

    /**
     * L<PERSON><PERSON> một nhóm thư viện ảnh mới.
     *
     * @param StoreGalleryGroupRequest $request
     * @return JsonResponse
     */
    public function store(StoreGalleryGroupRequest $request): JsonResponse
    {
        $galleryGroup = $this->service->create($request->validated(), ['createdBy', 'updatedBy']);

        return $this->success(new GalleryGroupResource($galleryGroup), 'Tạo nhóm thư viện ảnh thành công.', 201);
    }

    /**
     * Hiển thị chi tiết một nhóm thư viện ảnh.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $galleryGroup = $this->service->read($id, ['createdBy', 'updatedBy']);

        return $this->success(new GalleryGroupResource($galleryGroup), 'Lấy thông tin nhóm thư viện ảnh thành công.');
    }

    /**
     * Cập nhật một nhóm thư viện ảnh.
     *
     * @param UpdateGalleryGroupRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateGalleryGroupRequest $request, int $id): JsonResponse
    {
        $galleryGroup = $this->service->update($id, $request->validated(), ['createdBy', 'updatedBy']);

        return $this->success(new GalleryGroupResource($galleryGroup), 'Cập nhật nhóm thư viện ảnh thành công.');
    }

    /**
     * Xóa một nhóm thư viện ảnh.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $deleted = $this->service->delete($id);

        return $deleted
            ? $this->successNoContent('Xóa nhóm thư viện ảnh thành công.')
            : $this->error('Không thể xóa nhóm thư viện ảnh.', 500);
    }
}
