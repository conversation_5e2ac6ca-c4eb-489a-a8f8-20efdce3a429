<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Category extends BaseModel
{
    use HasFactory;
    use SoftDeletes;

    /**
     * <PERSON><PERSON><PERSON> thuộc tính nên được <PERSON>n khi chuyển thành mảng hoặc JSON.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'created_by',
        'updated_by',
    ];
    /**
     * Tên bảng liên kết với model.
     *
     * @var string
     */
    protected $table = 'categories';

    /**
     * Khóa chính của bảng.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Các thuộc tính có thể gán hàng loạt.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'parent_id',
        'description',
        'status',
        'is_featured',
        'featured_order',
        'order',
        'meta_title',
        'meta_description',
        'meta_image',
        'created_by',
        'updated_by',
    ];

    /**
     * <PERSON><PERSON><PERSON> các casts dành riêng cho model này.
     *
     * @return array
     */
    protected function getModelCasts(): array
    {
        return [
            'is_featured' => 'boolean',
            'featured_order' => 'integer',
            'order' => 'integer',
        ];
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Quan hệ với các bài viết thuộc danh mục này.
     */
    public function posts()
    {
        return $this->hasMany(Post::class);
    }
}
