<?php

namespace App\Repositories\Eloquent;

use App\Models\GalleryItem;
use App\Repositories\Interfaces\GalleryItemRepositoryInterface;

class GalleryItemRepository extends BaseRepository implements GalleryItemRepositoryInterface
{
    /**
     * <PERSON>h sách các trường có thể tìm kiếm toàn văn.
     *
     * @var array
     */
    protected array $searchableFields = [
        'alt_text',
        'title',
        'link',
        'image_url',
    ];

    protected function getModelClass(): string
    {
        return GalleryItem::class;
    }
}
