<?php

namespace App\Enums;

enum UserStatus: int
{
    case INACTIVE = 0;
    case ACTIVE = 1;
    case SUSPENDED = 2;

    /**
     * Get the label for the status
     */
    public function label(): string
    {
        return match ($this) {
            self::ACTIVE => 'ACTIVE',
            self::INACTIVE => 'INACTIVE',
            self::SUSPENDED => 'SUSPENDED',
        };
    }

    /**
     * Get all statuses as array
     */
    public static function toArray(): array
    {
        return [
            self::ACTIVE->value => self::ACTIVE->label(),
            self::INACTIVE->value => self::INACTIVE->label(),
            self::SUSPENDED->value => self::SUSPENDED->label(),
        ];
    }

    /**
     * Check if status is active
     */
    public function isActive(): bool
    {
        return $this === self::ACTIVE;
    }

    /**
     * Check if status is inactive
     */
    public function isInactive(): bool
    {
        return $this === self::INACTIVE;
    }

    /**
     * Check if status is suspended
     */
    public function isSuspended(): bool
    {
        return $this === self::SUSPENDED;
    }

    /**
     * Create from string value
     */
    public static function fromString(string $status): self
    {
        return match (strtoupper($status)) {
            'ACTIVE' => self::ACTIVE,
            'INACTIVE' => self::INACTIVE,
            'SUSPENDED' => self::SUSPENDED,
            default => self::INACTIVE,
        };
    }
}
