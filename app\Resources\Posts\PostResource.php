<?php

namespace App\Resources\Posts;

use App\Resources\BaseResource;

class PostResource extends BaseResource
{
    /**
     * Dữ liệu đặc biệt cho resource (relationships, computed properties)
     */
    protected function resourceData($request): array
    {
        return [
            // Computed properties
            'is_published' => $this->resource->status === 'published',
            'reading_time' => $this->calculateReadingTime(),
        ];
    }

    /**
     * Tính toán thời gian đọc ước t<PERSON> (phút)
     */
    private function calculateReadingTime(): int
    {
        $wordCount = str_word_count(strip_tags($this->resource->body ?? ''));
        $wordsPerMinute = 200; // Trung bình 200 từ/phút

        return max(1, ceil($wordCount / $wordsPerMinute));
    }
}
