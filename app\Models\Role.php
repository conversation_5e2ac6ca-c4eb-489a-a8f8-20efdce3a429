<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Spatie\Permission\Models\Role as SpatieRole;

class Role extends SpatieRole
{
    protected $fillable = [
        'name',
        'guard_name',
        'description',
        'is_protected',
    ];

    protected $casts = [
        'is_protected' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $attributes = [
        'guard_name' => 'api',
        'is_protected' => false,
    ];

    /**
     * Scope để lấy các vai trò được bảo vệ
     */
    public function scopeProtected(Builder $query): Builder
    {
        return $query->where('is_protected', true);
    }

    /**
     * Scope để lấy các vai trò không được bảo vệ
     */
    public function scopeUnprotected(Builder $query): Builder
    {
        return $query->where('is_protected', false);
    }

    /**
     * Accessor cho is_protected
     */
    public function getIsProtectedAttribute($value): bool
    {
        return (bool) $value;
    }

    /**
     * Kiểm tra vai trò có được bảo vệ không
     */
    public function isProtected(): bool
    {
        return $this->is_protected;
    }

    /**
     * Kiểm tra vai trò có thể được chỉnh sửa không
     */
    public function canBeModified(): bool
    {
        return ! $this->is_protected;
    }

    /**
     * Kiểm tra vai trò có thể được xóa không
     */
    public function canBeDeleted(): bool
    {
        return ! $this->is_protected;
    }

    /**
     * Boot method để thêm các event listeners
     */
    protected static function boot()
    {
        parent::boot();

        // Ngăn xóa vai trò được bảo vệ
        static::deleting(function ($role) {
            if ($role->is_protected) {
                throw new \Exception('Không thể xóa vai trò được bảo vệ');
            }
        });
    }
}
