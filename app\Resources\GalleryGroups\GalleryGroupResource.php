<?php

namespace App\Resources\GalleryGroups;

use App\Resources\BaseResource;
use App\Resources\GalleryItems\GalleryItemResource;

class GalleryGroupResource extends BaseResource
{
    /**
     * D<PERSON> liệu đặc biệt cho resource (relationships, computed properties)
     *
     * ⚠️ LƯU Ý: Không override toArray() method!
     * BaseResource tự động include tất cả attributes của model.
     */
    protected function resourceData($request): array
    {
        return [
            'galleryItems' => GalleryItemResource::collection($this->whenLoaded('galleryItems')),

            // Computed properties
            'location_display_text' => $this->getLocationDisplayText(),
            'gallery_items_count' => $this->whenLoaded('galleryItems', fn () => $this->galleryItems->count()),
            'active_gallery_items_count' => $this->whenLoaded('galleryItems', fn () => $this->galleryItems->where('status', 1)->count()),
        ];
    }

    /**
     * Lấy text hiển thị cho location_display
     */
    private function getLocationDisplayText(): string
    {
        return match ($this->resource->location_display) {
            1 => 'Homepage',
            2 => 'Landing',
            3 => 'Both',
            default => 'Unknown',
        };
    }
}
