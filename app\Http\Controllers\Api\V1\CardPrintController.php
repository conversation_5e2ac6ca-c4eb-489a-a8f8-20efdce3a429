<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\CardPrint\CancelCardPrintRequest;
use App\Http\Requests\CardPrint\StoreCardPrintRequest;
use App\Services\CardPrintService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Giao dịch
 */
class CardPrintController extends BaseController
{
    public function __construct(protected CardPrintService $cardPrintService)
    {
    }

    public function index(Request $request): JsonResponse
    {
        $result = $this->cardPrintService->getCardPrints($request);

        return $result['success']
            ? $this->success($result['data'], $result['message'])
            : $this->error($result['message'], $result['code']);
    }

    public function store(StoreCardPrintRequest $request): JsonResponse
    {
        $result = $this->cardPrintService->createCardPrint($request->validated());

        return $result['success']
            ? $this->created($result['data'], $result['message'])
            : $this->error($result['message'], $result['code']);
    }

    public function cancelCard(CancelCardPrintRequest $request, $id): JsonResponse
    {
        $result = $this->cardPrintService->cancelCard($id);

        return $result['success']
            ? $this->success($result['data'], $result['message'])
            : $this->error($result['message'], $result['code']);
    }
}
