<?php

namespace App\Http\Controllers\Api\V1\Public;

use App\Http\Controllers\Api\V1\BaseController;
use App\Resources\Public\StaticPages\StaticPageResource;
use App\Services\StaticPageService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;

/**
 * @group Public - Static Pages
 *
 * API công khai để lấy dữ liệu trang tĩnh
 */
class StaticPageController extends BaseController
{
    /**
     * Khởi tạo controller với service
     *
     * @param StaticPageService $service
     */
    public function __construct(protected StaticPageService $service)
    {
    }

    /**
     * Lấy chi tiết trang tĩnh công khai
     *
     * Hiển thị thông tin chi tiết của một trang tĩnh dựa vào slug.
     * Chỉ trả về các trang có status = 1 (active).
     *
     * @urlParam slug string required Slug của trang tĩnh. Example: ve-chung-toi
     *
     * @param string $slug
     * @header X-Public-Api-Secret string required Khóa bí mật API công khai. Example: your_super_secret_key_here
     * @return JsonResponse
     */
    public function show(string $slug): JsonResponse
    {
        try {
            $staticPage = $this->service->getPublicStaticPageBySlug($slug);

            return $this->success(new StaticPageResource($staticPage), 'Lấy thông tin trang tĩnh thành công.');
        } catch (ModelNotFoundException $e) {
            return $this->error('Không tìm thấy trang tĩnh.', 404);
        }
    }
}
