<?php

use App\Enums\GamePackageStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('game_packages', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('Tên gói');
            $table->unsignedInteger('value')->comment('Số mcoin chuyển đổi');
            $table->foreignId('game_id')->constrained()->cascadeOnDelete();
            $table->tinyInteger('status')->default(GamePackageStatus::ACTIVE->value)->comment('0: Inactive, 1: Active');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('game_packages');
    }
};
