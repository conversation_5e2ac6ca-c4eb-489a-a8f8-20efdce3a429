<?php

namespace App\Http\Requests\CardPrint;

use App\Http\Requests\BaseRequest;

class StoreCardPrintRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'quantity' => 'required|integer|min:1|max:1000',
            'amount' => 'required|numeric|min:10000',
            'note' => 'nullable|string|max:500',
        ];
    }

    /**
     * <PERSON><PERSON> tả các tham số của request body cho tài liệu API.
     *
     * @return array
     */
    public function bodyParameters(): array
    {
        return [
            'quantity' => [
                'description' => 'Số lượng thẻ cần in (tối thiểu 1, tối đa 1000).',
                'example' => 10,
                'type' => 'integer',
            ],
            'amount' => [
                'description' => 'Số tiền cho mỗi thẻ (tối thiểu 10000).',
                'example' => 50000,
                'type' => 'number',
            ],
            'note' => [
                'description' => 'Ghi chú cho yêu cầu in thẻ (tùy chọn).',
                'example' => 'In thẻ cho sự kiện khuyến mãi.',
                'type' => 'string',
            ],
        ];
    }

    /**
     * @return array
     */
    public function messages(): array
    {
        return [
            'quantity.required' => 'Số lượng thẻ là bắt buộc.',
            'quantity.integer' => 'Số lượng thẻ phải là số nguyên.',
            'quantity.min' => 'Số lượng thẻ phải lớn hơn 0.',
            'quantity.max' => 'Số lượng thẻ không được vượt quá 1000.',
            'amount.required' => 'Mệnh giá là bắt buộc.',
            'amount.numeric' => 'Mệnh giá phải là số.',
            'amount.min' => 'Mệnh giá phải lớn hơn 10,000.',
            'note.string' => 'Ghi chú phải là chuỗi ký tự.',
            'note.max' => 'Ghi chú không được vượt quá 500 ký tự.',
        ];
    }
}
