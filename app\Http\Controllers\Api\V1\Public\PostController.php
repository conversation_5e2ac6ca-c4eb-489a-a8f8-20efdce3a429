<?php

namespace App\Http\Controllers\Api\V1\Public;

use App\Http\Controllers\Api\V1\BaseController;
use App\Resources\Public\Posts\PostResource;
use App\Resources\Public\Posts\PostResourceCollection;
use App\Services\PostService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Public - Posts
 *
 * API công khai để lấy dữ liệu bài viết
 */
class PostController extends BaseController
{
    /**
     * Khởi tạo controller với service
     *
     * @param PostService $service
     */
    public function __construct(protected PostService $service)
    {
    }

    /**
     * Lấy danh sách bài viết công khai
     *
     * Lấy danh sách các bài viết đã được xuất bản, hỗ trợ phân trang.
     *
     * @param Request $request
     * @header X-Public-Api-Secret string required Khóa bí mật API công khai. Example: your_super_secret_key_here
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $posts = $this->service->getPublicPosts($request);

        return $this->success(new PostResourceCollection($posts), 'Lấy danh sách bài viết thành công.');
    }

    /**
     * Lấy chi tiết bài viết công khai
     *
     * Hiển thị thông tin chi tiết của một bài viết dựa vào slug.
     *
     * @param string $slug Slug của bài viết
     * @header X-Public-Api-Secret string required Khóa bí mật API công khai. Example: your_super_secret_key_here
     * @return JsonResponse
     */
    public function show(string $slug): JsonResponse
    {
        try {
            $post = $this->service->getPostDetailBySlug($slug);

            return $this->success(new PostResource($post), 'Lấy thông tin bài viết thành công.');
        } catch (ModelNotFoundException $e) {
            return $this->error('Không tìm thấy bài viết.', 404);
        }
    }
}
