<?php

namespace App\Http\Controllers\Api\V1\Public;

use App\Http\Controllers\Api\V1\BaseController;
use App\Resources\Public\SiteSettingGroups\SiteSettingGroupResource;
use App\Services\SiteSettingGroupService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;

/**
 * @group Public - Site Settings
 *
 * API công khai để lấy dữ liệu cài đặt trang web
 */
class SiteSettingGroupController extends BaseController
{
    /**
     * Khởi tạo controller với service
     *
     * @param SiteSettingGroupService $service
     */
    public function __construct(protected SiteSettingGroupService $service)
    {
    }

    /**
     * Lấy cài đặt theo group_key
     *
     * Trả về một object key-value các cài đặt công khai
     * thuộc về một nhóm (ví dụ: 'general_info').
     *
     * @urlParam group_key string required Key của nhóm cài đặt. Example: general_info
     *
     * @param string $group_key
     * @header X-Public-Api-Secret string required Khóa bí mật API công khai. Example: your_super_secret_key_here
     * @return JsonResponse
     */
    public function showByGroupKey(string $group_key): JsonResponse
    {
        try {
            $settingGroup = $this->service->getPublicGroupWithSettings($group_key);

            return $this->success(new SiteSettingGroupResource($settingGroup), 'Lấy cài đặt trang web thành công.');
        } catch (ModelNotFoundException $e) {
            return $this->error('Không tìm thấy nhóm cài đặt.', 404);
        }
    }
}
