<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

/**
 * Middleware kiểm tra quyền truy cập cho API
 *
 * Middleware này kiểm tra xem user có quyền truy cập vào route cụ thể không:
 * - Kiểm tra user đã được authenticate chưa
 * - Kiểm tra user có permission(s) cần thiết không
 * - Hỗ trợ kiểm tra nhiều permissions với logic AND hoặc OR
 * - Trả về lỗi 403 nếu không có quyền
 *
 * Sử dụng:
 * - Một permission: middleware('permission:user.view')
 * - Nhiều permissions (AND): middleware('permission:user.view,user.create')
 * - Nhiều permissions (OR): middleware('permission:user.view|user.create')
 * - <PERSON><PERSON><PERSON> hợp AND/OR: middleware('permission:user.view,user.create|admin.view')
 */
class CheckPermission
{
    /**
     * <PERSON><PERSON><PERSON> nghĩa các mã lỗi chuẩn hóa
     */
    private const ERROR_CODES = [
        'PERMISSION_DENIED' => 'PERMISSION_DENIED',
        'PERMISSION_NOT_FOUND' => 'PERMISSION_NOT_FOUND',
        'USER_NOT_AUTHENTICATED' => 'USER_NOT_AUTHENTICATED',
        'INVALID_PERMISSION_FORMAT' => 'INVALID_PERMISSION_FORMAT',
    ];

    /**
     * Xử lý request đi qua middleware
     *
     * @param Request $request Request hiện tại
     * @param Closure $next Middleware tiếp theo trong pipeline
     * @param string $permissionsString Chuỗi permissions cần kiểm tra
     * @return mixed Response hoặc tiếp tục xử lý
     */
    public function handle(Request $request, Closure $next, string $permissionsString): mixed
    {
        try {
            // Kiểm tra user đã được authenticate chưa
            if (! Auth::guard('api')->check()) {
                return $this->forbiddenResponse(
                    'User chưa được xác thực',
                    self::ERROR_CODES['USER_NOT_AUTHENTICATED']
                );
            }

            $user = Auth::guard('api')->user();

            // Parse và kiểm tra permissions
            if (! $this->checkPermissions($user, $permissionsString)) {
                Log::warning('Permission denied', [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                    'required_permissions' => $permissionsString,
                    'user_permissions' => $user->getAllPermissions()->pluck('name')->toArray(),
                    'route' => $request->route()->getName(),
                    'method' => $request->method(),
                    'url' => $request->fullUrl(),
                    'ip' => $request->ip(),
                ]);

                return $this->forbiddenResponse(
                    'Bạn không có quyền truy cập vào tài nguyên này',
                    self::ERROR_CODES['PERMISSION_DENIED']
                );
            }

            // Cho phép request tiếp tục
            return $next($request);

        } catch (\Exception $e) {
            Log::error('Permission middleware error: ' . $e->getMessage(), [
                'permissions' => $permissionsString,
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->forbiddenResponse(
                'Lỗi kiểm tra quyền truy cập',
                self::ERROR_CODES['PERMISSION_DENIED']
            );
        }
    }

    /**
     * Kiểm tra permissions theo logic phức tạp
     *
     * @param mixed $user User instance
     * @param string $permissionsString Chuỗi permissions (có thể chứa ,, |)
     * @return bool
     */
    protected function checkPermissions($user, string $permissionsString): bool
    {
        // Nếu không có ký tự đặc biệt, kiểm tra một permission
        if (! str_contains($permissionsString, ',') && ! str_contains($permissionsString, '|')) {
            return $user->hasPermissionTo($permissionsString, 'api');
        }

        // Parse chuỗi permissions phức tạp
        return $this->evaluatePermissionExpression($user, $permissionsString);
    }

    /**
     * Đánh giá biểu thức permission phức tạp
     *
     * @param mixed $user User instance
     * @param string $expression Biểu thức permission
     * @return bool
     */
    protected function evaluatePermissionExpression($user, string $expression): bool
    {
        // Tách theo OR (|) trước
        $orParts = explode('|', $expression);

        foreach ($orParts as $orPart) {
            // Nếu phần OR này có chứa AND (,)
            if (str_contains($orPart, ',')) {
                $andParts = explode(',', $orPart);
                $allAndTrue = true;

                foreach ($andParts as $andPart) {
                    $permission = trim($andPart);
                    if (! $user->hasPermissionTo($permission, 'api')) {
                        $allAndTrue = false;

                        break;
                    }
                }

                // Nếu tất cả AND đều true, return true
                if ($allAndTrue) {
                    return true;
                }
            } else {
                // Chỉ có một permission đơn lẻ
                $permission = trim($orPart);
                if ($user->hasPermissionTo($permission, 'api')) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Tạo response cho các lỗi forbidden (403)
     *
     * @param string $message Thông báo lỗi cho user
     * @param string $code Error code cho client handling
     * @return JsonResponse
     */
    protected function forbiddenResponse(string $message, string $code): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'error_code' => $code,
            'timestamp' => now()->toIso8601String(),
        ], 403);
    }
}
