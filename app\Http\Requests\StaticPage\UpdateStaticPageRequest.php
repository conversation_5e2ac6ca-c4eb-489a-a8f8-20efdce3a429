<?php

namespace App\Http\Requests\StaticPage;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateStaticPageRequest extends FormRequest
{
    /**
     * Xác định người dùng có được phép thực hiện request này không
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Các quy tắc validation cho request
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $staticPageId = $this->route('id');

        return [
            'title' => ['sometimes', 'string', 'max:255'],
            'slug' => [
                'sometimes',
                'string',
                'max:255',
                Rule::unique('static_pages', 'slug')->ignore($staticPageId),
            ],
            'body' => ['sometimes', 'string'],
            'status' => ['sometimes', 'integer', 'in:0,1'],
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:500'],
            'meta_keywords' => ['nullable', 'string', 'max:255'],
        ];
    }

    /**
     * Các thông báo lỗi tùy chỉnh cho các quy tắc validation
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'title.max' => 'Tiêu đề trang không được vượt quá 255 ký tự.',
            'slug.max' => 'Slug trang không được vượt quá 255 ký tự.',
            'slug.unique' => 'Slug này đã tồn tại, vui lòng chọn slug khác.',
            'status.integer' => 'Trạng thái trang phải là số nguyên.',
            'status.in' => 'Trạng thái trang không hợp lệ.',
            'meta_title.max' => 'Meta title không được vượt quá 255 ký tự.',
            'meta_description.max' => 'Meta description không được vượt quá 500 ký tự.',
            'meta_keywords.max' => 'Meta keywords không được vượt quá 255 ký tự.',
        ];
    }

    /**
     * Định nghĩa tham số cho tài liệu API (Scribe)
     *
     * @return array<string, array<string, mixed>>
     */
    public function bodyParameters(): array
    {
        return [
            'title' => [
                'type' => 'string',
                'description' => 'Tiêu đề trang tĩnh.',
                'example' => 'Giới thiệu về chúng tôi (Cập nhật)',
            ],
            'slug' => [
                'type' => 'string',
                'description' => 'Slug của trang tĩnh, dùng cho URL.',
                'example' => 'gioi-thieu-ve-chung-toi',
            ],
            'body' => [
                'type' => 'string',
                'description' => 'Nội dung HTML của trang tĩnh.',
                'example' => '<h1>Giới thiệu về chúng tôi</h1><p>Nội dung chi tiết đã cập nhật...</p>',
            ],
            'status' => [
                'type' => 'integer',
                'description' => 'Trạng thái trang: 1 (hiển thị), 0 (ẩn).',
                'example' => 1,
            ],
            'meta_title' => [
                'type' => 'string',
                'description' => 'Tiêu đề SEO cho trang.',
                'example' => 'Giới thiệu về công ty XYZ - Trang chính thức (Cập nhật)',
            ],
            'meta_description' => [
                'type' => 'string',
                'description' => 'Mô tả SEO cho trang.',
                'example' => 'Trang giới thiệu chính thức về công ty XYZ, lịch sử hình thành và phát triển từ năm 2010...',
            ],
            'meta_keywords' => [
                'type' => 'string',
                'description' => 'Từ khóa SEO cho trang, phân cách bằng dấu phẩy.',
                'example' => 'giới thiệu, công ty, lịch sử, thành tựu, tầm nhìn, sứ mệnh',
            ],
        ];
    }
}
