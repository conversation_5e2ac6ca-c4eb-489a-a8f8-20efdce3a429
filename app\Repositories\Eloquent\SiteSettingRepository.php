<?php

namespace App\Repositories\Eloquent;

use App\Models\SiteSetting;
use App\Repositories\Interfaces\SiteSettingRepositoryInterface;

/**
 * Class SiteSettingRepository
 * @package App\Repositories\Eloquent
 */
class SiteSettingRepository extends BaseRepository implements SiteSettingRepositoryInterface
{
    /**
     * Danh sách các trường có thể tìm kiếm toàn văn.
     *
     * @var array
     */
    protected array $searchableFields = [
        'name',
        'key',
        'description',
        'value',
    ];

    /**
     * Trả về tên lớp Model.
     *
     * @return string
     */
    protected function getModelClass(): string
    {
        return SiteSetting::class;
    }
}
