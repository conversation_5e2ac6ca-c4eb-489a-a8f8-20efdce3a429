<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class MenuItem extends BaseModel
{
    use HasFactory;
    use SoftDeletes;

    /**
     * C<PERSON>c thuộc tính nên được <PERSON>n khi chuyển thành mảng hoặc JSON.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'created_by',
        'updated_by',
        'linkable_id',
        'linkable_type',
    ];
    /**
     * Khóa chính của bảng.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Các thuộc tính có thể gán hàng loạt.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'menu_id',
        'parent_id',
        'title',
        'slug',
        'order',
        'target',
        'linkable_id',
        'linkable_type',
        'custom_url',
        'status',
        'created_by',
        'updated_by',
    ];

    /**
     * Tên bảng tương ứng với model.
     *
     * @var string
     */
    protected $table = 'menu_items';

    /**
     * <PERSON><PERSON><PERSON> các casts dành riêng cho model này.
     *
     * @return array
     */
    protected function getModelCasts(): array
    {
        return [
            'status' => 'boolean',
            'order' => 'integer',
        ];
    }

    /**
     * Lấy menu mà menu item này thuộc về.
     */
    public function menu()
    {
        return $this->belongsTo(Menu::class);
    }

    /**
     * Lấy menu item cha.
     */
    public function parent()
    {
        return $this->belongsTo(MenuItem::class, 'parent_id');
    }

    /**
     * Lấy các menu item con.
     */
    public function children()
    {
        return $this->hasMany(MenuItem::class, 'parent_id')
            ->where('status', true)
            ->with(['children', 'linkable'])
            ->orderBy('order');
    }

    /**
     * Lấy model có thể liên kết (Post, Category, etc.).
     */
    public function linkable()
    {
        return $this->morphTo();
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
