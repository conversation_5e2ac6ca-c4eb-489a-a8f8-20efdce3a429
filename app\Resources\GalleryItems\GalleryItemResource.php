<?php

namespace App\Resources\GalleryItems;

use App\Resources\BaseResource;
use Illuminate\Http\Request;

/**
 * Resource cho Gallery Item
 */
class GalleryItemResource extends BaseResource
{
    /**
     * D<PERSON> liệu đặc biệt cho resource
     *
     * @param Request $request
     * @return array
     */
    protected function resourceData($request): array
    {
        return [
            'is_image_valid' => ! empty($this->image_url) && filter_var($this->image_url, FILTER_VALIDATE_URL),
            'status_text' => $this->status === 1 ? 'Hoạt động' : 'Không hoạt động',
            'is_active' => $this->status === 1,
        ];
    }
}
