<?php

namespace App\Http\Controllers\Api\V1\Public;

use App\Http\Controllers\Api\V1\BaseController;
use App\Resources\Public\GalleryGroups\GalleryGroupResource;
use App\Services\GalleryGroupService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;

/**
 * @group Public - Galleries
 *
 * API công khai để lấy dữ liệu thư viện ảnh
 */
class GalleryGroupController extends BaseController
{
    /**
     * Khởi tạo controller với service
     *
     * @param GalleryGroupService $service
     */
    public function __construct(protected GalleryGroupService $service)
    {
    }

    /**
     * Lấy thư viện ảnh theo location_key
     *
     * Trả về thông tin của một nhóm thư viện ảnh và danh sách các ảnh
     * (gallery items) đang hoạt động thuộc nhóm đó.
     *
     * @urlParam location_key string required The location key of the gallery group. Example: homepage-slider
     *
     * @param string $location_key
     * @header X-Public-Api-Secret string required Khóa bí mật API công khai. Example: your_super_secret_key_here
     * @return JsonResponse
     */
    public function showByLocationKey(string $location_key): JsonResponse
    {
        try {
            $galleryGroup = $this->service->getGalleryGroupByLocationKey($location_key);

            return $this->success(new GalleryGroupResource($galleryGroup), 'Lấy thông tin thư viện ảnh thành công.');
        } catch (ModelNotFoundException $e) {
            return $this->error('Không tìm thấy thư viện ảnh.', 404);
        }
    }
}
