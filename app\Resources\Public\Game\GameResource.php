<?php

namespace App\Resources\Public\Game;

use App\Resources\BaseResource;
use App\Resources\Public\GamePackage\GamePackageResource;

class GameResource extends BaseResource
{
    /**
     * @param $request
     * @return array
     */
    protected function resourceData($request): array
    {
        return [
            'packages' => GamePackageResource::collection($this->whenLoaded('packages')),
        ];
    }
}
