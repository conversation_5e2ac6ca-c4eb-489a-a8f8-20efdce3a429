<?php

namespace App\Services;

use App\Repositories\Eloquent\UserRepository;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Tymon\JWTAuth\Exceptions\JWTException;
use Ty<PERSON>\JWTAuth\Facades\JWTAuth;

/**
 * Service xử lý các nghiệp vụ liên quan đến xác thực người dùng
 *
 * Class này chịu trách nhiệm:
 * - Xử lý đăng nhập/đăng xuất
 * - Quản lý rate limiting để chống brute force
 * - Tạo và hủy JWT tokens
 * - Ghi log các hoạt động xác thực
 */
class AuthService extends BaseService
{
    /**
     * Cấu hình Rate Limit (giới hạn số lần thử)
     *
     * IP_RATE_LIMIT: Số lần thử tối đa từ 1 IP
     * IP_RATE_WINDOW: Thời gian reset counter cho IP (tính bằng giây)
     * EMAIL_RATE_LIMIT: Số lần thử tối đa cho 1 email
     * EMAIL_RATE_WINDOW: Thời gian reset counter cho email (tính bằng giây)
     */
    private const IP_RATE_LIMIT = 5;
    private const IP_RATE_WINDOW = 60; // 1 minute
    private const EMAIL_RATE_LIMIT = 3;
    private const EMAIL_RATE_WINDOW = 900; // 15 minutes

    /**
     * Mapping giữa mã lỗi và thông báo lỗi
     *
     * Sử dụng mã lỗi (error code) giúp:
     * - Frontend dễ dàng xử lý theo từng trường hợp cụ thể
     * - Thống nhất cách xử lý lỗi trong toàn hệ thống
     * - Dễ dàng thay đổi nội dung thông báo mà không ảnh hưởng logic
     *
     * Các placeholder như :seconds, :minutes sẽ được thay thế bằng giá trị thực tế
     */
    private const ERRORS = [
        'INVALID_CREDENTIALS' => 'Thông tin đăng nhập không chính xác',
        'TOKEN_CREATION_FAILED' => 'Không thể tạo token, vui lòng thử lại sau',
        'LOGOUT_FAILED' => 'Đăng xuất thất bại, vui lòng thử lại sau',
        'RATE_LIMIT_IP' => 'Quá nhiều lần thử từ IP này. Thử lại sau :seconds giây',
        'RATE_LIMIT_EMAIL' => 'Tài khoản tạm khóa. Thử lại sau :minutes phút',
        'USER_INFO_ERROR' => 'Không thể lấy thông tin user. Vui lòng thử lại sau.',
        'USER_PROFILE_UPDATE_ERROR' => 'Cập nhật thông tin người dùng thất bại',
        'USER_OLD_PASSWORD_INVALID' => 'Mật khẩu cũ không chính xác',
        'USER_CHANGE_PASSWORD_ERROR' => 'Đổi mật khẩu thất bại',
    ];

    public function __construct(
        protected UserRepository $userRepository,
        protected FileUploadService $fileUploadService
    ) {
    }

    /**
     * Override phương thức của BaseService để cung cấp thông báo lỗi riêng cho module Auth
     *
     * Phương thức này:
     * 1. Tìm message tương ứng với errorCode trong ERRORS array
     * 2. Nếu không tìm thấy, sử dụng message mặc định từ parent class
     * 3. Thay thế các placeholder trong message bằng giá trị thực từ params
     *
     * @param string $errorCode Mã lỗi cần lấy thông báo
     * @param array $params Các tham số để thay thế placeholder
     * @return string Thông báo lỗi đã được format
     */
    protected function getErrorMessage(string $errorCode, array $params = []): string
    {
        $message = self::ERRORS[$errorCode] ?? $errorCode;

        return parent::interpolateMessage($message, $params);
    }

    /**
     * Xử lý đăng nhập người dùng
     *
     * Quy trình:
     * 1. Kiểm tra rate limit cho IP và email
     * 2. Thử xác thực với credentials được cung cấp
     * 3. Nếu thất bại: tăng counter rate limit
     * 4. Nếu thành công: xóa rate limit và trả về token
     *
     * @param array $credentials Thông tin đăng nhập ['email' => '', 'password' => '']
     * @param string $ip Địa chỉ IP của người dùng
     * @return array Kết quả xử lý ['success' => bool, 'data' => array, ...]
     */
    public function login(array $credentials, string $ip): array
    {
        // Bước 1: Kiểm tra rate limit trước khi cho phép đăng nhập
        // Điều này giúp chống tấn công brute force
        $rateLimitResult = $this->checkRateLimits($credentials['email'], $ip);
        if (! $this->isSuccess($rateLimitResult)) {
            return $rateLimitResult;
        }

        try {
            // Bước 2: Thử xác thực với JWT
            // JWTAuth::attempt() sẽ:
            // - Validate credentials với database
            // - Tạo token nếu credentials hợp lệ
            // - Trả về false nếu không hợp lệ
            $token = JWTAuth::attempt($credentials);

            if (! $token) {
                // Bước 3a: Đăng nhập thất bại
                // Tăng counter để áp dụng rate limit
                $this->incrementRateLimits($credentials['email'], $ip);

                // Trả về lỗi với mã 401 (Unauthorized)
                return $this->error('INVALID_CREDENTIALS', 401);
            }

            // Bước 3b: Đăng nhập thành công
            // Xóa rate limit để user không bị block
            $this->clearRateLimits($credentials['email'], $ip);

            // Ghi log để tracking và audit
            $this->logInfo('User logged in successfully', [
                'email' => $credentials['email'],
                'ip' => $ip,
            ]);

            return $this->success([
                'access_token' => $token,
                'token_type' => 'bearer',
                'expires_in' => config('jwt.ttl') * 60,
            ]);
        } catch (JWTException $e) {
            $this->logError('JWT Creation Failed', [
                'error' => $e->getMessage(),
                'email' => $credentials['email'],
            ]);

            return $this->error('TOKEN_CREATION_FAILED', 500);
        }
    }

    /**
     * Xử lý đăng xuất người dùng
     *
     * Quy trình:
     * 1. Lấy token hiện tại từ request
     * 2. Invalidate token (thêm vào blacklist)
     * 3. Token này sẽ không còn sử dụng được nữa
     *
     * @return array Kết quả xử lý
     */
    public function logout(): array
    {
        try {
            // Invalidate token hiện tại
            // Token sẽ được thêm vào blacklist và không thể sử dụng lại
            JWTAuth::invalidate(JWTAuth::getToken());

            return $this->success([], 'Đăng xuất thành công');
        } catch (JWTException $e) {
            // Log lỗi khi không thể invalidate token
            $this->logError('Logout Failed', ['error' => $e->getMessage()]);

            return $this->error('LOGOUT_FAILED', 500);
        }
    }

    /**
     * Kiểm tra rate limit cho IP và email
     *
     * Phương thức này áp dụng 2 lớp rate limit:
     * 1. Theo IP: Chống 1 IP spam nhiều tài khoản
     * 2. Theo Email: Bảo vệ từng tài khoản cụ thể
     *
     * @param string $email Email đang thử đăng nhập
     * @param string $ip IP address của request
     * @return array Kết quả kiểm tra
     */
    private function checkRateLimits(string $email, string $ip): array
    {
        // Kiểm tra rate limit theo IP
        // Tạo key unique cho IP này
        $ipKey = $this->getRateLimitKey('ip', $ip);

        // Kiểm tra xem IP này đã vượt quá giới hạn chưa
        if (RateLimiter::tooManyAttempts($ipKey, self::IP_RATE_LIMIT)) {
            // Lấy thời gian còn lại phải chờ (tính bằng giây)
            $seconds = RateLimiter::availableIn($ipKey);

            // Trả về lỗi 429 (Too Many Requests) kèm thời gian chờ
            return $this->error('RATE_LIMIT_IP', 429, [
                'seconds' => $seconds,
                'retry_after' => $seconds, // Header standard cho HTTP 429
            ]);
        }

        // Kiểm tra rate limit theo email
        // Tương tự như IP nhưng với threshold thấp hơn
        $emailKey = $this->getRateLimitKey('email', $email);
        if (RateLimiter::tooManyAttempts($emailKey, self::EMAIL_RATE_LIMIT)) {
            $seconds = RateLimiter::availableIn($emailKey);
            $minutes = ceil($seconds / 60); // Chuyển sang phút cho dễ đọc

            return $this->error('RATE_LIMIT_EMAIL', 429, [
                'minutes' => $minutes,
                'retry_after' => $seconds,
            ]);
        }

        // Không có rate limit, cho phép tiếp tục
        return $this->success();
    }

    /**
     * Tăng counter rate limit khi đăng nhập thất bại
     *
     * Phương thức này được gọi sau mỗi lần đăng nhập thất bại để:
     * - Tăng counter cho IP với window time ngắn (1 phút)
     * - Tăng counter cho email với window time dài hơn (15 phút)
     *
     * @param string $email Email vừa thử đăng nhập
     * @param string $ip IP address của request
     * @return void
     */
    private function incrementRateLimits(string $email, string $ip): void
    {
        // Hit = tăng counter lên 1
        // Sau khi hết window time, counter sẽ tự động reset về 0
        RateLimiter::hit($this->getRateLimitKey('ip', $ip), self::IP_RATE_WINDOW);
        RateLimiter::hit($this->getRateLimitKey('email', $email), self::EMAIL_RATE_WINDOW);
    }

    /**
     * Xóa rate limit khi đăng nhập thành công
     *
     * Điều này giúp:
     * - User không bị ảnh hưởng bởi các lần thử sai trước đó
     * - Reset counter về 0 ngay lập tức
     *
     * @param string $email Email vừa đăng nhập thành công
     * @param string $ip IP address của request
     * @return void
     */
    private function clearRateLimits(string $email, string $ip): void
    {
        RateLimiter::clear($this->getRateLimitKey('ip', $ip));
        RateLimiter::clear($this->getRateLimitKey('email', $email));
    }

    /**
     * Tạo key unique cho rate limiter
     *
     * Format: "login_{type}:{identifier}"
     * Ví dụ:
     * - "login_ip:***********"
     * - "login_email:<EMAIL>"
     *
     * @param string $type Loại rate limit ('ip' hoặc 'email')
     * @param string $identifier Giá trị định danh (IP address hoặc email)
     * @return string Key để sử dụng với RateLimiter
     */
    private function getRateLimitKey(string $type, string $identifier): string
    {
        return "login_{$type}:{$identifier}";
    }

    /**
     * Lấy thông tin user đang đăng nhập kèm danh sách quyền
     *
     * @return array Kết quả xử lý
     */
    public function getUserInfo(): array
    {
        try {
            /** @var \App\Models\User $user */
            $user = auth('api')->user();

            if (! $user) {
                return $this->error('USER_NOT_FOUND', 401);
            }

            $permissions = $user->getAllPermissions()->pluck('name')->toArray();
            $roles = $user->roles->pluck('name')->toArray();

            return $this->success(
                [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'avatar' => $user->avatar,
                    'status' => $user->status,
                    'roles' => implode(' ,', $roles),
                    'permissions' => $permissions,
                ],
                'Lấy thông tin user thành công'
            );
        } catch (Exception $e) {
            $this->logError('Error getting user info: ' . $e->getMessage());

            return $this->error('USER_INFO_ERROR', 500);
        }
    }

    public function updateProfile(int $userId, array $data): array
    {
        DB::beginTransaction();

        try {
            $user = $this->userRepository->findOrFail($userId);
            // Handle avatar upload if a file present
            if (request()->hasFile('avatar')) {
                $avatarFile = request()->file('avatar');
                $result = $this->fileUploadService->uploadImage($avatarFile, 'avatars', 'public', $user->avatar, [
                    'max_size' => 2 * 1024 * 1024, // 2MB
                    'min_width' => 100,
                    'min_height' => 100,
                    'max_width' => 2048,
                    'max_height' => 2048,
                ]);
                if (! $result['success']) {
                    DB::rollBack();

                    return $result;
                }
                $data['avatar'] = $result['data']['url'];
            }

            if (! isset($data['avatar'])) {
                unset($data['avatar']);
            }

            $user = $this->userRepository->update($userId, $data);
            DB::commit();

            return $this->success(['user' => $user], 'Cập nhật thông tin thành công');
        } catch (Exception $e) {
            DB::rollBack();
            $this->logError('Error updating profile: ' . $e->getMessage(), ['user_id' => $userId]);

            return $this->error('USER_PROFILE_UPDATE_ERROR', 500);
        }
    }

    public function changePassword(int $userId, array $data): array
    {
        DB::beginTransaction();

        try {
            $user = $this->userRepository->findOrFail($userId);
            if (! Hash::check($data['old_password'], $user->password)) {
                return $this->error('USER_OLD_PASSWORD_INVALID', 400);
            }
            $user = $this->userRepository->update($userId, [
                'password' => Hash::make($data['new_password']),
            ]);

            DB::commit();

            return $this->success(
                [
                    'user' => $user,
                ],
                'Đổi mật khẩu thành công'
            );
        } catch (Exception $e) {
            DB::rollBack();
            $this->logError('Error changing password: ' . $e->getMessage(), [
                'user_id' => $userId,
            ]);

            return $this->error('USER_CHANGE_PASSWORD_ERROR', 500);
        }
    }
}
