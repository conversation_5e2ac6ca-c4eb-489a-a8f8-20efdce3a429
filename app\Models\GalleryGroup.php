<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class GalleryGroup extends BaseModel
{
    use HasFactory;
    use SoftDeletes;

    protected $hidden = [
        'created_by',
        'updated_by',
    ];

    protected $fillable = [
        'name',
        'location_key',
        'location_display',
        'description',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'location_display' => 'integer',
    ];

    /**
     * Quan hệ với user tạo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Quan hệ với user cập nhật
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Quan hệ với gallery items
     */
    public function galleryItems(): HasMany
    {
        return $this->hasMany(GalleryItem::class);
    }
}
