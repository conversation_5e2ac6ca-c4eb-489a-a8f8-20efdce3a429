<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\Overview\GetOverviewByDateRequest;
use App\Http\Requests\Statistics\GetDailyStatisticsRequest;
use App\Http\Requests\Statistics\GetRevenueStatisticsRequest;
use App\Services\AnalyticService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Thống kê - Tổng quan
 */
class OverviewController extends Controller
{
    use ApiResponse;

    public function __construct(
        protected AnalyticService $analyticService,
    ) {
    }

    /**
     * Lấy thông tin tổng quan.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $result = $this->analyticService->getOverview();

        return $this->success($result, '<PERSON><PERSON>y thông tin tổng quan thành công!');
    }

    /**
     * Tổng quan theo khoảng thời gian.
     *
     * @param GetOverviewByDateRequest $request
     * @return JsonResponse
     */
    public function getOverviewByDate(GetOverviewByDateRequest $request): JsonResponse
    {
        $data = $request->validated();
        $result = $this->analyticService->getOverviewByDate(
            $data['type'] ?? 1,
            $data['time_period'] ?? 1
        );

        return $this->success($result, 'Lấy thông tin tổng quan theo ngày thành công!');
    }

    /**
     * Thống kê hàng ngày.
     *
     * @param GetDailyStatisticsRequest $request
     * @return JsonResponse
     */
    public function dailyStatistics(GetDailyStatisticsRequest $request): JsonResponse
    {
        $data = $request->validated();
        $result = $this->analyticService->getDailyStatistics(
            $data['start_date'] ?? now()->subDays(7)->format('Y-m-d'),
            $data['end_date'] ?? now()->format('Y-m-d'),
            $data['type'] ?? null
        );

        return $this->success($result, 'Lấy thống kê hàng ngày thành công!');
    }

    /**
     * Thống kê doanh thu.
     *
     * @param GetRevenueStatisticsRequest $request
     * @return JsonResponse
     */
    public function revenueStatistics(GetRevenueStatisticsRequest $request): JsonResponse
    {
        $data = $request->validated();
        $result = $this->analyticService->getRevenueStatistics(
            $data['start_date'] ?? now()->subDays(30)->format('Y-m-d'),
            $data['end_date'] ?? now()->format('Y-m-d')
        );

        return $this->success($result, 'Lấy thống kê doanh thu thành công!');
    }
}
