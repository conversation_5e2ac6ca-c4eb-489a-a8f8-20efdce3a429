<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class Game extends BaseModel
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'thumb',
        'slug',
        'meta_data',
        'status',
    ];

    public function packages(): HasMany
    {
        return $this->hasMany(GamePackage::class);
    }

    public function getThumbAttribute($value): string
    {
        return $value ? Storage::url($value) : '';
    }
}
