<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Model cho nhóm cài đặt hệ thống
 */
class SiteSettingGroup extends BaseModel
{
    use HasFactory;
    use SoftDeletes;

    /**
     * Các thuộc tính nên được ẩn khi chuyển thành mảng hoặc JSON.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'created_by',
        'updated_by',
    ];

    /**
     * Khóa chính của bảng.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Các thuộc tính có thể gán hàng loạt.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'group_key',
        'description',
        'order',
        'created_by',
        'updated_by',
    ];

    /**
     * Tên bảng tương ứng với model.
     *
     * @var string
     */
    protected $table = 'site_setting_groups';

    /**
     * <PERSON><PERSON><PERSON> các casts dành riêng cho model này.
     *
     * @return array
     */
    protected function getModelCasts(): array
    {
        return [
            'id' => 'integer',
        ];
    }

    /**
     * Quan hệ với các cài đặt trong nhóm
     *
     * @return HasMany
     */
    public function siteSettings(): HasMany
    {
        return $this->hasMany(SiteSetting::class, 'group_id');
    }

    /**
     * Quan hệ với người tạo
     *
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Quan hệ với người cập nhật
     *
     * @return BelongsTo
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
