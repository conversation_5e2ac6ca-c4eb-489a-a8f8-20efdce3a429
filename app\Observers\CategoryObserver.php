<?php

namespace App\Observers;

use App\Models\Category;

/**
 * Observer xử lý các sự kiện liên quan đến Category
 */
class CategoryObserver extends BaseObserver
{
    /**
     * Xử lý sự kiện "created" của Category.
     *
     * @param \App\Models\Category $category
     * @return void
     */
    public function created(Category $category): void
    {
        $this->log(
            action: 'create',
            description: "Tạo mới danh mục '{$category->name}'",
            model: $category,
            dataAfter: $category->toArray()
        );
    }

    /**
     * Xử lý sự kiện "updated" của Category.
     *
     * @param \App\Models\Category $category
     * @return void
     */
    public function updated(Category $category): void
    {
        $this->log(
            action: 'update',
            description: "Cập nhật danh mục '{$category->name}'",
            model: $category,
            dataBefore: $category->getOriginal(),
            dataAfter: $category->getChanges()
        );
    }

    /**
     * Xử lý sự kiện "deleting" của Category.
     *
     * @param \App\Models\Category $category
     * @return void
     */
    public function deleting(Category $category): void
    {
        // Ghi log trước khi thay đổi slug
        $this->log(
            action: 'delete',
            description: "Xóa danh mục '{$category->name}'",
            model: $category,
            dataBefore: $category->toArray()
        );

        // Thêm timestamp vào slug để tránh xung đột khi tạo mới
        if (! $category->isForceDeleting()) {
            $category->slug = $category->slug . '_deleted_' . time();
            $category->saveQuietly(); // Lưu mà không kích hoạt các events
        }
    }
}
