<?php

namespace App\Observers;

use App\Models\SiteSettingGroup;

/**
 * Observer xử lý các sự kiện liên quan đến SiteSettingGroup
 */
class SiteSettingGroupObserver extends BaseObserver
{
    /**
     * <PERSON><PERSON> lý sự kiện "created" của SiteSettingGroup.
     *
     * @param \App\Models\SiteSettingGroup $siteSettingGroup
     * @return void
     */
    public function created(SiteSettingGroup $siteSettingGroup): void
    {
        $this->log(
            action: 'create',
            description: "Tạo mới nhóm cài đặt '{$siteSettingGroup->name}'",
            model: $siteSettingGroup,
            dataAfter: $siteSettingGroup->toArray()
        );
    }

    /**
     * Xử lý sự kiện "updated" của SiteSettingGroup.
     *
     * @param \App\Models\SiteSettingGroup $siteSettingGroup
     * @return void
     */
    public function updated(SiteSettingGroup $siteSettingGroup): void
    {
        $this->log(
            action: 'update',
            description: "<PERSON><PERSON>p nhật nhóm cài đặt '{$siteSettingGroup->name}'",
            model: $siteSettingGroup,
            dataBefore: $siteSettingGroup->getOriginal(),
            dataAfter: $siteSettingGroup->getChanges()
        );
    }

    /**
     * Xử lý sự kiện "deleting" của SiteSettingGroup.
     *
     * @param \App\Models\SiteSettingGroup $siteSettingGroup
     * @return void
     */
    public function deleting(SiteSettingGroup $siteSettingGroup): void
    {
        $this->log(
            action: 'delete',
            description: "Xóa nhóm cài đặt '{$siteSettingGroup->name}'",
            model: $siteSettingGroup,
            dataBefore: $siteSettingGroup->toArray()
        );

        if (! $siteSettingGroup->isForceDeleting()) {
            $siteSettingGroup->group_key = $siteSettingGroup->group_key . '_deleted_' . time();
            $siteSettingGroup->saveQuietly();
        }
    }
}
