<?php

namespace App\Repositories\Eloquent;

use App\Models\StaticPage;
use App\Repositories\Interfaces\StaticPageRepositoryInterface;

/**
 * Repository xử lý các thao tác với model StaticPage
 */
class StaticPageRepository extends BaseRepository implements StaticPageRepositoryInterface
{
    /**
     * <PERSON>h sách các trường có thể tìm kiếm toàn văn.
     *
     * @var array
     */
    protected array $searchableFields = [
        'title',
        'slug',
        'body',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    /**
     * <PERSON><PERSON>c đ<PERSON>nh model class sử dụng cho repository này
     *
     * @return string
     */
    protected function getModelClass(): string
    {
        return StaticPage::class;
    }
}
