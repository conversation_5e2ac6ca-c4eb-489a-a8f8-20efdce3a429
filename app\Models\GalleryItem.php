<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Model cho item trong thư viện ảnh
 */
class GalleryItem extends BaseModel
{
    use HasFactory;
    use SoftDeletes;

    protected $hidden = [
        'created_by',
        'updated_by',
    ];

    protected $fillable = [
        'gallery_group_id',
        'image_url',
        'alt_text',
        'title',
        'link',
        'target',
        'status',
        'order',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'status' => 'integer',
        'order' => 'integer',
        'target' => 'string',
    ];

    /**
     * Quan hệ với gallery group
     */
    public function galleryGroup(): BelongsTo
    {
        return $this->belongsTo(GalleryGroup::class);
    }

    /**
     * Quan hệ với user tạo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * <PERSON>uan hệ với user cập nhật
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
