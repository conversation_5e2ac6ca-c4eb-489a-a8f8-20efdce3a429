<?php

namespace App\Observers;

use App\Models\GalleryGroup;

/**
 * Observer xử lý các sự kiện liên quan đến GalleryGroup
 */
class GalleryGroupObserver extends BaseObserver
{
    /**
     * Xử lý sự kiện "created" của GalleryGroup.
     *
     * @param \App\Models\GalleryGroup $galleryGroup
     * @return void
     */
    public function created(GalleryGroup $galleryGroup): void
    {
        $this->log(
            action: 'create',
            description: "Tạo mới nhóm thư viện ảnh '{$galleryGroup->name}'",
            model: $galleryGroup,
            dataAfter: $galleryGroup->toArray()
        );
    }

    /**
     * Xử lý sự kiện "updated" của GalleryGroup.
     *
     * @param \App\Models\GalleryGroup $galleryGroup
     * @return void
     */
    public function updated(GalleryGroup $galleryGroup): void
    {
        $this->log(
            action: 'update',
            description: "Cập nhật nhóm thư viện ảnh '{$galleryGroup->name}'",
            model: $galleryGroup,
            dataBefore: $galleryGroup->getOriginal(),
            dataAfter: $galleryGroup->getChanges()
        );
    }

    /**
     * Xử lý sự kiện "deleting" của GalleryGroup.
     *
     * @param \App\Models\GalleryGroup $galleryGroup
     * @return void
     */
    public function deleting(GalleryGroup $galleryGroup): void
    {
        $this->log(
            action: 'delete',
            description: "Xóa nhóm thư viện ảnh '{$galleryGroup->name}'",
            model: $galleryGroup,
            dataBefore: $galleryGroup->toArray()
        );

        if (! $galleryGroup->isForceDeleting()) {
            $galleryGroup->location_key = $galleryGroup->location_key . '_deleted_' . time();
            $galleryGroup->saveQuietly();
        }
    }
}
