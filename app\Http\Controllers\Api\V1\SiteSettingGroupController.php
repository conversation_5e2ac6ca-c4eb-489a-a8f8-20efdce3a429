<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\SiteSettingGroup\StoreSiteSettingGroupRequest;
use App\Http\Requests\SiteSettingGroup\UpdateSiteSettingGroupRequest;
use App\Resources\SiteSettingGroup\SiteSettingGroupResource;
use App\Resources\SiteSettingGroup\SiteSettingGroupResourceCollection;
use App\Services\SiteSettingGroupService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Cài đặt trang web - Group
 */
class SiteSettingGroupController extends BaseController
{
    use ApiResponse;

    /**
     * Khởi tạo controller với service injection
     *
     * @param SiteSettingGroupService $service
     */
    public function __construct(protected SiteSettingGroupService $service)
    {
    }

    /**
     * Hiển thị danh sách nhóm cài đặt hệ thống.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $siteSettingGroups = $this->service->list($request, ['createdBy', 'updatedBy']);

        return $this->success(
            new SiteSettingGroupResourceCollection($siteSettingGroups),
            'Lấy danh sách nhóm cài đặt hệ thống thành công.'
        );
    }

    /**
     * Lưu nhóm cài đặt hệ thống mới.
     *
     * @param StoreSiteSettingGroupRequest $request
     * @return JsonResponse
     */
    public function store(StoreSiteSettingGroupRequest $request): JsonResponse
    {
        try {
            $siteSettingGroup = $this->service->create($request->validated(), ['createdBy', 'updatedBy']);

            return $this->success(
                new SiteSettingGroupResource($siteSettingGroup),
                'Tạo nhóm cài đặt hệ thống thành công.',
                201
            );
        } catch (\InvalidArgumentException $e) {
            return $this->error($e->getMessage(), 422);
        }
    }

    /**
     * Hiển thị chi tiết nhóm cài đặt hệ thống.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $siteSettingGroup = $this->service->read($id, ['createdBy', 'updatedBy']);

        return $this->success(
            new SiteSettingGroupResource($siteSettingGroup),
            'Lấy thông tin nhóm cài đặt hệ thống thành công.'
        );
    }

    /**
     * Cập nhật nhóm cài đặt hệ thống.
     *
     * @param UpdateSiteSettingGroupRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateSiteSettingGroupRequest $request, int $id): JsonResponse
    {
        try {
            $siteSettingGroup = $this->service->update($id, $request->validated(), ['createdBy', 'updatedBy']);

            return $this->success(
                new SiteSettingGroupResource($siteSettingGroup),
                'Cập nhật nhóm cài đặt hệ thống thành công.'
            );
        } catch (\InvalidArgumentException $e) {
            return $this->error($e->getMessage(), 422);
        }
    }

    /**
     * Xóa nhóm cài đặt hệ thống.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $deleted = $this->service->delete($id);

        return $deleted
            ? $this->successNoContent('Xóa nhóm cài đặt hệ thống thành công.')
            : $this->error('Không thể xóa nhóm cài đặt hệ thống.', 500);
    }
}
