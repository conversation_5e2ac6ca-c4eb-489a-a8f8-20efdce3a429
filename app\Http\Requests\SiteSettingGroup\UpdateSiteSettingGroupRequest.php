<?php

namespace App\Http\Requests\SiteSettingGroup;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Form Request cho việc cập nhật nhóm cài đặt hệ thống
 */
class UpdateSiteSettingGroupRequest extends FormRequest
{
    /**
     * Xác định người dùng có quyền thực hiện request này không
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Quy tắc validation cho request
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // Lấy ID của site setting group từ route
        $id = $this->route('id');

        return [
            'name' => ['sometimes', 'required', 'string', 'max:255'],
            'group_key' => [
                'sometimes',
                'required',
                'string',
                'max:100',
                Rule::unique('site_setting_groups', 'group_key')->ignore($id),
                'regex:/^[a-z0-9_-]+$/',
            ],
            'description' => ['nullable', 'string', 'max:1000'],
            'order' => ['sometimes', 'nullable', 'integer', 'min:0'],
        ];
    }

    /**
     * Thông báo lỗi tùy chỉnh cho validation
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Tên nhóm cài đặt là bắt buộc.',
            'name.string' => 'Tên nhóm cài đặt phải là chuỗi ký tự.',
            'name.max' => 'Tên nhóm cài đặt không được vượt quá 255 ký tự.',

            'group_key.required' => 'Khóa nhóm là bắt buộc.',
            'group_key.string' => 'Khóa nhóm phải là chuỗi ký tự.',
            'group_key.max' => 'Khóa nhóm không được vượt quá 100 ký tự.',
            'group_key.unique' => 'Khóa nhóm đã tồn tại trong hệ thống.',
            'group_key.regex' => 'Khóa nhóm chỉ được chứa chữ thường, số, dấu gạch dưới (_) và gạch ngang (-).',

            'description.string' => 'Mô tả phải là chuỗi ký tự.',
            'description.max' => 'Mô tả không được vượt quá 1000 ký tự.',

            'order.integer' => 'Thứ tự phải là số nguyên.',
            'order.min' => 'Thứ tự không được nhỏ hơn 0.',
        ];
    }

    /**
     * Định nghĩa tham số cho tài liệu API
     *
     * @return array<string, array<string, mixed>>
     */
    public function bodyParameters(): array
    {
        return [
            'name' => [
                'type' => 'string',
                'description' => 'Tên nhóm cài đặt.',
                'example' => 'Cài đặt chung',
            ],
            'group_key' => [
                'type' => 'string',
                'description' => 'Khóa nhóm chỉ được chứa chữ thường, số, dấu gạch dưới (_) và gạch ngang (-).',
                'example' => 'general_settings',
            ],
            'description' => [
                'type' => 'string',
                'description' => 'Mô tả nhóm cài đặt.',
                'example' => 'Nhóm cài đặt chung cho hệ thống',
            ],
            'order' => [
                'type' => 'integer',
                'description' => 'Thứ tự sắp xếp của nhóm cài đặt.',
                'example' => 1,
            ],
        ];
    }
}
