<?php

namespace App\Http\Requests\CardPrint;

use App\Http\Requests\BaseRequest;

class VerifyCardRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'serial' => 'required|string|size:15',
            'pin' => 'required|string|size:12',
        ];
    }

    /**
     * <PERSON><PERSON> tả các tham số của request body cho tài liệu API.
     *
     * @return array
     */
    public function bodyParameters(): array
    {
        return [
            'serial' => [
                'description' => 'Số seri của thẻ (15 ký tự).',
                'example' => '123456789012345',
                'type' => 'string',
            ],
            'pin' => [
                'description' => 'Mã PIN của thẻ (12 ký tự).',
                'example' => '098765432109',
                'type' => 'string',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'serial.required' => 'Số seri là bắt buộc.',
            'serial.string' => 'Số seri phải là chuỗi ký tự.',
            'serial.size' => 'Số seri phải có đúng 15 ký tự.',
            'pin.required' => 'Mã PIN là bắt buộc.',
            'pin.string' => 'Mã PIN phải là chuỗi ký tự.',
            'pin.size' => 'Mã PIN phải có đúng 12 ký tự.',
        ];
    }
}
