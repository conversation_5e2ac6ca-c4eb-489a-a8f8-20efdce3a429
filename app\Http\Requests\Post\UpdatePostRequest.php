<?php

namespace App\Http\Requests\Post;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePostRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $postId = $this->route('id');

        return [
            'category_id' => ['sometimes', 'required', 'integer', 'exists:categories,id'],
            'title' => ['sometimes', 'required', 'string', 'max:255'],
            'slug' => ['sometimes', 'required', 'string', 'max:255', Rule::unique('posts', 'slug')->ignore($postId)],
            'excerpt' => ['nullable', 'string'],
            'body' => ['sometimes', 'required', 'string'],
            'cover_image' => ['nullable'],
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string'],
            'meta_keywords' => ['nullable', 'string'],
            'status' => ['sometimes', 'string', Rule::in(['draft', 'published', 'pending_review'])],
            'is_hot' => ['sometimes', 'boolean'],
            'show_on_homepage' => ['sometimes', 'boolean'],
            'published_at' => ['nullable', 'date'],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'category_id.required' => 'Danh mục là bắt buộc.',
            'category_id.exists' => 'Danh mục không tồn tại.',
            'title.required' => 'Tiêu đề là bắt buộc.',
            'slug.required' => 'Slug là bắt buộc.',
            'slug.unique' => 'Slug đã tồn tại.',
            'body.required' => 'Nội dung là bắt buộc.',
            'status.in' => 'Trạng thái không hợp lệ.',
        ];
    }

    /**
     * Defines the parameters for the request body.
     *
     * @return array
     */
    public function bodyParameters(): array
    {
        return [
            'category_id' => [
                'type' => 'integer',
                'description' => 'ID của danh mục bài viết.',
                'example' => 1,
            ],
            'title' => [
                'type' => 'string',
                'description' => 'Tiêu đề bài viết.',
                'example' => 'Bài viết mới về Laravel',
            ],
            'slug' => [
                'type' => 'string',
                'description' => 'Slug của bài viết (duy nhất).',
                'example' => 'bai-viet-moi-ve-laravel',
            ],
            'excerpt' => [
                'type' => 'string',
                'description' => 'Đoạn mô tả ngắn.',
                'example' => 'Đây là mô tả ngắn gọn về bài viết.',
            ],
            'body' => [
                'type' => 'string',
                'description' => 'Nội dung đầy đủ của bài viết.',
                'example' => 'Nội dung chi tiết của bài viết...',
            ],
            'cover_image' => [
                'type' => 'file|string',
                'description' => 'Ảnh bìa bài viết. Có thể nhập URL hoặc upload file ảnh (tối đa 5MB). Hỗ trợ các định dạng: jpg, jpeg, png, gif, webp. Nếu không thay đổi, hãy để trống.',
                'example' => 'file hoặc https://example.com/images/cover-image.jpg',
            ],
            'status' => [
                'type' => 'string',
                'description' => "Trạng thái bài viết: 'draft', 'published', 'pending_review'.",
                'example' => 'published',
            ],
            'is_hot' => [
                'type' => 'boolean',
                'description' => 'Đánh dấu bài viết là tin nổi bật.',
                'example' => true,
            ],
            'show_on_homepage' => [
                'type' => 'boolean',
                'description' => 'Hiển thị bài viết trên trang chủ.',
                'example' => false,
            ],
            'published_at' => [
                'type' => 'string',
                'description' => 'Ngày xuất bản (YYYY-MM-DD HH:MM:SS).',
                'example' => '2025-07-17 12:00:00',
            ],
            'meta_title' => [
                'type' => 'string',
                'description' => 'Tiêu đề SEO.',
                'example' => 'Tiêu đề SEO cho bài viết',
            ],
            'meta_description' => [
                'type' => 'string',
                'description' => 'Mô tả SEO.',
                'example' => 'Mô tả SEO chi tiết cho bài viết.',
            ],
            'meta_keywords' => [
                'type' => 'string',
                'description' => 'Các từ khóa SEO, cách nhau bằng dấu phẩy.',
                'example' => 'laravel, php, web development',
            ],
        ];
    }
}
