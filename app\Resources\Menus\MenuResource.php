<?php

namespace App\Resources\Menus;

use App\Resources\BaseResource;
use App\Resources\MenuItems\MenuItemResource;

class MenuResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    protected function resourceData($request): array
    {
        return [
            'menu_items' => MenuItemResource::collection($this->whenLoaded('menuItems')),
        ];
    }
}
