<?php

namespace App\Repositories\Eloquent;

use App\Enums\GamePackageStatus;
use App\Enums\GameStatus;
use App\Models\Game;
use App\Repositories\Interfaces\GameRepositoryInterface;

class GameRepository extends BaseRepository implements GameRepositoryInterface
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return Game::class;
    }

    /**
     * Get the first active game with its active packages.
     *
     * @return Game|null
     */
    public function getFirstActiveWithPackages(): ?Game
    {
        return $this->model
            ->where('status', GameStatus::ACTIVE->value)
            ->with(['packages' => function ($query) {
                $query->where('status', GamePackageStatus::ACTIVE->value)
                      ->orderBy('value', 'asc');
            }])
            ->first();
    }
}
