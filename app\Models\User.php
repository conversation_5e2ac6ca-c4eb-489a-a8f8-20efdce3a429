<?php

namespace App\Models;

use App\Enums\UserStatus;
use Database\Factories\UserFactory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Permission\Traits\HasRoles;
use Tymon\JWTAuth\Contracts\JWTSubject;

class User extends BaseAuthenticatable implements JWTSubject
{
    /** @use HasFactory<UserFactory> */
    use HasFactory;
    use Notifiable;
    use HasRoles;
    use SoftDeletes;

    use HasPermissions;

    /**
     * The guard name for the model.
     *
     * @var string
     */
    protected $guard_name = 'api';

    /**
     * Tên bảng liên kết với model.
     *
     * @var string
     */
    protected $table = 'users';

    /**
     * Kh<PERSON>a chính của bảng.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * <PERSON><PERSON>c thuộc tính có thể gán hàng loạt.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'avatar',
        'force_password_change',
        'email_verified_at',
        'last_login_at',
        'status',
    ];

    /**
     * Các thuộc tính nên được ẩn khi serialize.
     *
     * @var array<int, string>
     */
    protected $hidden = ['password', 'remember_token', 'force_password_change'];

    /**
     * Lấy các casts dành riêng cho model này.
     *
     * @return array
     */
    protected function getModelCasts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'force_password_change' => 'boolean',
            'status' => 'integer',
        ];
    }

    /**
     * Lấy định danh sẽ được lưu trữ trong JWT token.
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Trả về một mảng với các custom claims sẽ được thêm vào JWT token.
     */
    public function getJWTCustomClaims(): array
    {
        return [];
    }

    protected static function newFactory(): Factory|UserFactory
    {
        return UserFactory::new();
    }

    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'model_has_roles', 'model_id', 'role_id');
    }

    /**
     * Get status label
     *
     * @return string
     */
    public function getStatusLabelAttribute(): string
    {
        return UserStatus::from($this->status)->label();
    }

    /**
     * Get status enum
     *
     * @return UserStatus
     */
    public function getStatusEnumAttribute(): UserStatus
    {
        return UserStatus::from($this->status);
    }

    /**
     * Check if user is active
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return UserStatus::from($this->status)->isActive();
    }

    /**
     * Check if user is inactive
     *
     * @return bool
     */
    public function isInactive(): bool
    {
        return UserStatus::from($this->status)->isInactive();
    }

    /**
     * Check if user is suspended
     *
     * @return bool
     */
    public function isSuspended(): bool
    {
        return UserStatus::from($this->status)->isSuspended();
    }
}
