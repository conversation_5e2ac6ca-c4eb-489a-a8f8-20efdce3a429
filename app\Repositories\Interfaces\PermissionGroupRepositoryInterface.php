<?php

namespace App\Repositories\Interfaces;

use App\Models\PermissionGroup;
use Illuminate\Database\Eloquent\Collection;

interface PermissionGroupRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Lấy tất cả permission groups với children
     *
     * @return Collection
     */
    public function getAllWithChildren(): Collection;

    /**
     * Lấy permission group với children theo ID
     *
     * @param int $id
     * @return PermissionGroup|null
     */
    public function findWithChildren(int $id): ?PermissionGroup;

    /**
     * Lấy permission groups theo parent_id
     *
     * @param int|null $parentId
     * @return Collection
     */
    public function getByParentId(?int $parentId = null): Collection;
}
