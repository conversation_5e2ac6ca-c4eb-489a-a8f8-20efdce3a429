<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Post extends BaseModel
{
    use HasFactory;
    use SoftDeletes;

    protected $hidden = [
        'category_id',
        'created_by',
        'updated_by',
    ];

    protected $fillable = [
        'category_id',
        'title',
        'slug',
        'excerpt',
        'body',
        'cover_image',
        'status',
        'is_hot',
        'show_on_homepage',
        'published_at',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'is_hot' => 'boolean',
        'show_on_homepage' => 'boolean',
        'published_at' => 'datetime',
    ];

    /**
     * Quan hệ với bảng categories
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * <PERSON>uan hệ với bảng users (tác gi<PERSON>)
     */
    /**
     * <PERSON>uan hệ với bảng users (người tạo)
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Quan hệ với bảng users (người cập nhật)
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope để lọc bài viết đã xuất bản
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope để lọc bài viết nổi bật
     */
    public function scopeHot($query)
    {
        return $query->where('is_hot', true);
    }

    /**
     * Scope để lọc bài viết hiển thị trên homepage
     */
    public function scopeOnHomepage($query)
    {
        return $query->where('show_on_homepage', true);
    }
}
