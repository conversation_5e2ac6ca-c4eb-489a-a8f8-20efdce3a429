<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('Tên danh mục');
            $table->string('slug')->unique()->comment('URL thân thiện SEO');
            $table->foreignId('parent_id')->nullable()->constrained('categories')->cascadeOnDelete()->comment('<PERSON>h mục cha, hỗ trợ đa cấp');
            $table->text('description')->nullable()->comment('Mô tả danh mục');
            $table->tinyInteger('status')->default(1)->comment('Trạng thái: active: 1, inactive: 0');
            $table->boolean('is_featured')->default(false)->comment('Hiển thị trên trang chủ hay không');
            $table->integer('featured_order')->default(0)->comment('Sắ<PERSON> xếp thứ tự trong danh sách nổi bật');
            $table->integer('order')->default(0)->comment('Thứ tự hiển thị danh mục chung');
            $table->string('meta_title', 255)->nullable()->comment('Tiêu đề SEO hiển thị trên tab trình duyệt');
            $table->text('meta_description')->nullable()->comment('Đoạn mô tả ngắn cho kết quả tìm kiếm Google');
            $table->string('meta_image', 2048)->nullable()->comment('Ảnh đại diện chia sẻ lên mạng xã hội');
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete()->comment('Người tạo danh mục');
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete()->comment('Người cập nhật danh mục');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
