<?php

namespace App\Resources\Public\Categories;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource để hiển thị thông tin chi tiết của một danh mục cho người dùng cuối.
 */
class CategoryResource extends JsonResource
{
    /**
     * Dữ liệu đặc biệt cho resource (relationships, computed properties)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request = null): array
    {
        return [
            'name' => $this->resource->name,
            'slug' => $this->resource->slug,
            'description' => $this->resource->description,
        ];
    }
}
