<?php

namespace App\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class BaseResource
 *
 * Lớp cơ sở cho các API Resource, chuẩn hóa định dạng trả về cho REST API.
 *
 * Mục đích:
 * - <PERSON><PERSON><PERSON> bảo tất cả resource trả về theo chuẩn response của dự án (dùng macro success).
 * - Cho phép mở rộng, tuỳ biến thêm các field hoặc logic chung cho mọi resource.
 * - Hỗ trợ tùy chỉnh trường trả về và ẩn dữ liệu nhạy cảm.
 */
abstract class BaseResource extends JsonResource
{
    /**
     * Các trường cơ bản cho resource
     * Resource con có thể override để định nghĩa các trường mặc định
     */
    protected array $resourceFields = [];

    /**
     * Các trường nhạy cảm sẽ bị ẩn
     * Resource con có thể override để thêm các trường nhạy cảm
     */
    protected array $sensitiveFields = [];

    /**
     * Cung cấp mảng dữ liệu đặc thù của resource con.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    abstract protected function resourceData($request): array;

    /**
     * Chuyển resource thành mảng dữ liệu.
     *
     * Override từ JsonResource.
     * Hỗ trợ tùy chỉnh trường trả về và ẩn dữ liệu nhạy cảm.
     *
     * @param Request|null $request
     * @return array Mảng dữ liệu của resource.
     */
    public function toArray($request = null): array
    {
        // Lấy request hiện tại nếu không được truyền vào
        if ($request === null) {
            $request = request();
        }

        // Lấy mảng dữ liệu gốc từ model, bước này sẽ áp dụng `serializeDate`
        $baseData = $this->resource->toArray();

        // Lấy dữ liệu đặc thù từ resource con (ví dụ: các quan hệ whenLoaded)
        $resourceData = $this->resourceData($request);

        // Nếu có định nghĩa resourceFields, chỉ trả về các trường được chỉ định
        if (! empty($this->resourceFields)) {
            $filteredData = [];
            foreach ($this->resourceFields as $field) {
                if (isset($baseData[$field])) {
                    $filteredData[$field] = $baseData[$field];
                }
            }
            $baseData = $filteredData;
        }

        // Loại bỏ các trường nhạy cảm
        foreach ($this->sensitiveFields as $field) {
            unset($baseData[$field]);
        }

        // Hợp nhất hai mảng, dữ liệu từ resource con sẽ ghi đè lên dữ liệu gốc nếu có trùng key
        return array_merge($baseData, $resourceData);
    }

    /**
     * Lấy danh sách trường hiện tại
     *
     * @return array
     */
    public function getResourceFields(): array
    {
        return $this->resourceFields;
    }

    /**
     * Lấy danh sách trường nhạy cảm
     *
     * @return array
     */
    public function getSensitiveFields(): array
    {
        return $this->sensitiveFields;
    }
}
