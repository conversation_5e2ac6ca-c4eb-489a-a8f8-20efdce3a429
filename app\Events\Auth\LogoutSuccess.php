<?php

namespace App\Events\Auth;

use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Queue\SerializesModels;

class LogoutSuccess
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    /**
     * Người dùng đã đăng xuất.
     *
     * @var \App\Models\User
     */
    public User $user;

    /**
     * Request hiện tại.
     *
     * @var \Illuminate\Http\Request
     */
    public Request $request;

    /**
     * Tạo một instance sự kiện mới.
     *
     * @param \App\Models\User $user
     * @param \Illuminate\Http\Request $request
     * @return void
     */
    public function __construct(User $user, Request $request)
    {
        $this->user = $user;
        $this->request = $request;
    }
}
