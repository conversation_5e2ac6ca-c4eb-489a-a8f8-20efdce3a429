<?php

namespace Database\Seeders;

use App\Enums\GamePackageStatus;
use App\Enums\GameStatus;
use App\Models\Game;
use App\Models\GamePackage;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class GameSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        GamePackage::query()->delete();
        Game::query()->delete();

        $game = Game::query()->updateOrCreate(
            [
                'slug' => Str::slug('Kiếm hiệp tình'),
            ],
            [
                'name' => 'Kiếm hiệp tình',
                'thumb' => null,
                'status' => GameStatus::ACTIVE->value,
            ]
        );

        collect([
            ['name' => '20 Xu', 'value' => 20],
            ['name' => '50 Xu', 'value' => 50],
            ['name' => '100 Xu', 'value' => 100],
            ['name' => '200 Xu', 'value' => 200],
            ['name' => '500 Xu', 'value' => 500],
            ['name' => '1.000 Xu', 'value' => 1000],
        ])->each(fn ($pkg) => GamePackage::query()->updateOrCreate(
            [
                'name' => $pkg['name'],
                'game_id' => $game->id,
            ],
            [
                'value' => $pkg['value'],
                'status' => GamePackageStatus::ACTIVE->value,
            ]
        ));
    }
}
