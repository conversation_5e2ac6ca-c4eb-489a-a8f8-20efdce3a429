APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:8000
APP_ADMIN_URL=http://localhost:5173,http://localhost:5174

APP_LOCALE=vi
APP_FALLBACK_LOCALE=vi
APP_FAKER_LOCALE=vi_VN
APP_TIMEZONE=Asia/Ho_Chi_Minh

PHP_CLI_SERVER_WORKERS=2

BCRYPT_ROUNDS=12

LOG_CHANNEL=stderr
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Cấu hình kết nối Database cho Docker
# Tên các biến này phải khớp với environment trong docker-compose.yml
DB_CONNECTION=mysql
DB_HOST=db # Tên service của database trong docker-compose.yml
DB_PORT=3306
DB_DATABASE=laravel_db
DB_USERNAME=user
DB_PASSWORD=secret

CORS_ALLOWED_ORIGINS=*
HASH_DRIVER=bcrypt
SANCTUM_STATEFUL_DOMAINS=localhost
BROADCAST_DRIVER=log

QUEUE_CONNECTION=database

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
# REDIS_HOST=jx1-api-redis # Tên service của Redis trong docker-compose.yml
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=
PUSHER_SCHEME=https

JWT_TTL=90000
JWT_SECRET=

SCRIBE_BASIC_AUTH_USER=your_username
SCRIBE_BASIC_AUTH_PASS=your_strong_password
SCRIBE_AUTH_TOKEN=your_strong_token

PUBLIC_API_SECRET_KEY=

ANALYTICS_API_BASE_URL=http://localhost:3000
ANALYTICS_API_KEY="server to server secret key"
