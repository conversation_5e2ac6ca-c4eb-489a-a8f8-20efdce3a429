<?php

namespace App\Services;

use App\Repositories\Interfaces\StaticPageRepositoryInterface;
use Illuminate\Database\Eloquent\Model;

/**
 * Service xử lý các logic nghiệp vụ liên quan đến StaticPage
 */
class StaticPageService extends BaseCrudService
{
    /**
     * Xác định repository class sử dụng cho service này
     *
     * @return string
     */
    protected function getRepositoryClass(): string
    {
        return StaticPageRepositoryInterface::class;
    }

    /**
     * Lấy chi tiết trang tĩnh công khai theo slug.
     *
     * @param string $slug Slug của trang tĩnh.
     * @return Model
     * @throws ModelNotFoundException Nếu không tìm thấy trang tĩnh hoặc trang không active.
     */
    public function getPublicStaticPageBySlug(string $slug): Model
    {
        $staticPage = $this->repository->findByOrFail('slug', $slug);

        return $staticPage;
    }
}
