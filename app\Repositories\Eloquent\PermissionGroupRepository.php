<?php

namespace App\Repositories\Eloquent;

use App\Models\PermissionGroup;
use App\Repositories\Interfaces\PermissionGroupRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class PermissionGroupRepository extends BaseRepository implements PermissionGroupRepositoryInterface
{
    /**
     * @inheritDoc
     */
    protected function getModelClass(): string
    {
        return PermissionGroup::class;
    }

    /**
     * Lấy tất cả permission groups với children
     *
     * @return Collection
     */
    public function getAllWithChildren(): Collection
    {
        return $this->model->with('children')->whereNull('parent_id')->get();
    }

    /**
     * Lấy permission group với children theo ID
     *
     * @param int $id
     * @return PermissionGroup|null
     */
    public function findWithChildren(int $id): ?PermissionGroup
    {
        return $this->model->with('children')->find($id);
    }

    /**
     * Lấy permission groups theo parent_id
     *
     * @param int|null $parentId
     * @return Collection
     */
    public function getByParentId(?int $parentId = null): Collection
    {
        return $this->model->with('children')->where('parent_id', $parentId)->get();
    }
}
