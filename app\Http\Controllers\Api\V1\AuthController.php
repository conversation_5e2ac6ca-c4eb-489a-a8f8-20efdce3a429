<?php

namespace App\Http\Controllers\Api\V1;

use App\Events\Auth\LoginSuccess;
use App\Events\Auth\LogoutSuccess;
use App\Http\Requests\Auth\ChangePasswordRequest;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\UpdateProfileRequest;
use App\Models\User;
use App\Services\AuthService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

/**
 * @group Xác thực
 * Controller xử lý các request liên quan đến xác thực
 */
class AuthController extends BaseController
{
    /**
     * Constructor với Dependency Injection
     *
     * @param AuthService $authService Service xử lý business logic cho auth
     */
    public function __construct(protected AuthService $authService)
    {
    }

    /**
     * Xử lý request đăng nhập
     *
     * @param LoginRequest $request Request đã được validate qua FormRequest
     * @return JsonResponse Response JSON với token hoặc error
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $result = $this->authService->login($request->only('email', 'password'), $request->ip());

        if (! $result['success']) {
            return $this->error(
                $result['message'],
                (int) $result['code'],
                isset($result['error_code']) ? ['error_code' => $result['error_code']] : [],
                $result['meta'] ?? []
            );
        }

        if (isset($result['data'])) {
            $user = Auth::user();
            event(new LoginSuccess($user, $request));
        }


        return $this->success($result['data']);
    }

    /**
     * Xử lý request đăng xuất
     *
     * @param Request $request
     * @return JsonResponse Response JSON với success message hoặc error
     */
    public function logout(Request $request): JsonResponse
    {
        /** @var User|null $user */
        $user = auth('api')->user();

        $result = $this->authService->logout();

        if (! $result['success']) {
            return $this->error(
                $result['message'],
                (int) $result['code'],
                isset($result['error_code']) ? ['error_code' => $result['error_code']] : []
            );
        }

        // Kích hoạt sự kiện đăng xuất thành công để ghi log
        if ($user) {
            event(new LogoutSuccess($user, $request));
        }

        return $this->success([], $result['message']);
    }

    /**
     * Lấy thông tin user đang đăng nhập
     *
     * @return JsonResponse
     */
    public function me(): JsonResponse
    {
        $result = $this->authService->getUserInfo();

        if (! $result['success']) {
            return $this->error($result['message'], $result['code']);
        }

        return $this->success($result['data']);
    }

    /**
     * Cập nhật thông tin cá nhân
     */
    public function updateProfile(UpdateProfileRequest $request): JsonResponse
    {
        $result = $this->authService->updateProfile(auth('api')->id(), $request->validated());

        if (! $result['success']) {
            return $this->error($result['message'], $result['code'] ?? 400);
        }

        return $this->success($result['data'], $result['message'] ?? 'Cập nhật thành công');
    }

    /**
     * Đổi mật khẩu
     */
    public function changePassword(ChangePasswordRequest $request): JsonResponse
    {
        $result = $this->authService->changePassword(auth('api')->id(), $request->validated());

        if (! $result['success']) {
            return $this->error($result['message'], $result['code'] ?? 400);
        }

        return $this->success($result['data'], $result['message'] ?? 'Đổi mật khẩu thành công');
    }
}
