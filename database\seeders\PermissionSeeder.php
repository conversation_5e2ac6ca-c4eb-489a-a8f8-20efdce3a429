<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\PermissionGroup;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PermissionSeeder extends Seeder
{
    /**
     * Cấu hình permission groups
     */
    protected array $groups = [
        [
            'code' => 'overview',
            'name' => 'Tổng quan',
            'children' => [],
        ],
        [
            'code' => 'system_management',
            'name' => 'Quản lý hệ thống',
            'children' => [
                ['code' => 'user_management', 'name' => 'Quản lý người dùng'],
                ['code' => 'role_management', 'name' => 'Quản lý vai trò'],
                ['code' => 'permission_management', 'name' => 'Quản lý quyền hạn'],
                ['code' => 'log_management', 'name' => 'Quản lý nhật ký'],
            ],
        ],
        [
            'code' => 'cms_management',
            'name' => 'Quản lý module CMS',
            'children' => [
                ['code' => 'menu_management', 'name' => 'Quản lý menu'],
                ['code' => 'menu_item_management', 'name' => 'Quản lý menu item'],
                ['code' => 'category_management', 'name' => 'Quản lý danh mục'],
                ['code' => 'post_management', 'name' => 'Quản lý tin tức'],
                ['code' => 'static_page_management', 'name' => 'Quản lý trang tĩnh'],
                ['code' => 'gallery_group_management', 'name' => 'Quản lý nhóm thư viện ảnh' ],
                ['code' => 'gallery_item_management', 'name' => 'Quản lý ảnh'],
            ],
        ],
        [
            'code' => 'reports_management',
            'name' => 'Báo cáo thống kê',
            'children' => [],
        ],
        [
            'code' => 'site_setting_group_management',
            'name' => 'Quản lý nhóm cài đặt',
            'children' => [
                ['code' => 'site_setting_management', 'name' => 'Quản lý cài đặt'],
            ],
        ],
        [
            'code' => 'card_print_management',
            'name' => 'Quản lý in thẻ',
            'children' => [],
        ],
        [
            'code' => 'user_log_management',
            'name' => 'Quản lý nhật ký hoạt động người dùng',
            'children' => [],
        ],
    ];

    /**
     * Cấu hình permissions
     */
    protected array $permissions = [
        // Tổng quan
        ['name' => 'dashboard.view', 'label' => 'Xem dashboard tổng quan', 'guard_name' => 'api', 'group_code' => 'overview'],
        // Quản lý người dùng
        ['name' => 'user_management.view', 'label' => 'Xem danh sách người dùng', 'guard_name' => 'api', 'group_code' => 'user_management'],
        ['name' => 'user_management.create', 'label' => 'Tạo người dùng mới', 'guard_name' => 'api', 'group_code' => 'user_management'],
        ['name' => 'user_management.edit', 'label' => 'Chỉnh sửa người dùng', 'guard_name' => 'api', 'group_code' => 'user_management'],
        ['name' => 'user_management.delete', 'label' => 'Xóa người dùng', 'guard_name' => 'api', 'group_code' => 'user_management'],
        // Quản lý vai trò
        ['name' => 'role_management.view', 'label' => 'Xem danh sách vai trò', 'guard_name' => 'api', 'group_code' => 'role_management'],
        ['name' => 'role_management.create', 'label' => 'Tạo vai trò mới', 'guard_name' => 'api', 'group_code' => 'role_management'],
        ['name' => 'role_management.edit', 'label' => 'Chỉnh sửa vai trò', 'guard_name' => 'api', 'group_code' => 'role_management'],
        ['name' => 'role_management.delete', 'label' => 'Xóa vai trò', 'guard_name' => 'api', 'group_code' => 'role_management'],
        // Quản lý quyền hạn
        ['name' => 'permission_management.view', 'label' => 'Xem danh sách quyền hạn', 'guard_name' => 'api', 'group_code' => 'permission_management'],
        ['name' => 'permission_management.edit', 'label' => 'Gán quyền hạn', 'guard_name' => 'api', 'group_code' => 'permission_management'],
        // Quản lý nhật ký
        ['name' => 'log_management.view', 'label' => 'Xem nhật ký hệ thống', 'guard_name' => 'api', 'group_code' => 'log_management'],
        ['name' => 'log_management.export', 'label' => 'Xuất nhật ký', 'guard_name' => 'api', 'group_code' => 'log_management'],
        // Quản lý menu
        ['name' => 'menu_management.view', 'label' => 'Xem danh sách menu', 'guard_name' => 'api', 'group_code' => 'menu_management'],
        ['name' => 'menu_management.create', 'label' => 'Tạo menu mới', 'guard_name' => 'api', 'group_code' => 'menu_management'],
        ['name' => 'menu_management.edit', 'label' => 'Chỉnh sửa menu', 'guard_name' => 'api', 'group_code' => 'menu_management'],
        ['name' => 'menu_management.delete', 'label' => 'Xóa menu', 'guard_name' => 'api', 'group_code' => 'menu_management'],
        // Quản lý menu item
        ['name' => 'menu_item_management.view', 'label' => 'Xem danh sách menu item', 'guard_name' => 'api', 'group_code' => 'menu_item_management'],
        ['name' => 'menu_item_management.create', 'label' => 'Tạo menu item mới', 'guard_name' => 'api', 'group_code' => 'menu_item_management'],
        ['name' => 'menu_item_management.edit', 'label' => 'Chỉnh sửa menu item', 'guard_name' => 'api', 'group_code' => 'menu_item_management'],
        ['name' => 'menu_item_management.delete', 'label' => 'Xóa menu item', 'guard_name' => 'api', 'group_code' => 'menu_item_management'],
        // Quản lý danh mục
        ['name' => 'category_management.view', 'label' => 'Xem danh sách danh mục', 'guard_name' => 'api', 'group_code' => 'category_management'],
        ['name' => 'category_management.create', 'label' => 'Tạo danh mục mới', 'guard_name' => 'api', 'group_code' => 'category_management'],
        ['name' => 'category_management.edit', 'label' => 'Chỉnh sửa danh mục', 'guard_name' => 'api', 'group_code' => 'category_management'],
        ['name' => 'category_management.delete', 'label' => 'Xóa danh mục', 'guard_name' => 'api', 'group_code' => 'category_management'],
        // Quản lý tin tức
        ['name' => 'post_management.view', 'label' => 'Xem danh sách tin tức', 'guard_name' => 'api', 'group_code' => 'post_management'],
        ['name' => 'post_management.create', 'label' => 'Tạo tin tức mới', 'guard_name' => 'api', 'group_code' => 'post_management'],
        ['name' => 'post_management.edit', 'label' => 'Chỉnh sửa tin tức', 'guard_name' => 'api', 'group_code' => 'post_management'],
        ['name' => 'post_management.delete', 'label' => 'Xóa tin tức', 'guard_name' => 'api', 'group_code' => 'post_management'],
        // Gallery Group Management
        ['name' => 'gallery_group_management.view', 'label' => 'Xem danh sách nhóm thư viện ảnh', 'guard_name' => 'api', 'group_code' => 'gallery_group_management'],
        ['name' => 'gallery_group_management.create', 'label' => 'Tạo nhóm thư viện ảnh', 'guard_name' => 'api', 'group_code' => 'gallery_group_management'],
        ['name' => 'gallery_group_management.edit', 'label' => 'Chỉnh sửa nhóm thư viện ảnh', 'guard_name' => 'api', 'group_code' => 'gallery_group_management'],
        ['name' => 'gallery_group_management.delete', 'label' => 'Xóa nhóm thư viện ảnh', 'guard_name' => 'api', 'group_code' => 'gallery_group_management'],
        // Gallery Item Management
        ['name' => 'gallery_item_management.view', 'label' => 'Xem danh sách ảnh', 'guard_name' => 'api', 'group_code' => 'gallery_item_management'],
        ['name' => 'gallery_item_management.create', 'label' => 'Tạo ảnh mới', 'guard_name' => 'api', 'group_code' => 'gallery_item_management'],
        ['name' => 'gallery_item_management.edit', 'label' => 'Chỉnh sửa ảnh', 'guard_name' => 'api', 'group_code' => 'gallery_item_management'],
        ['name' => 'gallery_item_management.delete', 'label' => 'Xóa ảnh', 'guard_name' => 'api', 'group_code' => 'gallery_item_management'],
        // Site Setting Group Management
        ['name' => 'site_setting_group_management.view', 'label' => 'Xem danh sách nhóm cài đặt', 'guard_name' => 'api', 'group_code' => 'site_setting_group_management'],
        ['name' => 'site_setting_group_management.create', 'label' => 'Tạo nhóm cài đặt', 'guard_name' => 'api', 'group_code' => 'site_setting_group_management'],
        ['name' => 'site_setting_group_management.edit', 'label' => 'Chỉnh sửa nhóm cài đặt', 'guard_name' => 'api', 'group_code' => 'site_setting_group_management'],
        ['name' => 'site_setting_group_management.delete', 'label' => 'Xóa nhóm cài đặt', 'guard_name' => 'api', 'group_code' => 'site_setting_group_management'],
        // Site Setting Management
        ['name' => 'site_setting_management.view', 'label' => 'Xem danh sách cài đặt', 'guard_name' => 'api', 'group_code' => 'site_setting_management'],
        ['name' => 'site_setting_management.create', 'label' => 'Tạo cài đặt mới', 'guard_name' => 'api', 'group_code' => 'site_setting_management'],
        ['name' => 'site_setting_management.edit', 'label' => 'Chỉnh sửa cài đặt', 'guard_name' => 'api', 'group_code' => 'site_setting_management'],
        ['name' => 'site_setting_management.delete', 'label' => 'Xóa cài đặt', 'guard_name' => 'api', 'group_code' => 'site_setting_management'],
        // Static Page Management
        ['name' => 'static_page_management.view', 'label' => 'Xem danh sách trang tĩnh', 'guard_name' => 'api', 'group_code' => 'static_page_management'],
        ['name' => 'static_page_management.create', 'label' => 'Tạo trang tĩnh mới', 'guard_name' => 'api', 'group_code' => 'static_page_management'],
        ['name' => 'static_page_management.edit', 'label' => 'Chỉnh sửa trang tĩnh', 'guard_name' => 'api', 'group_code' => 'static_page_management'],
        ['name' => 'static_page_management.delete', 'label' => 'Xóa trang tĩnh', 'guard_name' => 'api', 'group_code' => 'static_page_management'],
        // Báo cáo thống kê
        ['name' => 'reports_management.view', 'label' => 'Xem báo cáo thống kê', 'guard_name' => 'api', 'group_code' => 'reports_management'],
        // In thẻ in
        ['name' => 'card_print_management.view', 'label' => 'Xem danh sách thẻ in', 'guard_name' => 'api', 'group_code' => 'card_print_management'],
        ['name' => 'card_print_management.create', 'label' => 'Tạo thẻ in', 'guard_name' => 'api', 'group_code' => 'card_print_management'],
        ['name' => 'card_print_management.edit', 'label' => 'Cập nhật thẻ in', 'guard_name' => 'api', 'group_code' => 'card_print_management'],
        ['name' => 'card_print_management.delete', 'label' => 'Hủy thẻ in', 'guard_name' => 'api', 'group_code' => 'card_print_management'],
        // Quản lý nhật ký hoạt động người dùng
        ['name' => 'user_log_management.view', 'label' => 'Xem nhật ký hoạt động người dùng', 'guard_name' => 'api', 'group_code' => 'user_log_management'],
        ['name' => 'user_log_management.export', 'label' => 'Xuất nhật ký hoạt động người dùng', 'guard_name' => 'api', 'group_code' => 'user_log_management'],
        ['name' => 'user_log_management.delete', 'label' => 'Xóa nhật ký hoạt động người dùng', 'guard_name' => 'api', 'group_code' => 'user_log_management'],
    ];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🔐 Bắt đầu tạo permissions và phân quyền...');
        DB::transaction(function () {
            $this->createPermissionGroups();
            $this->createPermissions();
            $this->assignPermissionsToSuperAdmin();
        });
        $this->command->info('✅ Permissions và phân quyền đã được tạo thành công!');
    }

    /**
     * Tạo permission groups
     */
    private function createPermissionGroups(): void
    {
        $allCodes = [];
        $count = 0;
        foreach ($this->groups as $group) {
            $parent = $this->upsertGroup($group['code'], $group['name']);
            $allCodes[] = $group['code'];
            $count++;
            if (! empty($group['children'])) {
                foreach ($group['children'] as $child) {
                    $this->upsertGroup($child['code'], $child['name'], $parent->id);
                    $allCodes[] = $child['code'];
                    $count++;
                }
            }
        }
        $deleted = PermissionGroup::query()->whereNotIn('code', $allCodes)->delete();
        if ($deleted > 0) {
            $this->command->warn("🗑️ Đã xóa $deleted permission group không còn trong cấu hình!");
        }
        $this->command->info('📁 Đã tạo ' . $count . ' permission groups (có phân cấp cha-con)');
    }

    private function upsertGroup(string $code, string $name, $parentId = null)
    {
        return PermissionGroup::query()->updateOrCreate(
            ['code' => $code],
            [
                'name' => $name,
                'parent_id' => $parentId,
            ]
        );
    }

    /**
     * Tạo permissions
     */
    private function createPermissions(): void
    {
        $allNames = [];

        foreach ($this->permissions as $permission) {
            Permission::query()->updateOrCreate(
                ['name' => $permission['name'], 'guard_name' => $permission['guard_name']],
                $permission
            );

            $allNames[] = $permission['name'];
        }

        $deleted = Permission::query()->whereNotIn('name', $allNames)->delete();

        if ($deleted > 0) {
            $this->command->warn("🗑️ Đã xóa $deleted permission không còn trong cấu hình!");
        }

        $this->command->info('🔐 Đã tạo ' . count($this->permissions) . ' permissions');
    }

    /**
     * Gán tất cả quyền cho Super Administrator
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $roleRepository = app(\App\Repositories\Eloquent\RoleRepository::class);
        $superAdminRole = $roleRepository->findBy('name', 'Super Administrator');

        if (! $superAdminRole) {
            $this->command->error('❌ Role Super Administrator không tồn tại! Hãy chạy SuperAdminSeeder trước.');

            return;
        }

        $permissions = Permission::all();
        $superAdminRole->syncPermissions($permissions);

        $this->command->info('👑 Đã gán ' . $permissions->count() . ' quyền cho Super Administrator');
        $this->command->info('');
        $this->command->info('🎯 Super Administrator hiện có toàn quyền trên hệ thống!');
    }
}
