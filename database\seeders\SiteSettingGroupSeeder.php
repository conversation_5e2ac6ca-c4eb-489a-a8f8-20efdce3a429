<?php

namespace Database\Seeders;

use App\Models\SiteSettingGroup;
use App\Models\User;
use Illuminate\Database\Seeder;

/**
 * Seeder cho bảng site_setting_groups
 */
class SiteSettingGroupSeeder extends Seeder
{
    /**
     * Tạo dữ liệu mẫu cho các nhóm cài đặt trang.
     */
    public function run(): void
    {
        $user = User::first();

        // Danh sách các nhóm cài đặt mẫu
        $groups = [
            [
                'name' => 'Thông tin chung',
                'group_key' => 'general_info',
                'description' => 'Các thông tin chung của website như tên, slogan, logo.',
            ],
            [
                'name' => 'Cấu hình Liên hệ',
                'group_key' => 'contact_config',
                'description' => 'Các thông tin liên hệ như địa chỉ, email, hotline.',
            ],
            [
                'name' => 'Cấu hình Mạng xã hội',
                'group_key' => 'social_media',
                'description' => '<PERSON> đến các trang mạng xã hội (Facebook, Zalo, Youtube...).',
            ],
            [
                'name' => 'Cấu hình SEO Mặc định',
                'group_key' => 'default_seo',
                'description' => 'Các thẻ meta SEO mặc định cho trang chủ và các trang khác.',
            ],
        ];

        // Tạo các nhóm cài đặt từ danh sách
        foreach ($groups as $group) {
            SiteSettingGroup::updateOrCreate(
                ['group_key' => $group['group_key']], // Điều kiện để tìm hoặc tạo
                array_merge($group, [
                    'created_by' => $user->id,
                    'updated_by' => $user->id,
                ])
            );
        }

        $this->command->info('✅ Các nhóm cài đặt (SiteSettingGroups) đã được tạo thành công!');
    }
}
