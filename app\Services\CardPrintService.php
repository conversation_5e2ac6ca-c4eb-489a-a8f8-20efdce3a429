<?php

namespace App\Services;

use App\Enums\CardPrintStatus;
use App\Repositories\Interfaces\CardPrintRepositoryInterface;
use App\Resources\CardPrint\CardPrintResourceCollection;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CardPrintService extends BaseService
{
    public function __construct(protected CardPrintRepositoryInterface $cardPrintRepository)
    {
    }

    public function getCardPrints(Request $request): array
    {
        try {
            $cardPrints = $this->cardPrintRepository->getCardPrints($request->all());
            $data = new CardPrintResourceCollection($cardPrints);

            return $this->success(
                $data->toArray($request),
                'Lấy danh sách thẻ thành công.'
            );
        } catch (Exception $e) {
            $this->logError('Lỗi khi lấy danh sách thẻ: ' . $e->getMessage());

            return $this->error('CARD_PRINT_LIST_ERROR', 500);
        }
    }

    public function createCardPrint(array $data): array
    {
        DB::beginTransaction();

        try {
            $createdCards = [];
            for ($i = 0; $i < $data['quantity']; $i++) {
                $createdCards[] = $this->cardPrintRepository->create([
                    'serial' => strtoupper(Str::random(15)),
                    'pin' => strtoupper(Str::random(12)),
                    'amount' => $data['amount'],
                    'note' => $data['note'] ?? null,
                    'status' => CardPrintStatus::PENDING->value,
                ]);
            }

            DB::commit();

            return $this->success(
                ['cards_created' => count($createdCards)],
                'Tạo thẻ thành công.',
                201
            );
        } catch (Exception $e) {
            DB::rollBack();
            $this->logError('Lỗi khi tạo thẻ: ' . $e->getMessage());

            return $this->error('CARD_PRINT_CREATE_ERROR', 500);
        }
    }

    public function cancelCard(int $id): array
    {
        try {
            $card = $this->cardPrintRepository->findOrFail($id);

            if ($card->status === CardPrintStatus::USED->value) {
                return $this->error('CARD_ALREADY_USED', 409);
            }

            $this->cardPrintRepository->update($id, ['status' => CardPrintStatus::CANCELED->value]);

            return $this->success([], 'Hủy thẻ thành công.', 204);
        } catch (Exception $e) {
            $this->logError('Lỗi khi hủy thẻ: ' . $e->getMessage());

            return $this->error('CARD_PRINT_DELETE_ERROR', 500);
        }
    }

    /**
     * Verify and use a card.
     *
     * @param array $data
     * @param string|null $ipAddress
     * @return array
     */
    public function verifyAndUseCard(array $data, ?string $ipAddress): array
    {
        try {
            $card = $this->cardPrintRepository->findBy('serial', $data['serial']);

            if (! $card) {
                return $this->error('CARD_NOT_FOUND', 404);
            }

            if ($card->pin !== $data['pin']) {
                return $this->error('INVALID_PIN', 400);
            }

            if ($card->status === CardPrintStatus::USED->value) {
                return $this->error('CARD_ALREADY_USED', 409);
            }

            if ($card->status === CardPrintStatus::CANCELED->value) {
                return $this->error('CARD_CANCELED', 410);
            }

            $this->cardPrintRepository->update($card->id, [
                'status' => CardPrintStatus::USED->value,
                'used_at' => now(),
                'used_by_ip' => $ipAddress,
            ]);

            return $this->success(['amount' => $card->amount], 'Sử dụng thẻ thành công.');
        } catch (Exception $e) {
            $this->logError('Lỗi khi xác thực thẻ: ' . $e->getMessage());

            return $this->error('CARD_VERIFY_ERROR', 500);
        }
    }
}
