<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Model cho nhật ký hoạt động của người dùng.
 */
class UserLog extends BaseModel
{
    use HasFactory;
    use SoftDeletes;

    /**
     * Tên bảng trong cơ sở dữ liệu.
     *
     * @var string
     */
    protected $table = 'user_logs';

    /**
     * Các thuộc tính có thể gán hàng loạt.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'action',
        'description',
        'data_before',
        'data_after',
        'details',
        'actor_id',
        'actor_type',
        'model_id',
        'model_type',
        'url',
        'ip',
        'user_agent',
        'action_time',
    ];

    /**
     * Các thuộc tính nên được cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'data_before' => 'array',
        'data_after' => 'array',
        'details' => 'array',
        'action_time' => 'datetime',
    ];

    /**
     * <PERSON>uan hệ với người dùng thực hiện hành động.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    /**
     * Quan hệ đa hình với người thực hiện hành động (có thể là user hoặc hệ thống).
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function actor(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Quan hệ đa hình với đối tượng bị tác động.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Ghi đè phương thức boot của trait HasUserStamps để vô hiệu hóa nó.
     * Bảng user_logs không có cột created_by và updated_by.
     *
     * @return void
     */
    protected static function bootHasUserStamps()
    {
        // Để trống để không tự động thêm user stamps.
    }
}
