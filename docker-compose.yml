# <PERSON><PERSON><PERSON> hình Docker Compose cho dự án Laravel

services:
  # Service cho ứng dụng Laravel (PHP-FPM)
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: jx1-api-app
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./:/var/www/html
    networks:
      - jx1-network

  # Service cho Nginx Web Server
  nginx:
    image: nginx:1.25-alpine
    container_name: jx1-api-nginx
    restart: unless-stopped
    ports:
      - "0.0.0.0:8000:80" # Cho phép truy cập từ mạng LAN
    volumes:
      - ./:/var/www/html
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - app
    networks:
      - jx1-network

  # Service cho Database MySQL
  db:
    image: mysql:8.0.35
    container_name: jx1-api-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_DATABASE:-laravel_db}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD:-root}
      MYSQL_PASSWORD: ${DB_PASSWORD:-secret}
      MYSQL_USER: ${DB_USERNAME:-user}
    ports:
      - "33066:3306"
    volumes:
      - db-data:/var/lib/mysql
      - ./docker/mysql/initdb.d:/docker-entrypoint-initdb.d
    networks:
      - jx1-network

  # Service cho Redis
  redis:
    image: redis:7-alpine
    container_name: jx1-api-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redisdata:/data
    networks:
      - jx1-network

# Khai báo network chung
networks:
  jx1-network:
    driver: bridge

# Khai báo volume để lưu trữ dữ liệu database
volumes:
  db-data:
    driver: local
  redisdata:
    driver: local
