<?php

namespace Database\Seeders;

use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\User;
use Illuminate\Database\Seeder;

class MenuSeeder extends Seeder
{
    /**
     * Tạo dữ liệu mẫu cho bảng menus và menu_items.
     */
    public function run(): void
    {
        $user = User::first();

        // 1. Tạo Menu chính
        $mainMenu = Menu::updateOrCreate(
            ['location_key' => 'main-menu'], // Điều kiện để tìm hoặc tạo
            [
                'name' => 'Main Menu',
                'location_key' => 'main-menu', // Lần thử cuối: Dùng 'location_key' theo quy ước của các bảng khác
                'created_by' => $user->id,
                'updated_by' => $user->id,
            ]
        );

        // 2. Tạo các mục menu (Menu Items) cho Main Menu
        $menuItems = [
            [
                'title' => 'Trang chủ',
                'slug' => 'trang-chu',
                'custom_url' => '/',
                'order' => 1,
                'target' => '_self', // Mở trong cùng tab
                'linkable_id' => null,
                'linkable_type' => null,
                'status' => 1,
            ],
            [
                'title' => 'Giới thiệu',
                'slug' => 'gioi-thieu',
                'custom_url' => '/gioi-thieu',
                'order' => 2,
                'target' => '_self',
                'linkable_id' => null,
                'linkable_type' => null,
                'status' => 1,
            ],
            [
                'title' => 'Tin tức & Sự kiện',
                'slug' => 'tin-tuc-su-kien',
                'custom_url' => '/tin-tuc-su-kien',
                'order' => 3,
                'target' => '_self',
                'linkable_id' => null,
                'linkable_type' => null,
                'status' => 1,
            ],
            [
                'title' => 'Hướng dẫn',
                'slug' => 'huong-dan',
                'custom_url' => '/huong-dan',
                'order' => 4,
                'target' => '_self',
                'linkable_id' => null,
                'linkable_type' => null,
                'status' => 1,
            ],
            [
                'title' => 'Liên hệ',
                'slug' => 'lien-he',
                'custom_url' => '/lien-he',
                'order' => 5,
                'target' => '_self',
                'linkable_id' => null,
                'linkable_type' => null,
                'status' => 1,
            ],
        ];

        foreach ($menuItems as $itemData) {
            MenuItem::updateOrCreate(
                [
                    'menu_id' => $mainMenu->id,
                    'slug' => $itemData['slug'],
                ],
                array_merge($itemData, [
                    'menu_id' => $mainMenu->id,
                    'created_by' => $user->id,
                    'updated_by' => $user->id,
                ])
            );
        }

        // Lấy mục "Tin tức & Sự kiện" vừa tạo để thêm menu con
        $newsMenuItem = MenuItem::where('title', 'Tin tức & Sự kiện')->first();

        if ($newsMenuItem) {
            $subMenuItems = [
                 [
                    'title' => 'Tin tức',
                    'slug' => 'tin-tuc',
                    'custom_url' => '/tin-tuc',
                    'order' => 1,
                    'target' => '_self',
                    'linkable_id' => null,
                    'linkable_type' => null,
                    'status' => 1,
                    'parent_id' => $newsMenuItem->id,
                ],
                [
                    'title' => 'Sự kiện',
                    'slug' => 'su-kien',
                    'custom_url' => '/su-kien',
                    'order' => 2,
                    'target' => '_self',
                    'linkable_id' => null,
                    'linkable_type' => null,
                    'status' => 1,
                    'parent_id' => $newsMenuItem->id,
                ],
            ];

            foreach ($subMenuItems as $itemData) {
                MenuItem::updateOrCreate(
                    [
                        'menu_id' => $mainMenu->id,
                        'slug' => $itemData['slug'],
                        'parent_id' => $newsMenuItem->id,
                    ],
                    array_merge($itemData, [
                        'menu_id' => $mainMenu->id,
                        'created_by' => $user->id,
                        'updated_by' => $user->id,
                    ])
                );
            }
        }

        $this->command->info('✅ Menus và MenuItems đã được tạo thành công!');
    }
}
