<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('posts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained('categories')->cascadeOnDelete()->comment('Danh mục bài viết');
            $table->string('title')->comment('Tiêu đề bài viết');
            $table->string('slug')->unique();
            $table->text('excerpt')->nullable()->comment('<PERSON><PERSON> tả ngắn, hiển thị ở danh sách');
            $table->longText('body')->comment('Nội dung bài viết');
            $table->string('cover_image')->nullable()->comment('Ảnh bài viết, đường dẫn');
            $table->string('status')->default('draft')->comment('Trạng thái: draft, published, pending_review');
            $table->boolean('is_hot')->default(false)->comment('Tin nổi bật');
            $table->boolean('show_on_homepage')->default(false)->comment('Hiển thị trên homepage');
            $table->timestamp('published_at')->nullable()->comment('Thời gian xuất bản');
            $table->string('meta_title', 255)->nullable()->comment('Tiêu đề SEO');
            $table->text('meta_description')->nullable()->comment('Mô tả SEO');
            $table->text('meta_keywords')->nullable()->comment('Từ khóa SEO');
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete()->comment('Người tạo');
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete()->comment('Người cập nhật');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('posts');
    }
};
