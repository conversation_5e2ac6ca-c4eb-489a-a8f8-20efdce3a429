<?php

namespace App\Listeners;

use App\Services\UserLogService;

/**
 * Lớp cơ sở cho các event listener cần ghi log
 *
 * @package App\Listeners
 */
abstract class BaseListener
{
    /**
     * Khởi tạo listener.
     *
     * @param \App\Services\UserLogService $userLogService
     */
    public function __construct(protected UserLogService $userLogService)
    {
    }

    /**
     * Ghi log nếu logging được bật
     *
     * @param string $action
     * @param string $description
     * @param mixed|null $model
     * @param array $metadata
     * @return void
     */
    protected function log(
        string $action,
        string $description,
        mixed $model = null,
        array $metadata = []
    ): void {
        if ($this->userLogService->isLoggingEnabled()) {
            $this->userLogService->log(
                action: $action,
                description: $description,
                model: $model,
                details: $metadata,
            );
        }
    }
}
