<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Log;

class AnalyticService extends BaseService
{
    public function __construct(protected ThirdPartyService $thirdPartyService)
    {
    }

    /**
     * Get overview statistics
     *
     * @return array
     * @throws Exception
     */
    public function getOverview(): array
    {
        try {
            // Gọi GET rõ ràng qua ThirdPartyService
            $response = $this->thirdPartyService->get('api/statistics/overview');

            return [
                'totalAccounts' => $response['totalAccounts'] ?? 0,
                'totalUniqueIPs' => $response['totalUniqueIPs'] ?? 0,
                'totalRevenue' => $response['totalRevenue'] ?? 0,
                'totalSuccessfulTransactions' => $response['totalSuccessfulTransactions'] ?? 0,
            ];
        } catch (Exception $e) {
            Log::error('Failed to get overview statistics: ' . $e->getMessage());

            throw $e;
        }
    }

    /**
     * Get revenue statistics
     *
     * @param string $startDate Start date (Y-m-d)
     * @param string $endDate End date (Y-m-d)
     * @return array
     * @throws Exception
     */
    public function getRevenueStatistics(string $startDate, string $endDate): array
    {
        try {
            // Gọi GET rõ ràng qua ThirdPartyService
            return $this->thirdPartyService->get(
                'api/statistics/revenue',
                [
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                ]
            );
        } catch (Exception $e) {
            Log::error('Failed to get revenue statistics: ' . $e->getMessage(), [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ]);

            throw $e;
        }
    }

    /**
     * Get overview data by date range
     *
     * @param int $type Overview type (1-4)
     * @param string|null $timePeriod Time period (daily, weekly, monthly, yearly)
     * @return array
     * @throws Exception
     */
    public function getOverviewByDate(int $type, ?string $timePeriod = null): array
    {
        try {
            // Gọi GET rõ ràng qua ThirdPartyService
            return $this->thirdPartyService->get(
                'api/statistics/overview/by-date',
                [
                    'type' => $type,
                    'time_period' => $timePeriod,
                ]
            );
        } catch (Exception $e) {
            Log::error('Failed to get overview by date: ' . $e->getMessage(), [
                'type' => $type,
                'time_period' => $timePeriod,
            ]);

            throw $e;
        }
    }

    /**
     * Get daily statistics
     *
     * @param string $startDate Start date (Y-m-d)
     * @param string $endDate End date (Y-m-d)
     * @param int|null $type Statistics type (1: accounts, 2: IPs)
     * @return array
     * @throws Exception
     */
    public function getDailyStatistics(string $startDate, string $endDate, ?int $type = null): array
    {
        try {
            // Gọi GET rõ ràng qua ThirdPartyService
            return $this->thirdPartyService->get(
                'api/statistics/daily',
                [
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'type' => $type,
                ]
            );
        } catch (Exception $e) {
            Log::error('Failed to get daily statistics: ' . $e->getMessage(), [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'type' => $type,
            ]);

            throw $e;
        }
    }
}
