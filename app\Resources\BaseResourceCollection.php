<?php

namespace App\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Lớ<PERSON> cơ sở cho các API Resource Collection
 *
 * Mục đích:
 * - Chu<PERSON>n hóa cấu trúc response cho collections
 * - Tự động thêm metadata và status
 * - Hỗ trợ phân trang chuẩn với đầy đủ thông tin cho frontend
 * - Tích hợp với macro success() cho response thống nhất
 *
 * Kế thừa lớp này để tạo resource collection tuỳ chỉnh
 */
abstract class BaseResourceCollection extends ResourceCollection
{
    /**
     * Chuyển collection thành mảng response chuẩn
     *
     * Cấu trúc response:
     * {
     *     "data": [...],
     *     "pagination": {
     *         "current_page": trang hiện tại,
     *         "per_page": số bản ghi mỗi trang,
     *         "total": tổng số bản ghi,
     *         "last_page": trang cuối cùng,
     *         "from": vị trí bản ghi đầu tiên,
     *         "last_item": vị trí bản ghi cuối cùng,
     *         "has_more_pages": có trang tiếp theo không
     *     }
     * }
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        $data = [
            'data' => $this->collection, // Danh sách các resource items
        ];

        // Nếu collection là một LengthAwarePaginator, thêm thông tin phân trang đầy đủ
        if ($this->resource instanceof LengthAwarePaginator) {
            $data['pagination'] = [
                'current_page' => $this->resource->currentPage(),
                'per_page' => $this->resource->perPage(),
                'total' => $this->resource->total(),
                'last_page' => $this->resource->lastPage(),
                'from' => $this->resource->firstItem(),
                'to' => $this->resource->lastItem(),
                'has_more_pages' => $this->resource->hasMorePages(),
            ];
        } else {
            // Nếu không phải LengthAwarePaginator, chỉ trả về tổng số bản ghi
            $data['pagination'] = [
                'total' => $this->collection->count(),
            ];
        }

        return $data;
    }

    /**
     * Thêm metadata mặc định cho mọi response
     *
     * @param \Illuminate\Http\Request $request
     * @return array<string, mixed>
     */
    public function with($request): array
    {
        return [
            'success' => true,
        ];
    }
}
