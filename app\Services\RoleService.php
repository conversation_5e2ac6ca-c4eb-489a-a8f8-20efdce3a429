<?php

namespace App\Services;

use App\Repositories\Eloquent\PermissionGroupRepository;
use App\Repositories\Eloquent\PermissionRepository;
use App\Repositories\Eloquent\RoleRepository;
use App\Repositories\Eloquent\UserRepository;
use Exception;
use Illuminate\Support\Facades\DB;

class RoleService extends BaseService
{
    public function __construct(
        protected RoleRepository              $roleRepository,
        protected PermissionRepository        $permissionRepository,
        protected UserRepository              $userRepository,
        protected PermissionGroupRepository   $permissionGroupRepository
    ) {
    }

    /**
     * <PERSON><PERSON>y tất cả roles
     */
    public function getAllRoles(array $params = [], array $selects = []): array
    {
        try {
            $roles = $this->roleRepository->getAllRoles($params, $selects);

            return $this->success(['roles' => $roles], '<PERSON><PERSON>y danh sách vai trò thành công');
        } catch (Exception $e) {
            $this->logError('Error getting all roles: ' . $e->getMessage());

            return $this->error('ROLE_LIST_ERROR', 500);
        }
    }

    /**
     * Tạo role mới
     */
    public function createRole(array $data): array
    {
        DB::beginTransaction();

        try {
            $data['guard_name'] = $data['guard_name'] ?? 'api';
            $data['is_protected'] = $data['is_protected'] ?? false;

            $role = $this->roleRepository->create($data);

            DB::commit();

            return $this->success(['role' => $role], 'Tạo vai trò thành công');
        } catch (Exception $e) {
            DB::rollBack();
            $this->logError('Error create role: ' . $e->getMessage(), ['data' => $data]);

            return $this->error('ROLE_CREATE_ERROR', 500);
        }
    }

    /**
     * Cập nhật role
     */
    public function updateRole(int $id, array $data): array
    {
        DB::beginTransaction();

        try {
            $role = $this->roleRepository->findOrFail($id);

            if ($role->is_protected) {
                return $this->error('PROTECTED_ROLE_UPDATE_ERROR', 403);
            }

            $role = $this->roleRepository->update($id, $data);
            DB::commit();

            return $this->success(['role' => $role], 'Cập nhật vai trò thành công');
        } catch (Exception $e) {
            DB::rollBack();
            $this->logError('Error update role: ' . $e->getMessage(), ['id' => $id, 'data' => $data]);

            return $this->error('ROLE_UPDATE_ERROR', 500);
        }
    }

    /**
     * Xóa role
     */
    public function deleteRole(int $id): array
    {
        DB::beginTransaction();

        try {
            $role = $this->roleRepository->findOrFail($id);

            if ($role->is_protected) {
                return $this->error('PROTECTED_ROLE_DELETE_ERROR', 403);
            }

            $this->roleRepository->deleteWithDetach($id);
            DB::commit();

            return $this->success([], 'Xóa vai trò thành công');
        } catch (Exception $e) {
            DB::rollBack();
            $this->logError('Error delete role: ' . $e->getMessage(), ['id' => $id]);

            return $this->error('ROLE_DELETE_ERROR', 500);
        }
    }

    /**
     * Lấy chi tiết role
     */
    public function getRoleDetail(int $id): array
    {
        try {
            $role = $this->roleRepository->findOrFail($id);
            $rolePermissionIds = $this->roleRepository->getPermissionIds($id);
            $permissionsByGroup = $this->permissionRepository->getPermissionsGroupedByCode();
            $allGroups = $this->permissionGroupRepository->getAllWithChildren();

            $permissionGroups = $this->buildGroupTree($allGroups, $permissionsByGroup, $rolePermissionIds);

            $result = [
                'role' => [
                    'id' => $role->id,
                    'name' => $role->name,
                    'is_protected' => $role->is_protected,
                    'created_at' => $role->created_at,
                ],
                'permission_groups' => $permissionGroups,
            ];

            return $this->success($result, 'Lấy chi tiết vai trò thành công');
        } catch (Exception $e) {
            $this->logError('Error get role detail: ' . $e->getMessage(), ['id' => $id]);

            return $this->error('ROLE_GET_DETAIL_ERROR', 500);
        }
    }

    /**
     * Gán quyền cho role
     */
    public function assignPermissionsToRole(int $roleId, array $permissionIds): array
    {
        DB::beginTransaction();

        try {
            $role = $this->roleRepository->findOrFail($roleId);

            if ($role->is_protected) {
                return $this->error('PROTECTED_ROLE_ASSIGN_ERROR', 403);
            }

            $this->roleRepository->assignPermissions($roleId, $permissionIds);
            $role = $this->roleRepository->findWithPermissions($roleId);

            DB::commit();

            return $this->success(['role' => $role], 'Gán quyền cho vai trò thành công');
        } catch (Exception $e) {
            DB::rollBack();
            $this->logError('Error assign permissions to role: ' . $e->getMessage(), [
                'role_id' => $roleId,
                'permission_ids' => $permissionIds,
            ]);

            return $this->error('ROLE_ASSIGN_PERMISSION_ERROR', 500);
        }
    }

    /**
     * Gỡ quyền khỏi role
     */
    public function revokePermissionsFromRole(int $roleId, array $permissionIds): array
    {
        DB::beginTransaction();

        try {
            $role = $this->roleRepository->findOrFail($roleId);

            if ($role->is_protected) {
                return $this->error('PROTECTED_ROLE_REVOKE_ERROR', 403);
            }

            $this->roleRepository->revokePermissions($roleId, $permissionIds);
            $role = $this->roleRepository->findWithPermissions($roleId);

            DB::commit();

            return $this->success(['role' => $role], 'Thu hồi quyền của vai trò thành công');
        } catch (Exception $e) {
            DB::rollBack();
            $this->logError('Error revoke permissions from role: ' . $e->getMessage(), [
                'role_id' => $roleId,
                'permission_ids' => $permissionIds,
            ]);

            return $this->error('ROLE_REVOKE_PERMISSION_ERROR', 500);
        }
    }

    /**
     * Gán role cho user
     */
    public function assignRoleToUser(int $userId, array $roleIds): array
    {
        DB::beginTransaction();

        try {
            $user = $this->userRepository->findOrFail($userId);
            $user->roles()->syncWithoutDetaching($roleIds);

            DB::commit();

            return $this->success([], 'Gán vai trò cho user thành công');
        } catch (Exception $e) {
            DB::rollBack();
            $this->logError('Error assign role to user: ' . $e->getMessage(), [
                'user_id' => $userId,
                'role_ids' => $roleIds,
            ]);

            return $this->error('USER_ASSIGN_ROLE_ERROR', 500);
        }
    }

    /**
     * Đệ quy build tree group + permission cho chi tiết role
     */
    private function buildGroupTree($groups, $permissionsByGroup, $rolePermissionIds): \Illuminate\Support\Collection
    {
        return $groups->map(function ($group) use ($permissionsByGroup, $rolePermissionIds) {
            $children = $group->children;
            $item = [
                'code' => $group->code,
                'name' => $group->name,
                'children' => $children->isNotEmpty() ? $this->buildGroupTree($children, $permissionsByGroup, $rolePermissionIds) : [],
                'permissions' => [],
            ];

            if (isset($permissionsByGroup[$group->code])) {
                $item['permissions'] = collect($permissionsByGroup[$group->code])
                    ->map(function ($permission) use ($rolePermissionIds) {
                        $parts = explode('.', $permission->name);
                        $type = $parts[1] ?? null;

                        return [
                            'id' => $permission->id,
                            'name' => $permission->name,
                            'label' => $permission->label,
                            'type' => $type,
                            'active' => in_array($permission->id, $rolePermissionIds),
                        ];
                    })
                    ->values();
            }

            return $item;
        });
    }

    /**
     * Lấy danh sách error messages
     */
    protected function getErrorMessage(string $errorCode, array $params = []): string
    {
        $messages = [
            'ROLE_NOT_FOUND' => 'Vai trò không tồn tại',
            'ROLE_CREATE_ERROR' => 'Không thể tạo vai trò. Vui lòng thử lại sau.',
            'ROLE_UPDATE_ERROR' => 'Không thể cập nhật vai trò. Vui lòng thử lại sau.',
            'ROLE_DELETE_ERROR' => 'Không thể xóa vai trò. Vui lòng thử lại sau.',
            'ROLE_GET_DETAIL_ERROR' => 'Không thể lấy chi tiết vai trò. Vui lòng thử lại sau.',
            'ROLE_LIST_ERROR' => 'Không thể lấy danh sách vai trò. Vui lòng thử lại sau.',
            'ROLE_ASSIGN_PERMISSION_ERROR' => 'Không thể gán quyền cho vai trò. Vui lòng thử lại sau.',
            'ROLE_REVOKE_PERMISSION_ERROR' => 'Không thể gỡ quyền khỏi vai trò. Vui lòng thử lại sau.',
            'USER_ASSIGN_ROLE_ERROR' => 'Không thể gán vai trò cho user. Vui lòng thử lại sau.',
            'PROTECTED_ROLE_ASSIGN_ERROR' => 'Không thể gán quyền cho vai trò được bảo vệ',
            'PROTECTED_ROLE_REVOKE_ERROR' => 'Không thể gỡ quyền khỏi vai trò được bảo vệ',
            'PROTECTED_ROLE_UPDATE_ERROR' => 'Không thể cập nhật vai trò được bảo vệ',
            'PROTECTED_ROLE_DELETE_ERROR' => 'Không thể xóa vai trò được bảo vệ',
        ];

        return $messages[$errorCode] ?? 'Có lỗi xảy ra. Vui lòng thử lại sau.';
    }
}
