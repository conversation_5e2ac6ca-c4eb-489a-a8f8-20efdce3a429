<?php

namespace Database\Seeders;

use App\Services\UserLogService;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Tắt UserLogService khi seeding
        $userLogService = app(UserLogService::class);
        $userLogService->disableLogging();

        // Gọi các Seeder hệ thống (quyền, user, nhóm cài đặt hệ thống...)
        $this->call([
            RoleSeeder::class,
            UserSeeder::class,
            PermissionSeeder::class,
        ]);

        $this->call([
            SiteSettingGroupSeeder::class, // Tạo dữ liệu mẫu cho SiteSettingGroup
            SiteSettingSeeder::class, // Tạo dữ liệu mẫu cho SiteSetting
            CategorySeeder::class,    // Tạo dữ liệu mẫu cho Category
            MenuSeeder::class,        // Tạo dữ liệu mẫu cho Menu & MenuItem
            PostSeeder::class,        // Tạo dữ liệu mẫu cho Post
            GallerySeeder::class,     // Tạo dữ liệu mẫu cho GalleryGroup & GalleryItem
            StaticPageSeeder::class,  // Tạo dữ liệu mẫu cho StaticPage
            GameSeeder::class,        // Tạo dữ liệu mẫu cho Game & GamePackage
        ]);

        // Bật lại UserLogService khi seeding
        $userLogService->enableLogging();

        $this->command->info('✅ Tất cả dữ liệu mẫu đã được tạo thành công!');
    }
}
