<?php

namespace App\Observers;

use App\Models\User;

/**
 * Observer xử lý các sự kiện liên quan đến User
 */
class UserObserver extends BaseObserver
{
    /**
     * X<PERSON> lý sự kiện "created" của User.
     *
     * @param \App\Models\User $user
     * @return void
     */
    public function created(User $user): void
    {
        $this->log(
            action: 'create',
            description: "Tạo mới người dùng '{$user->name}' (ID: {$user->id})",
            model: $user,
            dataAfter: $user->toArray()
        );
    }

    /**
     * Xử lý sự kiện "updated" của User.
     *
     * @param \App\Models\User $user
     * @return void
     */
    public function updated(User $user): void
    {
        $this->log(
            action: 'update',
            description: "Cập nhật người dùng '{$user->name}' (ID: {$user->id})",
            model: $user,
            dataBefore: $user->getOriginal(),
            dataAfter: $user->getChanges()
        );
    }

    /**
     * Xử lý sự kiện "deleting" của User.
     *
     * @param \App\Models\User $user
     * @return void
     */
    public function deleting(User $user): void
    {
        $this->log(
            action: 'delete',
            description: "Xóa người dùng '{$user->name}' (ID: {$user->id})",
            model: $user,
            dataBefore: $user->toArray()
        );
    }
}
