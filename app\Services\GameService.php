<?php

namespace App\Services;

use App\Repositories\Interfaces\GameRepositoryInterface;
use App\Resources\Public\Game\GameResource;
use Exception;

class GameService extends BaseService
{
    public function __construct(protected GameRepositoryInterface $gameRepository)
    {
    }

    /**
     * Get the first active game with its active packages.
     *
     * @return array
     */
    public function getFirstGameWithPackages(): array
    {
        try {
            $game = $this->gameRepository->getFirstActiveWithPackages();

            if (! $game) {
                return $this->error('GAME_NOT_FOUND', 404);
            }

            return $this->success(['game' => new GameResource($game)], 'Lấy thông tin game thành công.');
        } catch (Exception $e) {
            $this->logError('Lỗi khi lấy thông tin game: ' . $e->getMessage());

            return $this->error('GET_GAME_INFO_ERROR', 500);
        }
    }
}
