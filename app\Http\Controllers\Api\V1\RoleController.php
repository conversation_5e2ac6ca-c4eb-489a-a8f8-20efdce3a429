<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\Role\AssignPermissionRequest;
use App\Http\Requests\Role\DetailRoleRequest;
use App\Http\Requests\Role\StoreRoleRequest;
use App\Http\Requests\Role\UpdateRoleRequest;
use App\Resources\Role\RoleResource;
use App\Resources\Role\RoleResourceCollection;
use App\Services\RoleService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
* @group Vai trò - Quản lý vai trò
*/
class RoleController extends BaseController
{
    public function __construct(protected RoleService $roleService)
    {
    }

    /**
     * Lấy tất cả role
     */
    public function index(Request $request): JsonResponse
    {
        $result = $this->roleService->getAllRoles($request->all());

        if (! $result['success']) {
            return response()->json($result, $result['code'] ?? 400);
        }

        return response()->json([
            'success' => true,
            'message' => $result['message'],
            'data' => [
                'roles' => new RoleResourceCollection($result['data']['roles']),
            ],
        ]);
    }

    /**
     * Chi tiết role
     */
    public function show(DetailRoleRequest $request, int $id): JsonResponse
    {
        $result = $this->roleService->getRoleDetail($id);

        if (! $result['success']) {
            return response()->json($result, $result['code'] ?? 400);
        }

        return response()->json($result);
    }

    /**
     * Tạo mới role
     */
    public function store(StoreRoleRequest $request): JsonResponse
    {
        $result = $this->roleService->createRole($request->validated());

        if (! $result['success']) {
            return response()->json($result, $result['code'] ?? 400);
        }

        return response()->json(
            [
                'success' => true,
                'message' => 'Tạo vai trò thành công',
                'data' => [
                    'role' => new RoleResource($result['data']['role']),
                ],
            ],
            201,
        );
    }

    /**
     * Cập nhật role
     */
    public function update(UpdateRoleRequest $request, int $id): JsonResponse
    {
        $result = $this->roleService->updateRole($id, $request->validated());

        if (! $result['success']) {
            return response()->json($result, $result['code'] ?? 400);
        }

        return response()->json([
            'success' => true,
            'message' => 'Cập nhật vai trò thành công',
            'data' => [
                'role' => new RoleResource($result['data']['role']),
            ],
        ]);
    }

    /**
     * Xóa role
     */
    public function destroy(int $id): JsonResponse
    {
        $result = $this->roleService->deleteRole($id);

        if (! $result['success']) {
            return response()->json($result, $result['code'] ?? 400);
        }

        return response()->json([
            'success' => true,
            'message' => 'Xóa vai trò thành công',
            'data' => [],
        ]);
    }

    /**
     * Gán quyền cho role
     */
    public function assignPermissions(AssignPermissionRequest $request, int $id): JsonResponse
    {
        $result = $this->roleService->assignPermissionsToRole($id, $request->validated()['permission_ids']);

        if (! $result['success']) {
            return response()->json($result, $result['code'] ?? 400);
        }

        return response()->json([
            'success' => true,
            'message' => 'Gán quyền cho vai trò thành công',
            'data' => [
                'role' => new RoleResource($result['data']['role']),
            ],
        ]);
    }

    /**
     * Gỡ quyền khỏi role
     */
    public function revokePermissions(AssignPermissionRequest $request, int $id): JsonResponse
    {
        $result = $this->roleService->revokePermissionsFromRole($id, $request->validated()['permission_ids']);

        if (! $result['success']) {
            return response()->json($result, $result['code'] ?? 400);
        }

        return response()->json([
            'success' => true,
            'message' => 'Gỡ quyền khỏi vai trò thành công',
            'data' => [
                'role' => new RoleResource($result['data']['role']),
            ],
        ]);
    }

    /**
     * Lấy toàn bộ danh sách roles (id, name)
     * GET /api/v1/data/all-roles
     */
    public function allRoles(): JsonResponse
    {
        $result = $this->roleService->getAllRoles([], ['id', 'name']);

        if (! $result['success']) {
            return response()->json($result, $result['code'] ?? 400);
        }

        return response()->json([
            'success' => true,
            'message' => $result['message'],
            'data' => [
                'roles' => new RoleResourceCollection($result['data']['roles']),
            ],
        ]);
    }
}
