<?php

namespace App\Resources\Public\GalleryGroups;

use App\Resources\Public\GalleryItems\GalleryItemResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource để hiển thị thông tin của một gallery group cho người dùng cuối.
 */
class GalleryGroupResource extends JsonResource
{
    /**
     * Dữ liệu đặc biệt cho resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'name' => $this->resource->name,
            'location_key' => $this->resource->location_key,
            'description' => $this->resource->description,
            'items' => new GalleryItemResourceCollection($this->whenLoaded('galleryItems')),
        ];
    }
}
