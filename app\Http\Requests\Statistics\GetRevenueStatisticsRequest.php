<?php

namespace App\Http\Requests\Statistics;

use App\Http\Requests\BaseRequest;

class GetRevenueStatisticsRequest extends BaseRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'start_date' => 'required|date|date_format:Y-m-d',
            'end_date' => 'required|date|date_format:Y-m-d|after_or_equal:start_date',
        ];
    }

    public function messages(): array
    {
        return [
            'start_date.required' => '<PERSON><PERSON>y bắt đầu là bắt buộc',
            'start_date.date' => 'Ngày bắt đầu phải là ngày hợp lệ',
            'start_date.date_format' => '<PERSON><PERSON>y bắt đầu phải có định dạng Y-m-d',
            'end_date.required' => '<PERSON><PERSON>y kết thúc là bắt buộc',
            'end_date.date' => '<PERSON><PERSON><PERSON> kết thúc phải là ngày hợp lệ',
            'end_date.date_format' => '<PERSON><PERSON>y kết thúc phải có định dạng Y-m-d',
            'end_date.after_or_equal' => '<PERSON><PERSON>y kết thúc phải sau hoặc bằng ngày bắt đầu',
        ];
    }

    /**
     * Mô tả các tham số query cho Scribe.
     *
     * Ghi chú:
     * - Endpoint sử dụng GET nên dùng queryParameters().
     * - start_date, end_date yêu cầu định dạng Y-m-d và end_date >= start_date.
     *
     * @return array<string, array<string, mixed>>
     */
    public function queryParameters(): array
    {
        return [
            'start_date' => [
                'description' => 'Ngày bắt đầu theo định dạng Y-m-d. Ví dụ: 2024-01-01',
                'example' => '2024-01-01',
                'required' => true,
            ],
            'end_date' => [
                'description' => 'Ngày kết thúc theo định dạng Y-m-d và phải >= start_date. Ví dụ: 2024-01-30',
                'example' => '2024-01-30',
                'required' => true,
            ],
        ];
    }
}
