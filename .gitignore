# Files and directories ignored by Git

# Dependencies
/node_modules
/vendor

# Laravel framework files
/public/hot
/public/storage
/storage/*.key
/storage/app/public
/storage/framework/cache/
/storage/framework/sessions/
/storage/framework/testing/
/storage/framework/views/
/storage/logs/*.log

# Environment files
/.env
/.env.backup
/.env.production
/.env.staging
/.env.testing

# IDE helper files
/_ide_helper.php
/_ide_helper_models.php
/.phpstorm.meta.php

# Testing
/.phpunit.result.cache

# Build artifacts
/public/build

# Other
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log

# Scribe documentation generated files
.scribe/
/public/vendor/scribe

# Docker
docker/mysql/initdb.d/*

# Ignore Scribe-generated endpoints and cache
.scribe/endpoints/
!.scribe/endpoints/custom*.yaml
.scribe/endpoints.cache/
.scribe/.filehashes

# Ignore generated API docs (Markdown hoặc HTML)
resources/views/scribe/*.md
resources/views/scribe/*.blade.php
public/docs/

# PHP CS Fixer
.php-cs-fixer.cache

# IDE
.idea

# MacOS
.DS_Store

# vhost
.htaccess
.user.ini
.well-known/

# Ignore GitHub Actions workflows
.github/
