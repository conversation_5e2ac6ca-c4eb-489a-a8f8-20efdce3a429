<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

/**
 * Base class cho tất cả Service trong hệ thống
 *
 * - Cung cấp các phương thức xử lý logic nghiệp vụ chung
 * - Chuẩn hóa format response thành công/thất bại
 * - Hỗ trợ logging lỗi và thông tin debug
 * - Cho phép kế thừa và mở rộng trong các service con
 */
abstract class BaseService
{
    /**
     * Tạo response thành công (chuẩn hóa theo format của hệ thống)
     *
     * @param array $data Dữ liệu trả về (mặc định là mảng rỗng)
     * @param string|null $message Thông điệp kèm theo (tùy chọn)
     * @return array Mảng response định dạng chuẩn
     */
    protected function success(array $data = [], ?string $message = null): array
    {
        $response = [
            'success' => true,
            'data' => $data,
        ];

        if ($message !== null) {
            $response['message'] = $message;
        }

        return $response;
    }

    /**
     * Tạo response thất bại (chuẩn hóa theo format của hệ thống)
     *
     * @param string $errorCode Mã lỗi logic trong hệ thống (ví dụ: USER_NOT_FOUND)
     * @param int $httpCode Mã HTTP tương ứng (ví dụ: 404, 400...)
     * @param array $meta Thông tin bổ sung (tùy chọn)
     * @return array Mảng response định dạng lỗi chuẩn
     */
    protected function error(string $errorCode, int $httpCode = 400, array $meta = []): array
    {
        return [
            'success' => false,
            'error_code' => $errorCode,
            'message' => $this->getErrorMessage($errorCode, $meta),
            'code' => $httpCode,
            'meta' => $meta,
        ];
    }

    /**
     * Lấy thông báo lỗi dựa trên mã lỗi.
     *
     * Các service con có thể override phương thức này để cung cấp các thông báo lỗi tùy chỉnh.
     * Nếu không, nó sẽ trả về chính mã lỗi đó.
     *
     * @param string $errorCode Mã lỗi.
     * @param array $params Các tham số để thay thế trong thông báo.
     * @return string Thông báo lỗi.
     */
    protected function getErrorMessage(string $errorCode, array $params = []): string
    {
        // Mặc định trả về chính mã lỗi nếu không tìm thấy thông báo cụ thể.
        // Các service con nên override lại phương thức này.
        $message = config("error_messages.{$errorCode}") ?? $errorCode;

        return $this->interpolateMessage($message, $params);
    }

    /**
     * Thay thế các placeholder trong chuỗi thông báo bằng các giá trị thực tế.
     *
     * Ví dụ: 'Tài khoản tạm khóa. Thử lại sau :minutes phút' với params ['minutes' => 15]
     * sẽ trở thành 'Tài khoản tạm khóa. Thử lại sau 15 phút'.
     *
     * @param string $message Chuỗi thông báo chứa placeholder (ví dụ: :key).
     * @param array $params Mảng kết hợp của placeholder và giá trị.
     * @return string Chuỗi thông báo đã được thay thế.
     */
    protected function interpolateMessage(string $message, array $params = []): string
    {
        foreach ($params as $key => $val) {
            $message = str_replace(':' . $key, $val, $message);
        }

        return $message;
    }

    /**
     * Kiểm tra kết quả trả về có thành công hay không
     *
     * @param array $result Response được trả về từ service
     * @return bool true nếu thành công, ngược lại false
     */
    protected function isSuccess(array $result): bool
    {
        return $result['success'] ?? false;
    }

    /**
     * Ghi log lỗi hệ thống trong service
     *
     * Sử dụng để ghi lại các exception hoặc lỗi logic có thể xảy ra
     *
     * @param string $message Nội dung lỗi
     * @param array $context Dữ liệu đính kèm để dễ truy vết (ví dụ: user_id, input data...)
     * @return void
     */
    protected function logError(string $message, array $context = []): void
    {
        Log::error('[' . static::class . '] ' . $message, $context);
    }

    /**
     * Ghi log thông tin (info-level) trong service
     *
     * Sử dụng để log các hành động quan trọng, phục vụ mục đích debug
     *
     * @param string $message Nội dung log
     * @param array $context Dữ liệu đính kèm thêm (tùy chọn)
     * @return void
     */
    protected function logInfo(string $message, array $context = []): void
    {
        Log::info('[' . static::class . '] ' . $message, $context);
    }
}
