<?php

namespace App\Services;

use App\Repositories\Interfaces\GalleryItemRepositoryInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;

/**
 * Service xử lý các logic liên quan đến GalleryItem
 */
class GalleryItemService extends BaseCrudService
{
    /**
     * Service xử lý upload file
     *
     * @var FileUploadService
     */
    protected FileUploadService $fileUploadService;

    /**
     * Khởi tạo service
     */
    public function __construct(FileUploadService $fileUploadService)
    {
        parent::__construct();
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Trả về tên lớp Repository
     *
     * @return string
     */
    protected function getRepositoryClass(): string
    {
        return GalleryItemRepositoryInterface::class;
    }

    /**
     * Tạo gallery item mới
     * Hỗ trợ upload file hoặc sử dụng URL có sẵn
     *
     * @param array $data Dữ liệu đầu vào
     * @param array $with <PERSON><PERSON><PERSON> quan hệ cần eager load
     * @return Model
     */
    public function create(array $data, array $with = []): Model
    {
        // Xử lý image_url nếu là file upload
        if (isset($data['image_url']) && $data['image_url'] instanceof UploadedFile) {
            // Upload file và lấy thông tin
            $fileInfo = $this->handleImageUpload($data['image_url']);

            // Cập nhật đường dẫn vào data
            $data['image_url'] = $fileInfo['data']['url'];
        }
        // Nếu không phải file upload thì giữ nguyên giá trị (string URL)

        return parent::create($data, $with);
    }

    /**
     * Cập nhật gallery item
     * Hỗ trợ upload file mới hoặc sử dụng URL mới
     *
     * @param int $id ID của gallery item cần cập nhật
     * @param array $data Dữ liệu đầu vào
     * @param array $with Các quan hệ cần eager load
     * @return Model
     */
    public function update($id, array $data, array $with = []): Model
    {
        // Xử lý image_url nếu là file upload
        if (isset($data['image_url']) && $data['image_url'] instanceof UploadedFile) {
            // Lấy đường dẫn file hiện tại (nếu có)
            $oldImageUrl = $this->repository->find($id)->image_url;

            // Upload file và lấy thông tin
            $fileInfo = $this->handleImageUpload($data['image_url'], $oldImageUrl);

            // Cập nhật đường dẫn vào data
            $data['image_url'] = $fileInfo['data']['url'];
        }
        // Nếu không phải file upload thì giữ nguyên giá trị (string URL)

        return parent::update($id, $data, $with);
    }

    /**
     * Xử lý upload ảnh
     *
     * @param UploadedFile $file File ảnh upload
     * @param string|null $oldImageUrl URL của ảnh cũ (nếu có) để xóa
     * @return array Kết quả upload
     */
    protected function handleImageUpload(UploadedFile $file, $oldImageUrl = null): array
    {
        return $this->fileUploadService->uploadImage(
            $file,
            'gallery_items',  // Tên bảng
            'public',        // Disk
            $oldImageUrl,    // URL của ảnh cũ (nếu có)
            [               // Options bổ sung
                'max_width' => 1920,
                'max_height' => 1080,
            ]
        );
    }
}
