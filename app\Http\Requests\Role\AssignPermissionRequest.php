<?php

namespace App\Http\Requests\Role;

use App\Http\Requests\BaseRequest;
use App\Repositories\Eloquent\RoleRepository;
use Illuminate\Validation\Rule;

class AssignPermissionRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'id' => $this->route('id'),
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'id' => [
                'required',
                'integer',
                Rule::exists('roles', 'id'),
            ],
            'permission_ids' => [
                'required',
                'array',
                'min:1',
            ],
            'permission_ids.*' => [
                'integer',
                Rule::exists('permissions', 'id'),
            ],
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $roleId = $this->route('id');
            $roleRepository = app(RoleRepository::class);
            $role = $roleRepository->find($roleId);

            if (! $role) {
                $validator->errors()->add('id', 'Vai trò không tồn tại');

                return;
            }

            if ($role->is_protected) {
                $validator->errors()->add('role', 'Không thể gán quyền cho vai trò được bảo vệ');
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'id.required' => 'ID vai trò là bắt buộc',
            'id.integer' => 'ID vai trò phải là số nguyên',
            'id.exists' => 'Vai trò không tồn tại',
            'permission_ids.required' => 'Danh sách quyền là bắt buộc',
            'permission_ids.array' => 'Danh sách quyền phải là mảng',
            'permission_ids.min' => 'Danh sách quyền phải có ít nhất 1 quyền',
            'permission_ids.*.integer' => 'ID quyền phải là số nguyên',
            'permission_ids.*.exists' => 'Quyền không tồn tại',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'id' => 'ID vai trò',
            'permission_ids' => 'danh sách quyền',
            'permission_ids.*' => 'ID quyền',
        ];
    }

    /**
     * Cung cấp thông tin mô tả cho các tham số yêu cầu trong API
     */
    public function bodyParameters(): array
    {
        return [
            'permission_ids' => [
                'description' => 'Danh sách ID của các quyền cần gán (tối thiểu 1 quyền)',
                'example' => [1, 2, 3],
                'required' => true,
            ],
        ];
    }
}
