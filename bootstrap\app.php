<?php

use App\Exceptions\Handler;
use App\Http\Middleware\CheckPermission;
use App\Http\Middleware\JwtMiddleware;
use App\Http\Middleware\ProtectScribeDocs;
use App\Http\Middleware\PublicApiAuthMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Middleware\HandleCors;
use Illuminate\Http\Request;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up'
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(remove: [StartSession::class, ShareErrorsFromSession::class]);

        // Đảm bảo HandleCors được áp dụng cho các API request
        $middleware->api(prepend: [
            HandleCors::class,
        ]);

        $middleware->alias([
            'jwt' => JwtMiddleware::class,
            'permission' => CheckPermission::class,
            'public.api.auth' => PublicApiAuthMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->render(function (Throwable $e, Request $request) {
            return app(Handler::class)->render($request, $e);
        });
    })
    ->withMiddleware(function (Middleware $middleware) {
        // Đăng ký middleware để bảo vệ trang tài liệu Scribe
        $middleware->alias([
            'scribe.auth' => ProtectScribeDocs::class,
        ]);
    })
    ->create();
