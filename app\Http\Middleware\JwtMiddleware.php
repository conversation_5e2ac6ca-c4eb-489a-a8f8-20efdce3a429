<?php

namespace App\Http\Middleware;

use Closure;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;
use <PERSON><PERSON>\JWTAuth\Exceptions\TokenBlacklistedException;
use Tymon\JWTAuth\Exceptions\TokenExpiredException;
use Tymon\JWTAuth\Exceptions\TokenInvalidException;
use Tymon\JWTAuth\Facades\JWTAuth;

/**
 * Middleware xác thực JWT Token cho API
 *
 * Middleware này kiểm tra và xác thực JWT token trong mọi request:
 * - Verify token có hợp lệ không
 * - Kiểm tra token có bị blacklist không
 * - Xác thực user từ token
 * - Validate trạng thái tài khoản user
 *
 * Đ<PERSON><PERSON><PERSON> áp dụng cho các route cần authentication trong api.php
 */
class JwtMiddleware
{
    /**
     * Authentication scheme cho WWW-Authenticate header
     *
     * 'Bearer' là chuẩn RFC 6750 cho OAuth 2.0 Bearer Token
     * Header sẽ có dạng: WWW-Authenticate: Bearer
     * Client sẽ gửi token: Authorization: Bearer <token>
     */
    private const AUTH_SCHEME = 'Bearer';

    /**
     * Định nghĩa các mã lỗi chuẩn hóa để client có thể xử lý một cách tự động.
     *
     * Việc sử dụng mã lỗi (error code) mang lại các lợi ích:
     * - Phía client (frontend, mobile app) có thể dễ dàng xử lý từng trường hợp lỗi cụ thể.
     * - Logic của client không phụ thuộc vào nội dung thông báo lỗi (message), vốn có thể thay đổi.
     * - Dễ dàng thực hiện đa ngôn ngữ (i18n) ở phía client.
     * - Đảm bảo việc xử lý lỗi nhất quán trên các nền tảng khác nhau.
     */
    private const ERROR_CODES = [
        'TOKEN_NOT_PROVIDED' => 'TOKEN_NOT_PROVIDED', // Không có token trong request
        'USER_NOT_FOUND' => 'USER_NOT_FOUND', // Token hợp lệ nhưng user không tồn tại
        'USER_INACTIVE' => 'USER_INACTIVE', // User bị deactivate
        'USER_SUSPENDED' => 'USER_SUSPENDED', // User bị suspend
        'EMAIL_NOT_VERIFIED' => 'EMAIL_NOT_VERIFIED', // Email chưa verify
        'TOKEN_EXPIRED' => 'TOKEN_EXPIRED', // Token hết hạn
        'TOKEN_INVALID' => 'TOKEN_INVALID', // Token không hợp lệ (sai format, signature...)
        'TOKEN_BLACKLISTED' => 'TOKEN_BLACKLISTED', // Token đã logout
        'TOKEN_PARSE_ERROR' => 'TOKEN_PARSE_ERROR', // Lỗi parse token
        'AUTH_ERROR' => 'AUTH_ERROR', // Lỗi chung không xác định
    ];

    /**
     * Xử lý request đi qua middleware
     *
     * Flow xử lý:
     * 1. Kiểm tra token có tồn tại trong request không
     * 2. Parse và validate token
     * 3. Lấy user từ token
     * 4. Kiểm tra trạng thái user
     * 5. Cho phép request tiếp tục nếu mọi thứ OK
     *
     * @param Request $request Request hiện tại
     * @param Closure $next Middleware tiếp theo trong pipeline
     * @return mixed Response hoặc tiếp tục xử lý
     *
     * @throws UnauthorizedHttpException
     */
    public function handle(Request $request, Closure $next): mixed
    {
        try {
            // Bước 1: Kiểm tra token có tồn tại không
            // parser()->setRequest() bind request vào JWT parser
            // hasToken() check Authorization header hoặc query parameter 'token'
            if (! JWTAuth::parser()->setRequest($request)->hasToken()) {
                throw new UnauthorizedHttpException(self::AUTH_SCHEME, 'Token not provided');
            }

            // Bước 2 & 3: Parse token và lấy user
            // parseToken() decode và validate token (signature, format...)
            // authenticate() lấy user từ token payload (sub claim)
            $user = JWTAuth::parseToken()->authenticate();

            // Kiểm tra user có tồn tại trong database không
            // Trường hợp user đã bị xóa nhưng token vẫn còn valid
            if (! $user) {
                throw new UnauthorizedHttpException(self::AUTH_SCHEME, 'User not found');
            }

            // Bước 4: Validate các điều kiện business của user
            // Tách riêng method để dễ maintain và extend
            $this->validateUserStatus($user);
        } catch (TokenExpiredException $e) {
            // Token đã hết hạn (exp claim < current time)
            // Client cần gọi refresh token endpoint
            return $this->unauthorizedResponse('Token has expired', self::ERROR_CODES['TOKEN_EXPIRED']);
        } catch (TokenBlacklistedException $e) {
            // Token đã bị blacklist (đã logout)
            // JWT package tự động check blacklist khi parse
            return $this->unauthorizedResponse('Token is blacklisted', self::ERROR_CODES['TOKEN_BLACKLISTED']);
        } catch (TokenInvalidException $e) {
            // Token không hợp lệ:
            // - Sai format (không phải 3 phần base64)
            // - Signature không match
            // - Issuer (iss) không đúng
            return $this->unauthorizedResponse('Token is invalid', self::ERROR_CODES['TOKEN_INVALID']);
        } catch (JWTException $e) {
            // Các lỗi JWT khác:
            // - Lỗi config
            // - Lỗi parse không xác định
            return $this->unauthorizedResponse('Token could not be parsed', self::ERROR_CODES['TOKEN_PARSE_ERROR']);
        } catch (UnauthorizedHttpException $e) {
            // Catch các UnauthorizedHttpException được throw thủ công
            // Ví dụ: từ validateUserStatus()
            $errorCode = $this->getErrorCodeFromMessage($e->getMessage());

            return $this->unauthorizedResponse($e->getMessage(), $errorCode);
        } catch (Exception $e) {
            // Catch-all cho các lỗi không lường trước
            // Log chi tiết để debug
            Log::error('JWT Middleware Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'user_id' => $user->id ?? null, // Có thể null nếu lỗi xảy ra trước khi get user
                'ip' => $request->ip(),
            ]);

            // Trả về lỗi chung, không expose internal error
            return $this->unauthorizedResponse(
                'Authentication error',
                self::ERROR_CODES['AUTH_ERROR'],
                500 // Internal Server Error thay vì 401
            );
        }

        // Mọi thứ OK, cho request đi tiếp
        // User đã được authenticate và có thể access qua auth()->user()
        return $next($request);
    }

    /**
     * Validate trạng thái và điều kiện business của user
     *
     * Method này kiểm tra các business rules sau khi user đã được authenticate:
     * - Account có active không
     * - Email đã verify chưa
     * - Account có bị suspend không
     * - Có thể thêm các rules khác tùy requirement
     *
     * Sử dụng method_exists() để đảm bảo compatibility với các User model khác nhau
     *
     * @param mixed $user User model instance
     * @throws UnauthorizedHttpException Khi user không đáp ứng điều kiện
     */
    protected function validateUserStatus($user): void
    {
        // Kiểm tra user có active không
        // Dùng cho trường hợp admin deactivate account
        if (method_exists($user, 'isActive') && ! $user->isActive()) {
            throw new UnauthorizedHttpException(
                self::AUTH_SCHEME,
                'User account is inactive'
            );
        }

        // Kiểm tra user có bị suspend không
        if (method_exists($user, 'isSuspended') && $user->isSuspended()) {
            throw new UnauthorizedHttpException(
                self::AUTH_SCHEME,
                'User account is suspended'
            );
        }

        // Kiểm tra email đã verify chưa (optional)
        // Có thể comment out nếu không cần bắt buộc verify email
        // if (method_exists($user, 'hasVerifiedEmail') && !$user->hasVerifiedEmail()) {
        //     throw new UnauthorizedHttpException(
        //         self::AUTH_SCHEME,
        //         'Email address is not verified'
        //     );
        // }

        // Có thể thêm các validation khác:
        // - Check subscription status cho SaaS
        // - Check 2FA nếu bắt buộc
        // - Check IP whitelist cho admin accounts
        // - Check force password change
        // - etc.
    }

    /**
     * Tạo response cho các lỗi unauthorized
     *
     * Format response chuẩn cho tất cả authentication errors:
     * - success: false (consistency với success response)
     * - message: Human readable message
     * - error_code: Machine readable code cho client
     * - timestamp: ISO 8601 format để tracking
     *
     * @param string $message Thông báo lỗi cho user
     * @param string $code Error code cho client handling
     * @param int $status HTTP status code (default 401)
     * @return JsonResponse
     */
    protected function unauthorizedResponse(
        string $message,
        string $code,
        int    $status = 401
    ): JsonResponse {
        return response()->json(
            [
                'success' => false,
                'message' => $message,
                'error_code' => $code,
                'timestamp' => now()->toIso8601String(), // Format: 2024-01-15T10:30:00+00:00
            ],
            $status
        );
    }

    /**
     * Map message thành error code tương ứng
     *
     * Method này giúp maintain consistency giữa các exception message
     * và error codes, đặc biệt cho các UnauthorizedHttpException
     * được throw từ validateUserStatus()
     *
     * @param string $message Exception message
     * @return string Error code tương ứng
     */
    protected function getErrorCodeFromMessage(string $message): string
    {
        // Mapping table từ message sang error code
        $messageToCode = [
            'Token not provided' => self::ERROR_CODES['TOKEN_NOT_PROVIDED'],
            'User not found' => self::ERROR_CODES['USER_NOT_FOUND'],
            'User account is inactive' => self::ERROR_CODES['USER_INACTIVE'],
            'User account is suspended' => self::ERROR_CODES['USER_SUSPENDED'],
            'Email address is not verified' => self::ERROR_CODES['EMAIL_NOT_VERIFIED'],
        ];

        // Trả về error code tương ứng hoặc AUTH_ERROR nếu không match
        return $messageToCode[$message] ?? self::ERROR_CODES['AUTH_ERROR'];
    }
}
