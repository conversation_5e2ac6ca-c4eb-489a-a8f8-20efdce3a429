<?php

namespace App\Http\Requests\Auth;

use App\Http\Requests\BaseRequest;

class ChangePasswordRequest extends BaseRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'old_password' => ['required', 'string', 'min:6'],
            'new_password' => ['required', 'string', 'min:8', 'max:255', 'confirmed'],
        ];
    }

    /**
     * Defines the parameters for the request body.
     *
     * @return array
     */
    public function bodyParameters(): array
    {
        return [
            'old_password' => [
                'description' => 'Mật khẩu hiện tại của người dùng.',
                'example' => 'password123',
            ],
            'new_password' => [
                'description' => 'Mật khẩu mới của người dùng (tối thiểu 8 ký tự).',
                'example' => 'new_password_strong',
            ],
            'new_password_confirmation' => [
                'description' => '<PERSON><PERSON><PERSON> nhận mật khẩu mới.',
                'example' => 'new_password_strong',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'old_password.required' => '<PERSON>ui lòng nhập mật khẩu hiện tại',
            'old_password.min' => 'Mật khẩu hiện tại tối thiểu 6 ký tự',
            'new_password.required' => 'Vui lòng nhập mật khẩu mới',
            'new_password.min' => 'Mật khẩu mới tối thiểu 8 ký tự',
            'new_password.max' => 'Mật khẩu mới tối đa 255 ký tự',
            'new_password.confirmed' => 'Xác nhận mật khẩu mới không khớp',
        ];
    }
}
