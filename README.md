# 💎 JX1 Kiếm Hiệp Tình 1 - API Nội bộ

Dự án này là một backend API được xây dựng trên nền tảng **Laravel 12**, với môi trường phát triển được quản lý hoàn toàn bởi **Docker**.

## **📖 Tổng quan**

Dự án tuân thủ kiến trúc `Controller -> Service -> Repository` để phân tách rõ ràng các lớp nghiệp vụ, giúp code dễ bảo trì, tái sử dụng và kiểm thử.

- **Controller**: Tiếp nhận và phản hồi HTTP request.
- **Service**: Chứa logic nghiệp vụ chính.
- **Repository**: Trừu tượng hóa lớp truy cập dữ liệu (Database).

## **🚀 Bắt đầu nhanh**

### **<PERSON><PERSON><PERSON> cầu**

- [Docker Desktop](https://www.docker.com/products/docker-desktop/) đã được cài đặt và đang chạy.

### **Cài đặt lần đầu**

Thực hiện các lệnh sau từ thư mục gốc của dự án:

1.  **Sao chép và cấu hình file môi trường:**

    ```bash
    cp .env.example .env
    ```

    > ⚠️ **Quan trọng**: Mở file `.env` và tùy chỉnh các biến môi trường nếu cần (ví dụ: `APP_PORT`, `DB_DATABASE`).

2.  **(Tùy chọn) Import Database có sẵn:**
    Nếu có file dump `.sql`, hãy đặt vào thư mục `docker/mysql/initdb.d/`. Database sẽ được import tự động khi Docker khởi chạy lần đầu.

3.  **Build và khởi chạy Docker containers:**

    ```bash
    docker-compose up -d --build
    ```

4.  **Cài đặt các dependencies của Composer:**

    ```bash
    docker-compose exec app composer install
    ```

5.  **Tạo khóa ứng dụng Laravel:**

    ```bash
    docker-compose exec app php artisan key:generate
    ```

6.  **Chạy migrations và seeders để tạo cấu trúc DB và dữ liệu mẫu:**

    ```bash
    docker-compose exec app php artisan migrate --seed
    ```

7.  **Tạo symbolic link cho storage:**
    ```bash
    docker-compose exec app php artisan storage:link
    ```

🎉 **Hoàn tất!** Ứng dụng của bạn hiện đang chạy tại `http://localhost:8000` (hoặc port bạn đã cấu hình trong `.env`).

## **💻 Quy trình phát triển hàng ngày**

### **Quản lý môi trường Docker**

- **Khởi động:** `docker-compose up -d`
- **Dừng:** `docker-compose down`
- **Xem logs thời gian thực (khuyên dùng):**
  _Đảm bảo `LOG_CHANNEL=stderr` trong file `.env`._
    ```bash
    docker-compose logs -f app
    ```

### **Chạy lệnh Artisan**

Luôn sử dụng tiền tố `docker-compose exec app` để chạy các lệnh Artisan.

```bash
# Ví dụ: Tạo một Model và Migration mới
docker-compose exec app php artisan make:model Category -m

# Ví dụ: Xem danh sách các routes
docker-compose exec app php artisan route:list
```

### **Chất lượng & Định dạng Code**

Dự án tích hợp sẵn **PHP CS Fixer** và **Prettier** để tự động định dạng code.

- **Tự động:** Khi bạn `git commit`, Husky sẽ tự động chạy và định dạng các file đã được `stage`. Bạn không cần làm gì thêm.
- **Thủ công:** Nếu muốn định dạng toàn bộ dự án, chạy `npm run format`.

### **🤖 Sử dụng AI Agent để đảm bảo đồng bộ**

Để đảm bảo AI Agent sinh ra code nhất quán với style của dự án, hãy luôn cung cấp "bối cảnh" (context) cho nó.

**Quy tắc vàng:** Khi yêu cầu AI tạo code mới (method, class, ...), hãy cung cấp cho nó một đoạn code mẫu đã có sẵn trong dự án và yêu cầu nó tuân theo phong cách đó.

> **Ví dụ về một prompt hiệu quả:**
>
> "Dựa vào `ProductController` đã có, hãy tạo giúp tôi một `CategoryController` với đầy đủ các phương thức CRUD (index, store, show, update, destroy).
>
> **Hãy đảm bảo rằng:**
>
> 1.  Code style, cách đặt tên biến, và cấu trúc phải hoàn toàn tương đồng với `ProductController`.
> 2.  Sử dụng `CategoryService` cho logic nghiệp vụ.
> 3.  Sử dụng `StoreCategoryRequest` và `UpdateCategoryRequest` để validate.
> 4.  Sử dụng `CategoryResource` để trả về response.
>
> Dưới đây là nội dung của `ProductController` để bạn tham khảo:
>
> ````php
> // ... (dán toàn bộ nội dung của ProductController vào đây) ...
> ```"
> ````

Bằng cách này, AI sẽ có một "khuôn mẫu" rõ ràng để tuân theo, giúp code mới được tạo ra hòa hợp một cách tự nhiên với toàn bộ dự án.

## **🏗️ Hướng dẫn tạo một API Resource mới**

Đây là luồng công việc chuẩn để tạo một bộ CRUD API hoàn chỉnh (ví dụ cho `Product`).

**Luồng xử lý:** `Route` → `Controller` → `FormRequest` → `Service` → `Repository` → `Model`

1.  **Tạo Model & Migration:**

    ```bash
    docker-compose exec app php artisan make:model Product -m
    ```

    - Chạy `docker-compose exec app php artisan migrate`.

2.  **Tạo Repository, Service, Form Requests, API Resources, và Controller** theo cấu trúc đã định.

3.  **Định nghĩa Route** trong `routes/api.php`.

> ✍️ **Ghi chú:** Để xem hướng dẫn chi tiết từng bước có kèm code mẫu, vui lòng tham khảo tài liệu `CONTRIBUTING.md`.

## **📄 Tài liệu & Công cụ hữu ích**

### **Tài liệu API (Scribe)**

Dự án sử dụng Scribe để tự động tạo tài liệu API.

- **Tạo/Cập nhật tài liệu:**
    ```bash
    docker-compose exec app php artisan scribe:generate
    ```
- **Truy cập tài liệu:** Mở trình duyệt và truy cập `http://localhost:8000/docs`.

> 💡 **Mẹo**: Để gom nhóm API chuẩn xác trong tài liệu, hãy thêm docblock `@group Tên Nhóm` trên cả **Controller class** và **Route group**.

### **Hỗ trợ IDE (IDE Helper)**

Sau khi cài/cập nhật package, chạy các lệnh sau để IDE có thể gợi ý code tốt hơn:

```bash
docker-compose exec app php artisan ide-helper:generate
docker-compose exec app php artisan ide-helper:models -W
docker-compose exec app php artisan ide-helper:meta
```
