<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\Menus\StoreMenuRequest;
use App\Http\Requests\Menus\UpdateMenuRequest;
use App\Resources\Menus\MenuResource;
use App\Resources\Menus\MenuResourceCollection;
use App\Services\MenuService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Menu - Group
 */
class MenuController extends BaseController
{
    /**
     * MenuService instance.
     */
    protected MenuService $service;

    /**
     * MenuController constructor.
     */
    public function __construct(MenuService $service)
    {
        $this->service = $service;
    }

    /**
     * Hiển thị danh sách các menu.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $menus = $this->service->list($request, ['createdBy', 'updatedBy']);

        return $this->success(new MenuResourceCollection($menus), 'Lấy danh sách menu thành công');
    }

    /**
     * Lưu một menu mới.
     *
     * @param StoreMenuRequest $request
     * @return JsonResponse
     */
    public function store(StoreMenuRequest $request): JsonResponse
    {
        $menu = $this->service->create($request->validated(), ['createdBy', 'updatedBy']);

        return $this->created(new MenuResource($menu), 'Tạo menu thành công');
    }

    /**
     * Hiển thị chi tiết một menu.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $menu = $this->service->read($id, ['createdBy', 'updatedBy']);

        return $this->success(new MenuResource($menu), 'Lấy thông tin menu thành công');
    }

    /**
     * Cập nhật một menu.
     *
     * @param UpdateMenuRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateMenuRequest $request, int $id): JsonResponse
    {
        $menu = $this->service->update($id, $request->validated(), ['createdBy', 'updatedBy']);

        return $this->success(new MenuResource($menu), 'Cập nhật menu thành công');
    }

    /**
     * Xóa một menu.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $deleted = $this->service->delete($id);

        if (! $deleted) {
            return $this->error(message: "Không tìm thấy {$this->service->getModelName()} với ID là {$id}");
        }

        return $this->successNoContent(message: 'Đã xoá thành công');
    }
}
