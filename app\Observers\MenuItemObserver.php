<?php

namespace App\Observers;

use App\Models\MenuItem;

/**
 * Observer xử lý các sự kiện liên quan đến MenuItem
 */
class MenuItemObserver extends BaseObserver
{
    /**
     * Xử lý sự kiện "created" của MenuItem.
     *
     * @param \App\Models\MenuItem $menuItem
     * @return void
     */
    public function created(MenuItem $menuItem): void
    {
        $this->log(
            action: 'create',
            description: "Tạo mới mục menu '{$menuItem->title}'",
            model: $menuItem,
            dataAfter: $menuItem->toArray()
        );
    }

    /**
     * <PERSON><PERSON> lý sự kiện "updated" của MenuItem.
     *
     * @param \App\Models\MenuItem $menuItem
     * @return void
     */
    public function updated(MenuItem $menuItem): void
    {
        $this->log(
            action: 'update',
            description: "Cập nhật mục menu '{$menuItem->title}'",
            model: $menuItem,
            dataBefore: $menuItem->getOriginal(),
            dataAfter: $menuItem->getChanges()
        );
    }

    /**
     * Xử lý sự kiện "deleting" của MenuItem.
     *
     * @param \App\Models\MenuItem $menuItem
     * @return void
     */
    public function deleting(MenuItem $menuItem): void
    {
        $this->log(
            action: 'delete',
            description: "Xóa mục menu '{$menuItem->title}'",
            model: $menuItem,
            dataBefore: $menuItem->toArray()
        );

        // Thêm timestamp vào slug để tránh xung đột khi tạo mới
        if (! $menuItem->isForceDeleting()) {
            $menuItem->slug = $menuItem->slug . '_deleted_' . time();
            $menuItem->saveQuietly(); // Lưu mà không kích hoạt các events
        }
    }
}
