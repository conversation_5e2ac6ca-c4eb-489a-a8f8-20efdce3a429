<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\GalleryItems\StoreGalleryItemRequest;
use App\Http\Requests\GalleryItems\UpdateGalleryItemRequest;
use App\Resources\GalleryItems\GalleryItemResource;
use App\Resources\GalleryItems\GalleryItemResourceCollection;
use App\Services\GalleryItemService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Thư viện ảnh - Ảnh
 */
class GalleryItemController extends BaseController
{
    public function __construct(protected GalleryItemService $service)
    {
    }

    /**
     * Lấy danh sách các ảnh trong một nhóm thư viện ảnh cụ thể.
     *
     * @urlParam gallery_group_id integer required ID của nhóm thư viện ảnh. Example: 1
     *
     * @param Request $request
     * @param int $gallery_group ID của nhóm thư viện ảnh
     * @return JsonResponse
     */
    public function index(Request $request, int $gallery_group_id): JsonResponse
    {
        // Lọc các gallery item thuộc về gallery group có ID là $gallery_group_id
        $request->merge(['gallery_group_id' => $gallery_group_id]);
        $galleryItems = $this->service->list($request, ['createdBy', 'updatedBy']);

        return $this->success(
            new GalleryItemResourceCollection($galleryItems),
            'Lấy danh sách ảnh thành công'
        );
    }

    /**
     * Lưu một ảnh mới thuộc nhóm thư viện ảnh cụ thể.
     *
     * @urlParam gallery_group_id integer required ID của nhóm thư viện ảnh. Example: 1
     *
     * @param StoreGalleryItemRequest $request
     * @param int $gallery_group ID của nhóm thư viện ảnh
     * @return JsonResponse
     */
    public function store(StoreGalleryItemRequest $request, int $gallery_group_id): JsonResponse
    {
        // Thêm gallery_group_id vào dữ liệu validated
        $data = $request->validated();
        $data['gallery_group_id'] = $gallery_group_id;

        $galleryItem = $this->service->create($data, ['createdBy', 'updatedBy']);

        return $this->created(
            new GalleryItemResource($galleryItem),
            'Tạo ảnh thành công'
        );
    }

    /**
     * Hiển thị chi tiết một ảnh trong nhóm thư viện ảnh.
     *
     * @urlParam gallery_group_id integer required ID của nhóm thư viện ảnh. Example: 1
     * @urlParam id integer required ID của ảnh. Example: 1
     *
     * @param int $gallery_group ID của nhóm thư viện ảnh
     * @param int $gallery_item ID của ảnh
     * @return JsonResponse
     */
    public function show(int $gallery_group_id, int $id): JsonResponse
    {
        $galleryItem = $this->service->read($id, ['createdBy', 'updatedBy']);

        // Kiểm tra xem gallery item có thuộc về gallery group không
        if ($galleryItem->gallery_group_id != $gallery_group_id) {
            return $this->notFound('Ảnh không thuộc về nhóm thư viện ảnh này');
        }

        return $this->success(
            new GalleryItemResource($galleryItem),
            'Lấy thông tin ảnh thành công'
        );
    }

    /**
     * Cập nhật một ảnh trong nhóm thư viện ảnh.
     *
     * Lưu ý: Khi cần upload file (meta_image), hãy sử dụng Method Spoofing:
     * - Gửi request HTTP POST thay vì PUT
     * - Thêm trường _method="PUT" vào form-data
     * - Thêm file meta_image vào form-data
     *
     * Nguyên nhân: PHP không xử lý được file upload trong PUT requests trực tiếp.
     *
     * @urlParam gallery_group_id integer required ID của nhóm thư viện ảnh. Example: 1
     * @urlParam id integer required ID của ảnh. Example: 1
     *
     * @param UpdateGalleryItemRequest $request
     * @param int $gallery_group ID của nhóm thư viện ảnh
     * @param int $gallery_item ID của ảnh
     * @return JsonResponse
     */
    public function update(UpdateGalleryItemRequest $request, int $gallery_group_id, int $id): JsonResponse
    {
        // Kiểm tra xem gallery item có thuộc về gallery group không
        $existingGalleryItem = $this->service->read($id, []);
        if ($existingGalleryItem->gallery_group_id != $gallery_group_id) {
            return $this->notFound('Ảnh không thuộc về nhóm thư viện ảnh này');
        }

        $galleryItem = $this->service->update($id, $request->validated(), ['createdBy', 'updatedBy']);

        return $this->success(
            new GalleryItemResource($galleryItem),
            'Cập nhật ảnh thành công'
        );
    }

    /**
     * Xóa một ảnh trong nhóm thư viện ảnh.
     *
     * @param int $gallery_group ID của nhóm thư viện ảnh
     * @param int $gallery_item ID của ảnh
     * @return JsonResponse
     */
    public function destroy(int $gallery_group_id, int $id): JsonResponse
    {
        // Kiểm tra xem gallery item có thuộc về gallery group không
        $existingGalleryItem = $this->service->read($id, []);
        if ($existingGalleryItem->gallery_group_id != $gallery_group_id) {
            return $this->notFound('Ảnh không thuộc về nhóm thư viện ảnh này');
        }

        $deleted = $this->service->delete($id);

        if (! $deleted) {
            return $this->error('Không thể xóa ảnh này', 500);
        }

        return $this->successNoContent('Đã xóa ảnh thành công');
    }
}
