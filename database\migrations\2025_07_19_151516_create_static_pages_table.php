<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('static_pages', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('Tiêu đề trang tĩnh');
            $table->string('slug')->unique();
            $table->longText('body')->comment('Nội dung trang tĩnh');
            $table->tinyInteger('status')->default(1)->comment('Trạng thái: 0: Inactive, 1: Active');
            $table->string('meta_title', 255)->nullable()->comment('Tiêu đề SEO');
            $table->text('meta_description')->nullable()->comment('Mô tả SEO');
            $table->text('meta_keywords')->nullable()->comment('Từ khóa SEO');
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete()->comment('Người tạo');
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete()->comment('Người cập nhật');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('static_pages');
    }
};
