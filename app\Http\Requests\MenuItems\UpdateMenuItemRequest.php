<?php

namespace App\Http\Requests\MenuItems;

use App\Enums\LinkableType;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class UpdateMenuItemRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Chuẩn bị dữ liệu cho validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Thêm menu_id từ route vào request để validation
        $route = $this->route();
        if ($route && $route->parameter('menu_id')) {
            $this->merge(['menu_id' => $route->parameter('menu_id')]);
        }

        $rawType = $this->input('linkable_type');
        if ($rawType !== null && $rawType !== '') {
            if (is_numeric($rawType)) {
                try {
                    $enum = LinkableType::from((int) $rawType);
                    $this->merge(['linkable_type' => $enum->modelClass()]);
                } catch (\ValueError $e) {
                }
            }
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $menuItemId = $this->route('id');

        $modelClasses = array_map(
            fn (LinkableType $t) => $t->modelClass(),
            LinkableType::cases()
        );

        $linkableIdRules = ['nullable', 'integer', 'required_with:linkable_type'];
        if ($this->filled('linkable_type') && in_array($this->input('linkable_type'), $modelClasses, true)) {
            $modelClass = $this->input('linkable_type');
            $table = (new $modelClass())->getTable();
            $linkableIdRules[] = "exists:{$table},id";
        }

        $linkableTypeRules = ['nullable', 'string', 'required_with:linkable_id', Rule::in($modelClasses)];

        return [
            'menu_id' => ['sometimes', 'required', 'integer', 'exists:menus,id'],
            'title' => ['sometimes', 'required', 'string', 'max:255'],
            'slug' => [
                'sometimes',
                'required',
                'string',
                'max:255',
                Rule::unique('menu_items', 'slug')->ignore($menuItemId),
            ],
            'parent_id' => ['nullable', 'integer', 'exists:menu_items,id'],
            'order' => ['nullable', 'integer'],
            'target' => ['nullable', 'string', Rule::in(['_self', '_blank'])],
            'linkable_id' => $linkableIdRules,
            'linkable_type' => $linkableTypeRules,
            'custom_url' => ['nullable', 'string', 'max:255'],
            'status' => ['nullable', 'boolean'],
        ];
    }

    /**
     * Defines the parameters for the request body.
     *
     * @return array
     */
    public function bodyParameters(): array
    {
        return [
            'title' => [
                'description' => 'Tiêu đề của mục menu (tùy chọn).',
                'example' => 'Trang chủ',
            ],
            'slug' => [
                'description' => 'Slug duy nhất cho mục menu (tùy chọn).',
                'example' => 'trang-chu',
            ],
            'parent_id' => [
                'description' => 'ID của mục menu cha (tùy chọn, cho menu đa cấp).',
                'example' => null,
            ],
            'order' => [
                'description' => 'Thứ tự sắp xếp của mục menu (tùy chọn, mặc định 0).',
                'example' => 1,
            ],
            'target' => [
                'description' => 'Cách mở liên kết: `_self` (mặc định) hoặc `_blank` (tùy chọn).',
                'example' => '_self',
            ],
            'linkable_id' => [
                'description' => 'ID của đối tượng được liên kết (Post, Category, tùy chọn).',
                'example' => null,
            ],
            'linkable_type' => [
                'description' => 'Loại đối tượng liên kết (1: Post, 2: Category, 3: StaticPage) hoặc FQCN model. Nếu dùng số, hệ thống sẽ tự convert sang model tương ứng.',
                'example' => 1,
            ],
            'custom_url' => [
                'description' => 'Đường dẫn tùy chỉnh nếu không dùng model (tùy chọn).',
                'example' => '/lien-he',
            ],
            'status' => [
                'description' => 'Trạng thái hiển thị (1: Active, 0: Inactive, tùy chọn).',
                'example' => true,
            ],
        ];
    }

    /**
     * Lấy các thông báo lỗi cho các quy tắc xác thực đã xác định.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'menu_id.required' => 'Vui lòng chọn menu.',
            'menu_id.integer' => 'ID menu phải là một số nguyên.',
            'menu_id.exists' => 'Menu được chọn không tồn tại.',

            'title.required' => 'Vui lòng nhập tiêu đề.',
            'title.string' => 'Tiêu đề phải là một chuỗi ký tự.',
            'title.max' => 'Tiêu đề không được vượt quá 255 ký tự.',

            'slug.required' => 'Slug là bắt buộc.',
            'slug.string' => 'Slug phải là một chuỗi ký tự.',
            'slug.max' => 'Slug không được vượt quá 255 ký tự.',
            'slug.unique' => 'Slug đã tồn tại. Vui lòng chọn slug khác.',

            'order.integer' => 'Thứ tự phải là một số nguyên.',

            'parent_id.integer' => 'ID cha phải là một số nguyên.',
            'parent_id.exists' => 'Menu cha được chọn không tồn tại.',

            'target.string' => 'Target phải là một chuỗi ký tự.',
            'target.in' => 'Target không hợp lệ. Chỉ chấp nhận _self hoặc _blank.',

            'linkable_id.integer' => 'ID liên kết phải là một số nguyên.',
            'linkable_id.required_with' => 'Vui lòng nhập ID liên kết khi đã chọn loại liên kết.',
            'linkable_id.exists' => 'ID liên kết không tồn tại trong loại đã chọn.',

            'linkable_type.string' => 'Loại liên kết phải là một chuỗi ký tự.',
            'linkable_type.required_with' => 'Vui lòng chọn loại liên kết khi có ID liên kết.',
            'linkable_type.in' => 'Loại liên kết không hợp lệ.',

            'custom_url.string' => 'URL tùy chỉnh phải là một chuỗi ký tự.',
            'custom_url.max' => 'URL tùy chỉnh không được vượt quá 255 ký tự.',

            'status.boolean' => 'Trạng thái phải là true hoặc false.',
        ];
    }
}
