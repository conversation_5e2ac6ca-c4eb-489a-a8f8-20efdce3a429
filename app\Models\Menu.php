<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Menu extends BaseModel
{
    use HasFactory;
    use SoftDeletes;

    /**
     * Các thuộc tính nên được <PERSON>n khi chuyển thành mảng hoặc JSON.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'created_by',
        'updated_by',
    ];
    /**
     * Kh<PERSON>a chính của bảng.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * C<PERSON>c thuộc tính có thể gán hàng loạt.
     *
     * @var array<int, string>
     */
    protected $fillable = ['name', 'location_key', 'created_by', 'updated_by'];

    /**
     * Tên bảng tương ứng với model.
     *
     * @var string
     */
    protected $table = 'menus';

    /**
     * Lấy các menu item thuộc về menu này.
     */
    public function menuItems()
    {
        return $this->hasMany(MenuItem::class)->orderBy('order');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
