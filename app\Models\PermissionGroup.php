<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PermissionGroup extends BaseModel
{
    /**
     * Kh<PERSON>a chính của bảng.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * <PERSON><PERSON><PERSON> thuộc tính có thể gán hàng loạt.
     *
     * @var array<int, string>
     */
    protected $fillable = ['name', 'parent_id', 'key', 'description', 'created_by', 'updated_by'];

    /**
     * <PERSON><PERSON><PERSON> các casts dành riêng cho model này.
     *
     * @return array
     */
    protected function getModelCasts(): array
    {
        return [];
    }
    protected $table = 'permission_groups';

    /**
     * Nhóm cha
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * Danh sách nhóm con
     */
    public function children(): HasM<PERSON>
    {
        return $this->hasMany(self::class, 'parent_id');
    }
}
