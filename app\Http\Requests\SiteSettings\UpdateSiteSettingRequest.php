<?php

namespace App\Http\Requests\SiteSettings;

use App\Http\Requests\BaseRequest;
use Illuminate\Http\UploadedFile;
use Illuminate\Validation\Rule;

/**
 * Request xác thực khi cập nhật Site Setting
 * Đã cập nhật để hỗ trợ upload file cho các loại: image, document, video
 */
class UpdateSiteSettingRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Chuẩn bị dữ liệu cho validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Thêm group_id từ route vào request để validation
        $route = $this->route();
        if ($route && $route->parameter('site_setting_group_id')) {
            $this->merge(['group_id' => $route->parameter('site_setting_group_id')]);
        }
    }

    /**
     * <PERSON><PERSON>y các quy tắc xác thực áp dụng cho yêu cầu.
     *
     * @return array
     */
    public function rules(): array
    {
        // Lấy ID của site setting từ route
        $siteSettingId = $this->route('id');
        $types = ['text', 'textarea', 'editor', 'switch', 'image', 'document', 'video'];

        $rules = [
            'group_id' => ['sometimes', 'required', 'integer', 'exists:site_setting_groups,id'],
            'name' => ['sometimes', 'required', 'string', 'max:255'],
            'key' => [
                'sometimes',
                'required',
                'string',
                'max:50',
                'regex:/^[a-z0-9_-]+$/',
                Rule::unique('site_settings', 'key')->ignore($siteSettingId),
            ],
            'value' => ['nullable'],
            'type' => ['nullable', 'string', Rule::in($types)],
            'status' => ['nullable', 'integer', Rule::in([0, 1])],
        ];

        return $rules;
    }

    /**
     * Defines the parameters for the request body.
     *
     * @return array
     */
    public function bodyParameters(): array
    {
        return [
            'name' => [
                'description' => 'Tên của cài đặt (tùy chọn).',
                'example' => 'Tên trang web',
            ],
            'key' => [
                'description' => 'Khóa định danh duy nhất cho cài đặt (tùy chọn, chỉ chứa a-z, 0-9, _, -).',
                'example' => 'site_name',
            ],
            'value' => [
                'description' => 'Giá trị của cài đặt hoặc file upload (tùy chọn). Có thể là chuỗi văn bản hoặc file upload tùy thuộc vào type. Khi cập nhật, có thể bỏ trống để giữ nguyên giá trị cũ.',
                'example' => 'Kiếm Hiệp Tình Duyên Mới',
                'type' => 'mixed',
            ],
            'type' => [
                'description' => 'Loại của cài đặt (tùy chọn). Các loại hỗ trợ: text, textarea, editor, switch, image, document, video.',
                'example' => 'text',
            ],
            'status' => [
                'description' => 'Trạng thái của cài đặt (tùy chọn, 0: Inactive, 1: Active).',
                'example' => 1,
            ],
        ];
    }

    /**
     * Thiết lập các quy tắc validation tùy chỉnh.
     *
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $type = $this->input('type');
            $value = $this->input('value');

            // Nếu có cập nhật type và value, và type là các loại file upload
            if ($value !== null && in_array($type, ['image', 'document', 'video'])) {
                // Nếu value không phải là file upload
                if (! ($value instanceof UploadedFile)) {
                    // Kiểm tra nếu là URL hợp lệ
                    if (! is_string($value) || ! filter_var($value, FILTER_VALIDATE_URL)) {
                        $validator->errors()->add('value', "Khi chọn type là {$type}, phải cung cấp file upload hoặc URL hợp lệ.");
                    } else {
                        // Kiểm tra đuôi file của URL có phù hợp với type không
                        $fileExtension = pathinfo(parse_url($value, PHP_URL_PATH), PATHINFO_EXTENSION);
                        $fileExtension = strtolower($fileExtension);

                        switch ($type) {
                            case 'image':
                                $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
                                if (! in_array($fileExtension, $validExtensions)) {
                                    $validator->errors()->add('value', "URL hình ảnh phải có đuôi hợp lệ (" . implode(', ', $validExtensions) . ").");
                                }

                                break;
                            case 'document':
                                $validExtensions = ['pdf', 'doc', 'docx', 'txt'];
                                if (! in_array($fileExtension, $validExtensions)) {
                                    $validator->errors()->add('value', "URL tài liệu phải có đuôi hợp lệ (" . implode(', ', $validExtensions) . ").");
                                }

                                break;
                            case 'video':
                                $validExtensions = ['mp4', 'mpeg', 'mov', 'webm'];
                                if (! in_array($fileExtension, $validExtensions)) {
                                    $validator->errors()->add('value', "URL video phải có đuôi hợp lệ (" . implode(', ', $validExtensions) . ").");
                                }

                                break;
                        }
                    }
                } else {
                    // Kiểm tra loại file phù hợp với type
                    switch ($type) {
                        case 'image':
                            if (! in_array($value->getMimeType(), ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'])) {
                                $validator->errors()->add('value', 'File upload phải là một hình ảnh hợp lệ (jpeg, png, gif, webp, svg).');
                            }

                            break;
                        case 'document':
                            if (! in_array($value->getMimeType(), ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'])) {
                                $validator->errors()->add('value', 'File upload phải là một tài liệu hợp lệ (pdf, doc, docx, txt).');
                            }

                            break;
                        case 'video':
                            if (! in_array($value->getMimeType(), ['video/mp4', 'video/mpeg', 'video/quicktime', 'video/webm'])) {
                                $validator->errors()->add('value', 'File upload phải là một video hợp lệ (mp4, mpeg, mov, webm).');
                            }

                            break;
                    }

                    // Kiểm tra kích thước file
                    $maxSize = 10240; // 10MB
                    if ($value->getSize() > $maxSize * 1024) {
                        $validator->errors()->add('value', 'Kích thước file không được vượt quá 10MB.');
                    }
                }
            }
            // Nếu không có value mới, giữ nguyên giá trị cũ
        });
    }

    /**
     * Lấy các thông báo lỗi cho các quy tắc xác thực đã xác định.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'group_id.required' => 'ID nhóm setting là bắt buộc.',
            'group_id.integer' => 'ID nhóm setting phải là một số nguyên.',
            'group_id.exists' => 'ID nhóm setting không tồn tại.',

            'name.required' => 'Vui lòng nhập tên setting.',
            'name.max' => 'Tên setting không được vượt quá 255 ký tự.',

            'key.required' => 'Mã setting là bắt buộc.',
            'key.string' => 'Mã setting phải là một chuỗi ký tự.',
            'key.max' => 'Mã setting không được vượt quá 50 ký tự.',
            'key.regex' => 'Mã setting chỉ được chứa chữ thường, số, dấu gạch dưới (_) và gạch ngang (-).',
            'key.unique' => 'Mã setting đã tồn tại. Vui lòng chọn mã khác.',

            'type.required' => 'Loại nội dung setting là bắt buộc.',
            'type.string' => 'Loại nội dung setting phải là một chuỗi ký tự.',
            'type.in' => 'Loại nội dung setting không hợp lệ.',

            'status.required' => 'Trạng thái setting là bắt buộc.',
            'status.integer' => 'Trạng thái setting phải là một số.',
            'status.in' => 'Trạng thái setting không hợp lệ.',
        ];
    }
}
