# <PERSON><PERSON><PERSON> h<PERSON>nh Docker Compose cho môi trường staging
services:
  # Service cho ứng dụng <PERSON> (PHP-FPM)
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: jx1-api-app-staging
    restart: unless-stopped
    env_file:
      - .env.staging
    working_dir: /var/www/html
    volumes:
      - ./:/var/www/html
    networks:
      - jx1-network-staging

  # Service cho Nginx Web Server
  nginx:
    image: nginx:1.25-alpine
    container_name: jx1-api-nginx-staging
    restart: unless-stopped
    ports:
      - "8001:80"
    volumes:
      - ./:/var/www/html
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - app
    networks:
      - jx1-network-staging

  # Service cho Redis
  redis:
    image: redis:7-alpine
    container_name: jx1-api-redis-staging
    restart: unless-stopped
    ports:
      - "6380:6379"
    volumes:
      - redisdata-staging:/data
    networks:
      - jx1-network-staging

# Khai báo network chung
networks:
  jx1-network-staging:
    driver: bridge

# Khai báo volume để lưu trữ dữ liệu redis
volumes:
  redisdata-staging:
    driver: local
