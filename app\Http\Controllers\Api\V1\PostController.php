<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\Post\StorePostRequest;
use App\Http\Requests\Post\UpdatePostRequest;
use App\Resources\Posts\PostResource;
use App\Resources\Posts\PostResourceCollection;
use App\Services\PostService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Bài viết
 */
class PostController extends BaseController
{
    use ApiResponse;

    public function __construct(protected PostService $service)
    {
    }

    /**
     * Hiển thị danh sách bài viết.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $posts = $this->service->list($request, ['category', 'createdBy', 'updatedBy']);

        return $this->success(new PostResourceCollection($posts), 'L<PERSON>y danh sách bài viết thành công.');
    }

    /**
     * L<PERSON>u bài viết mới.
     *
     * @param StorePostRequest $request
     * @return JsonResponse
     */
    public function store(StorePostRequest $request): JsonResponse
    {
        $post = $this->service->create($request->validated(), ['category', 'createdBy', 'updatedBy']);

        return $this->created(new PostResource($post), 'Tạo bài viết mới thành công.');
    }

    /**
     * Hiển thị chi tiết bài viết.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $post = $this->service->read($id, ['category', 'createdBy', 'updatedBy']);

        return $this->success(new PostResource($post), 'Lấy thông tin bài viết thành công.');
    }

    /**
     * Cập nhật bài viết.
     *
     * Lưu ý: Khi cần upload file (meta_image), hãy sử dụng Method Spoofing:
     * - Gửi request HTTP POST thay vì PUT
     * - Thêm trường _method="PUT" vào form-data
     * - Thêm file meta_image vào form-data
     *
     * Nguyên nhân: PHP không xử lý được file upload trong PUT requests trực tiếp.
     *
     * @param UpdatePostRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdatePostRequest $request, int $id): JsonResponse
    {
        $post = $this->service->update($id, $request->validated(), ['category', 'createdBy', 'updatedBy']);

        return $this->success(new PostResource($post), 'Cập nhật bài viết thành công.');
    }

    /**
     * Xoá bài viết.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $deleted = $this->service->delete($id);

        if (! $deleted) {
            return $this->error(message: "Không tìm thấy {$this->service->getModelName()} với ID là {$id}");
        }

        return $this->successNoContent(message: 'Đã xoá thành công');
    }

    /**
     * Cập nhật các thuộc tính của bài viết.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateAttributes(Request $request, int $id): JsonResponse
    {
        if (empty($request->all())) {
            return $this->error('Không có dữ liệu để cập nhật', 422);
        }
        $attributes = $request->all();
        $post = $this->service->updateAttributesWithValidation($id, $attributes, UpdatePostRequest::class);

        return $this->success(new PostResource($post), 'Cập nhật thuộc tính bài viết thành công.');
    }
}
