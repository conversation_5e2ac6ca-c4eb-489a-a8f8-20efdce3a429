# Hướng dẫn đóng góp & Quy trình tạo API Resource

Tài liệu này cung cấp hướng dẫn chi tiết từng bước để tạo một bộ API CRUD hoàn chỉnh cho một model mới, tuân thủ theo kiến trúc và các quy tắc của dự án.

## **Luồng hoạt động**

`Request` → `Controller` → `Service` → `Repository` → `Model`

- **Controller**: Tiếp nhận request, validate dữ liệu đầu vào (sử dụng FormRequest), gọi Service xử lý logic, và trả về response (sử dụng API Resource).
- **Service**: Chứa logic nghiệp vụ chính, xử lý các quy tắc business. Gọi Repository để thao tác với dữ liệu.
- **Repository**: Đóng vai trò là lớp truy cập dữ liệ<PERSON>, trừ<PERSON> tượng hóa các câu lệnh truy vấn Eloquent, giúp code dễ test và bảo trì.

---

### **Bước 1: Tạo Model và Migration**

Đầu tiên, tạo model `Product` và file migration tương ứng:

```bash
docker-compose exec app php artisan make:model Product -m
```

Chỉnh sửa file migration để bao gồm các trường chuẩn của dự án:

```php
// database/migrations/xxxx_create_products_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->decimal('price', 10, 2);
            $table->text('description')->nullable();
            $table->string('status')->default('active');
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
            $table->foreign('created_by')->references('id')->on('users');
            $table->foreign('updated_by')->references('id')->on('users');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
```

Chạy migration để tạo bảng trong database:

```bash
docker-compose exec app php artisan migrate
```

### **Bước 2: Cập nhật Model**

Cập nhật model `Product` với các thuộc tính và relationships chuẩn:

```php
// app/Models/Product.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends BaseModel
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['name', 'price', 'description', 'status', 'created_by', 'updated_by'];

    protected $casts = [
        'price' => 'decimal:2',
    ];

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }
}
```

### **Bước 3: Tạo Repository**

Tạo interface và implementation cho Repository.

**3.1. Tạo Repository Interface**

```php
// app/Repositories/Interfaces/ProductRepositoryInterface.php
namespace App\Repositories\Interfaces;

interface ProductRepositoryInterface extends BaseRepositoryInterface
{
    // public function findByStatus(string $status);
}
```

**3.2. Tạo Repository Implementation**

```php
// app/Repositories/Eloquent/ProductRepository.php
namespace App\Repositories\Eloquent;

use App\Models\Product;
use App\Repositories\Interfaces\ProductRepositoryInterface;

class ProductRepository extends BaseRepository implements ProductRepositoryInterface
{
    protected function getModelClass(): string
    {
        return Product::class;
    }
}
```

### **Bước 4: Tạo Service**

Service là nơi chứa logic nghiệp vụ chính.

```php
// app/Services/ProductService.php
namespace App\Services;

use App\Repositories\Interfaces\ProductRepositoryInterface;

class ProductService extends BaseCrudService
{
    protected function getRepositoryClass(): string
    {
        return ProductRepositoryInterface::class;
    }
}
```

### **Bước 5: Đăng ký Repository Binding**

Cập nhật `app/Providers/RepositoryServiceProvider.php`:

```php
// app/Providers/RepositoryServiceProvider.php
namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Repositories\Eloquent\ProductRepository;
use App\Repositories\Interfaces\ProductRepositoryInterface;

class RepositoryServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        // ...
        $this->app->singleton(ProductRepositoryInterface::class, ProductRepository::class);
    }
}
```

### **Bước 6: Tạo Form Requests**

Tạo các request để validate dữ liệu đầu vào.

```bash
docker-compose exec app php artisan make:request Product/StoreProductRequest
docker-compose exec app php artisan make:request Product/UpdateProductRequest
```

**6.1. StoreProductRequest**

```php
// app/Http/Requests/Product/StoreProductRequest.php
namespace App\Http\Requests\Product;

use Illuminate\Foundation\Http\FormRequest;

class StoreProductRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'price' => ['required', 'numeric', 'min:0'],
            'description' => ['nullable', 'string'],
            'status' => ['nullable', 'string', 'in:active,inactive'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Tên sản phẩm là bắt buộc.',
            'price.required' => 'Giá sản phẩm là bắt buộc.',
        ];
    }

    public function bodyParameters(): array
    {
        return [
            'name' => ['description' => 'Tên sản phẩm.', 'example' => 'iPhone 15 Pro Max'],
            'price' => ['description' => 'Giá sản phẩm.', 'example' => 29990000],
        ];
    }
}
```

**6.2. UpdateProductRequest**

```php
// app/Http/Requests/Product/UpdateProductRequest.php
namespace App\Http\Requests\Product;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProductRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'string', 'max:255'],
            'price' => ['sometimes', 'numeric', 'min:0'],
            'description' => ['nullable', 'string'],
            'status' => ['sometimes', 'string', 'in:active,inactive'],
        ];
    }
}
```

### **Bước 7: Tạo API Resource**

Tạo resource để định dạng dữ liệu JSON trả về.

````bash
docker-compose exec app php artisan make:resource Products/ProductResource
```php
// app/Resources/Products/ProductResource.php
namespace App\Resources\Products;

use App\Resources\BaseResource;

class ProductResource extends BaseResource
{
    protected function resourceData($request): array
    {
        return [
            'createdBy' => $this->whenLoaded('createdBy', fn() => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->name,
            ]),
            'updatedBy' => $this->whenLoaded('updatedBy', fn() => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->name,
            ]),
            'is_active' => $this->resource->status === 'active',
        ];
    }
}
````

### **Bước 8: Tạo Controller**

Tạo controller để xử lý request.

````bash
docker-compose exec app php artisan make:controller Api/V1/ProductController
```php
// app/Http/Controllers/Api/V1/ProductController.php
namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\Product\StoreProductRequest;
use App\Http\Requests\Product\UpdateProductRequest;
use App\Resources\Products\ProductResource;
use App\Resources\Products\ProductResourceCollection; // Tạo nếu cần
use App\Services\ProductService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Sản phẩm
 */
class ProductController extends BaseController
{
    public function __construct(protected ProductService $service) {}

    public function index(Request $request): JsonResponse
    {
        $products = $this->service->list($request, ['createdBy', 'updatedBy']);
        return $this->success(new ProductResourceCollection($products));
    }

    public function store(StoreProductRequest $request): JsonResponse
    {
        $product = $this->service->create($request->validated(), ['createdBy']);
        return $this->success(new ProductResource($product), 'Tạo sản phẩm thành công.', 201);
    }

    public function show(int $id): JsonResponse
    {
        $product = $this->service->read($id, ['createdBy', 'updatedBy']);
        return $this->success(new ProductResource($product));
    }

    public function update(UpdateProductRequest $request, int $id): JsonResponse
    {
        $product = $this->service->update($id, $request->validated(), ['updatedBy']);
        return $this->success(new ProductResource($product), 'Cập nhật sản phẩm thành công.');
    }

    public function destroy(int $id): JsonResponse
    {
        $this->service->delete($id);
        return $this->successNoContent('Xóa sản phẩm thành công.');
    }
}
````

### **Bước 9: Đăng ký Route**

Cập nhật file `routes/api.php`:

```php
// routes/api.php
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\ProductController;

Route::middleware('jwt')
    ->prefix('v1/cms')
    ->group(function () {
        // ...
        Route::apiResource('products', ProductController::class);
    });
```

### **Bước 10: Tạo Factory và Seeder (Tùy chọn)**

Tạo dữ liệu mẫu để test.

```bash
docker-compose exec app php artisan make:factory ProductFactory
docker-compose exec app php artisan make:seeder ProductSeeder
```

Chạy seeder:

```bash
docker-compose exec app php artisan db:seed --class=ProductSeeder
```

### **Bước 11: Tạo Tài liệu API với Scribe**

Cuối cùng, tạo tài liệu cho API mới.

```bash
docker-compose exec app php artisan scribe:generate
```

Truy cập `/docs` trên trình duyệt để xem kết quả.
