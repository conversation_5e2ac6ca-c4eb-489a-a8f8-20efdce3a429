<?php

namespace App\Services;

use App\Models\User;
use App\Repositories\Interfaces\UserLogRepositoryInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * Service xử lý các logic nghiệp vụ liên quan đến UserLog.
 */
class UserLogService extends BaseCrudService
{
    protected bool $loggingEnabled = true;

    /**
     * Tạm thởi vô hiệu hóa việc ghi log.
     */
    public function disableLogging(): void
    {
        $this->loggingEnabled = false;
    }

    /**
     * Kích hoạt lại việc ghi log.
     */
    public function enableLogging(): void
    {
        $this->loggingEnabled = true;
    }

    /**
     * Kiểm tra xem việc ghi log có được kích hoạt không.
     */
    public function isLoggingEnabled(): bool
    {
        return $this->loggingEnabled;
    }

    /**
     * Xác định repository class sử dụng cho service này
     *
     * @return string
     */
    protected function getRepositoryClass(): string
    {
        return UserLogRepositoryInterface::class;
    }

    /**
     * Tạm thời vô hiệu hóa việc ghi log để thực thi một hành động.
     * Đảm bảo việc ghi log sẽ được kích hoạt lại sau khi hành động hoàn tất.
     *
     * @param callable $callback Hàm chứa các logic không cần ghi log.
     * @return mixed Kết quả trả về từ hàm callback.
     */
    public function withoutLogging(callable $callback)
    {
        // Ghi lại trạng thái ban đầu
        $initialState = $this->isLoggingEnabled();

        $this->disableLogging();

        try {
            // Thực thi hàm callback
            return $callback();
        } finally {
            // Luôn khôi phục lại trạng thái ban đầu, bất kể có lỗi hay không
            if ($initialState) {
                $this->enableLogging();
            }
        }
    }

    /**
     * Ghi lại một hành động của người dùng.
     *
     * @param string $action Hành động (ví dụ: 'create', 'update', 'delete')
     * @param string|null $description Mô tả chi tiết về hành động
     * @param Request|null $request Đối tượng request chứa thông tin người dùng và request
     * @param Model|null $model Model liên quan đến hành động
     * @param array $dataBefore Dữ liệu trước khi thay đổi (dạng array)
     * @param array $dataAfter Dữ liệu sau khi thay đổi (dạng array)
     * @param array $details Các thông tin chi tiết bổ sung
     * @return void
     */
    public function log(string $action, ?string $description = null, ?Request $request = null, ?Model $model = null, array $dataBefore = [], array $dataAfter = [], array $details = []): void
    {
        if (! $this->isLoggingEnabled()) {
            return;
        }

        // Nếu request không được cung cấp, lấy từ global helper
        if (is_null($request)) {
            $request = request();
        }

        try {
            /** @var User|null $user */
            $user = $request->user();

            $logData = [
                'action' => $action,
                'description' => $description,
                'data_before' => ! empty($dataBefore) ? json_encode($dataBefore, JSON_UNESCAPED_UNICODE) : null,
                'data_after' => ! empty($dataAfter) ? json_encode($dataAfter, JSON_UNESCAPED_UNICODE) : null,
                'details' => ! empty($details) ? json_encode($details, JSON_UNESCAPED_UNICODE) : null,
                'action_time' => now(),
            ];

            if ($user) {
                $logData['actor_id'] = $user->id;
                $logData['actor_type'] = get_class($user);
            }

            if ($model) {
                $logData['model_id'] = $model->getKey();
                $logData['model_type'] = get_class($model);
            }

            if ($request instanceof Request) {
                $logData['url'] = $request->fullUrl();
                $logData['ip'] = $request->ip();
                $logData['user_agent'] = $request->userAgent();
            }

            $this->repository->create($logData);
        } catch (\Throwable $e) {
            // Ghi log lỗi nếu không thể tạo user log, nhưng không dừng chương trình chính.
            Log::error('Failed to create user log.', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
