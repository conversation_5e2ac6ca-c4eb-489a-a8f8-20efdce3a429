<?php

namespace App\Models\Concerns;

use Illuminate\Support\Facades\Auth;

trait HasUserStamps
{
    /**
     * The "booting" method of the trait.
     *
     * @return void
     */
    protected static function bootHasUserStamps()
    {
        static::creating(function ($model) {
            if (Auth::check()) {
                if (is_null($model->created_by)) {
                    $model->created_by = Auth::id();
                }
                if (is_null($model->updated_by)) {
                    $model->updated_by = Auth::id();
                }
            }
        });

        static::updating(function ($model) {
            if (Auth::check()) {
                if (is_null($model->updated_by)) {
                    $model->updated_by = Auth::id();
                }
            }
        });
    }
}
