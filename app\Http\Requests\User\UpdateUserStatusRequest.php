<?php

namespace App\Http\Requests\User;

use App\Enums\UserStatus;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class UpdateUserStatusRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'id' => $this->route('id'),
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => ['required', Rule::exists('users', 'id')],
            'status' => ['required', 'integer', Rule::in(array_column(UserStatus::cases(), 'value'))],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'id.required' => 'ID người dùng là bắt buộc',
            'id.exists' => 'Người dùng không tồn tại',
            'status.required' => 'Trạng thái là bắt buộc',
            'status.integer' => 'Trạng thái phải là số nguyên',
            'status.in' => 'Trạng thái không hợp lệ',
        ];
    }

    /**
     * Cung cấp thông tin mô tả cho các tham số yêu cầu trong API
     * Được sử dụng bởi Scribe để tạo tài liệu API
     *
     * @return array<string, array<string, mixed>>
     */
    public function bodyParameters(): array
    {
        return [
            'status' => [
                'description' => 'Trạng thái mới của người dùng',
                'example' => 1,
                'required' => true,
            ],
        ];
    }
}
