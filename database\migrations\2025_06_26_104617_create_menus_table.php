<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menus', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('Tên định danh cho admin (<PERSON>u đầu trang, chân trang...)');
            $table->string('location_key')->unique()->comment('Key gọi trên frontend (e.g. header, footer)');
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete()->comment('Người tạo danh mục');
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete()->comment('Ngư<PERSON>i cập nhật danh mục');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('menu_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('menu_id')->constrained('menus')->cascadeOnDelete()->comment('Liên kết đến bảng menus');
            $table->string('title')->comment('Tiêu đề menu item');
            $table->string('slug')->unique()->comment('Slug của menu item');
            $table->integer('order')->default(0)->comment('Sắp xếp thứ tự trong menu');
            $table->foreignId('parent_id')->nullable()->constrained('menu_items')->nullOnDelete()->comment('Hỗ trợ menu đa cấp');
            $table->string('target', 20)->default('_self')->comment('Target: _self, _blank');
            $table->unsignedBigInteger('linkable_id')->nullable()->comment('ID của model liên kết (Post, Category)');
            $table->string('linkable_type', 255)->nullable()->comment('Tên model, ví dụ: App\\Models\\Post');
            $table->string('custom_url', 2048)->nullable()->comment('Đường dẫn tuỳ chỉnh nếu không dùng model');
            $table->boolean('status')->default(true)->comment('Trạng thái hiển thị menu item');
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete()->comment('Người tạo danh mục');
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete()->comment('Người cập nhật danh mục');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu_items');
        Schema::dropIfExists('menus');
    }
};
