<?php

namespace App\Enums;

enum OverviewType: int
{
    case REGISTERED_ACCOUNTS = 1;
    case REGISTERED_IPS = 2;
    case TOTAL_REVENUE = 3;
    case TRANSACTION_COUNT = 4;

    public function getLabel(): string
    {
        return match($this) {
            self::REGISTERED_ACCOUNTS => 'Số tài khoản đăng ký',
            self::REGISTERED_IPS => 'Số IP đăng ký',
            self::TOTAL_REVENUE => 'Doanh thu',
            self::TRANSACTION_COUNT => 'Số lượng giao dịch',
        };
    }

    public function getUnit(): string
    {
        return match($this) {
            self::REGISTERED_ACCOUNTS => 'tài khoản',
            self::REGISTERED_IPS => 'IP',
            self::TOTAL_REVENUE => 'VNĐ',
            self::TRANSACTION_COUNT => 'giao dịch',
        };
    }

    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function getTypesWithLabels(): array
    {
        $types = [];
        foreach (self::cases() as $case) {
            $types[$case->value] = $case->getLabel();
        }

        return $types;
    }
}
